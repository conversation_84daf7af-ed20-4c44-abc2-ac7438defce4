#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯 - 桌面版（简化版）
自动化Augment登录和验证码获取工具
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import os
import requests
import re
import random
import string
import webbrowser

class EmailAPI:
    """邮箱API基类"""
    def __init__(self, config):
        self.config = config
        self.email_address = None
    
    def set_email_address(self, email):
        self.email_address = email
    
    def extract_verification_code(self, content):
        """从邮件内容中提取验证码"""
        patterns = [
            r'验证码[：:\s]*([A-Z0-9]{4,8})',
            r'verification code[：:\s]*([A-Z0-9]{4,8})',
            r'code[：:\s]*([A-Z0-9]{4,8})',
            r'([A-Z0-9]{6})',
            r'([0-9]{4,8})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)
        return None

class TempMailPlusAPI(EmailAPI):
    """TempMail.Plus API实现"""
    
    def get_emails(self):
        """获取邮件列表"""
        if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
            raise Exception('TempMail.Plus需要邮箱地址和PIN码')
        
        temp_mail_address = self.config['temp_mail_address']
        username, domain = temp_mail_address.split('@')
        
        # 尝试多个可能的API端点
        endpoints = [
            f'https://tempmail.plus/api/v1/inbox/{username}',
            f'https://tempmail.plus/api/inbox/{username}',
            f'https://tempmail.plus/inbox/{username}',
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, headers={
                    'Content-Type': 'application/json',
                    'X-Pin-Code': self.config['pin_code'],
                    'Authorization': f'PIN {self.config["pin_code"]}',
                }, timeout=10)
                
                if response.ok:
                    data = response.json()
                    emails = data.get('emails', data.get('messages', data.get('inbox', [])))
                    
                    if isinstance(emails, list):
                        results = []
                        for email in emails[:5]:
                            content = email.get('body', email.get('content', email.get('text', '')))
                            code = self.extract_verification_code(content)
                            if code:
                                results.append({
                                    'id': email.get('id'),
                                    'subject': email.get('subject'),
                                    'content': content,
                                    'verification_code': code,
                                    'date': email.get('date', email.get('timestamp'))
                                })
                        return results
            except Exception as e:
                print(f"端点 {endpoint} 失败: {e}")
                continue
        
        raise Exception('无法通过API获取邮件')

class OneSecMailAPI(EmailAPI):
    """1SecMail API实现"""
    
    def get_emails(self):
        if not self.email_address:
            raise Exception('邮箱地址未设置')
        
        login, domain = self.email_address.split('@')
        url = f'https://www.1secmail.com/api/v1/?action=getMessages&login={login}&domain={domain}'
        
        try:
            response = requests.get(url, timeout=10)
            emails = response.json()
            
            if not isinstance(emails, list):
                return []
            
            results = []
            for email in emails[:5]:
                detail_url = f'https://www.1secmail.com/api/v1/?action=readMessage&login={login}&domain={domain}&id={email["id"]}'
                detail_response = requests.get(detail_url, timeout=10)
                detail = detail_response.json()
                
                content = detail.get('textBody', detail.get('body', ''))
                code = self.extract_verification_code(content)
                if code:
                    results.append({
                        'id': email['id'],
                        'subject': email.get('subject', ''),
                        'content': content,
                        'verification_code': code,
                        'date': email.get('date', '')
                    })
            
            return results
        except Exception as e:
            raise Exception(f'1SecMail API错误: {e}')

class AugmentRefillApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment续杯工具 - 简化版")
        self.root.geometry("700x600")
        self.root.resizable(True, True)
        
        # 设置样式
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # 配置数据
        self.config_file = "augment_config.json"
        self.config = self.load_config()
        
        # 创建界面
        self.create_widgets()
        self.load_saved_config()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = tk.Frame(self.root, bg='#2b2b2b')
        title_frame.pack(fill="x", pady=10)
        
        title_label = tk.Label(
            title_frame, 
            text="🚀 Augment续杯工具", 
            font=("Arial", 20, "bold"),
            bg='#2b2b2b',
            fg='white'
        )
        title_label.pack()
        
        # 创建主框架
        main_frame = tk.Frame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 基础配置区域
        self.create_basic_config_section(main_frame)
        
        # 邮箱服务配置区域
        self.create_email_service_section(main_frame)
        
        # 操作按钮区域
        self.create_action_buttons(main_frame)
        
        # 日志显示区域
        self.create_log_section(main_frame)
        
    def create_basic_config_section(self, parent):
        """创建基础配置区域"""
        config_frame = ttk.LabelFrame(parent, text="📧 基础配置", padding=10)
        config_frame.pack(fill="x", pady=5)
        
        # 邮箱后缀
        domain_frame = tk.Frame(config_frame)
        domain_frame.pack(fill="x", pady=5)
        
        tk.Label(domain_frame, text="邮箱后缀:", width=15, anchor='w').pack(side="left")
        self.domain_entry = tk.Entry(domain_frame, width=30)
        self.domain_entry.pack(side="left", padx=10)
        tk.Label(domain_frame, text="例如: example.com", fg='gray').pack(side="left")
        
        # 随机字符串位数
        length_frame = tk.Frame(config_frame)
        length_frame.pack(fill="x", pady=5)
        
        tk.Label(length_frame, text="随机字符串位数:", width=15, anchor='w').pack(side="left")
        self.length_entry = tk.Entry(length_frame, width=10)
        self.length_entry.pack(side="left", padx=10)
        tk.Label(length_frame, text="默认: 12", fg='gray').pack(side="left")
        
    def create_email_service_section(self, parent):
        """创建邮箱服务配置区域"""
        email_frame = ttk.LabelFrame(parent, text="📮 邮箱服务配置", padding=10)
        email_frame.pack(fill="x", pady=5)
        
        # 邮箱服务选择
        service_frame = tk.Frame(email_frame)
        service_frame.pack(fill="x", pady=5)
        
        tk.Label(service_frame, text="邮箱服务:", width=15, anchor='w').pack(side="left")
        self.service_var = tk.StringVar(value="tempmailplus")
        self.service_combo = ttk.Combobox(
            service_frame,
            values=["tempmailplus", "1secmail"],
            textvariable=self.service_var,
            state="readonly",
            width=20
        )
        self.service_combo.pack(side="left", padx=10)
        self.service_combo.bind('<<ComboboxSelected>>', self.on_service_change)
        
        # TempMail.Plus配置
        self.tempmail_frame = ttk.LabelFrame(email_frame, text="TempMail.Plus 配置", padding=5)
        self.tempmail_frame.pack(fill="x", pady=5)
        
        # TempMail.Plus邮箱地址
        tempmail_addr_frame = tk.Frame(self.tempmail_frame)
        tempmail_addr_frame.pack(fill="x", pady=2)
        
        tk.Label(tempmail_addr_frame, text="邮箱地址:", width=12, anchor='w').pack(side="left")
        self.tempmail_entry = tk.Entry(tempmail_addr_frame, width=30)
        self.tempmail_entry.pack(side="left", padx=5)
        tk.Label(tempmail_addr_frame, text="例如: <EMAIL>", fg='gray').pack(side="left")
        
        # PIN码
        pin_frame = tk.Frame(self.tempmail_frame)
        pin_frame.pack(fill="x", pady=2)
        
        tk.Label(pin_frame, text="PIN码:", width=12, anchor='w').pack(side="left")
        self.pin_entry = tk.Entry(pin_frame, show="*", width=20)
        self.pin_entry.pack(side="left", padx=5)
        
    def create_action_buttons(self, parent):
        """创建操作按钮区域"""
        button_frame = tk.Frame(parent)
        button_frame.pack(fill="x", pady=10)
        
        # 按钮容器
        btn_container = tk.Frame(button_frame)
        btn_container.pack()
        
        # 保存配置按钮
        self.save_btn = tk.Button(
            btn_container,
            text="💾 保存配置",
            command=self.save_config,
            width=12,
            bg='#4CAF50',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        self.save_btn.pack(side="left", padx=5)
        
        # 测试邮箱按钮
        self.test_btn = tk.Button(
            btn_container,
            text="📧 测试邮箱",
            command=self.test_email,
            width=12,
            bg='#2196F3',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        self.test_btn.pack(side="left", padx=5)
        
        # 打开登录页面按钮
        self.open_btn = tk.Button(
            btn_container,
            text="🌐 打开登录页",
            command=self.open_login_page,
            width=12,
            bg='#FF9800',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        self.open_btn.pack(side="left", padx=5)
        
        # 生成邮箱按钮
        self.generate_btn = tk.Button(
            btn_container,
            text="📧 生成邮箱",
            command=self.generate_email,
            width=12,
            bg='#9C27B0',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        self.generate_btn.pack(side="left", padx=5)

    def create_log_section(self, parent):
        """创建日志显示区域"""
        log_frame = ttk.LabelFrame(parent, text="📋 运行日志", padding=10)
        log_frame.pack(fill="both", expand=True, pady=5)

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, width=80)
        self.log_text.pack(fill="both", expand=True)

        # 按钮框架
        log_btn_frame = tk.Frame(log_frame)
        log_btn_frame.pack(fill="x", pady=5)

        # 清除日志按钮
        clear_btn = tk.Button(
            log_btn_frame,
            text="🗑️ 清除日志",
            command=self.clear_log,
            width=10
        )
        clear_btn.pack(side="left")

        # 获取验证码按钮
        get_code_btn = tk.Button(
            log_btn_frame,
            text="🔍 获取验证码",
            command=self.get_verification_code_manual,
            width=12,
            bg='#F44336',
            fg='white',
            font=('Arial', 10, 'bold')
        )
        get_code_btn.pack(side="right")

    def on_service_change(self, event=None):
        """邮箱服务选择变化处理"""
        if self.service_var.get() == "tempmailplus":
            self.tempmail_frame.pack(fill="x", pady=5)
        else:
            self.tempmail_frame.pack_forget()

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
        self.root.update()

    def clear_log(self):
        """清除日志"""
        self.log_text.delete("1.0", "end")

    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载配置失败: {e}")
        return {}

    def save_config_to_file(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False

    def load_saved_config(self):
        """加载已保存的配置到界面"""
        if self.config.get('email_domain'):
            self.domain_entry.insert(0, self.config['email_domain'])
        if self.config.get('random_length'):
            self.length_entry.insert(0, str(self.config['random_length']))
        if self.config.get('email_service'):
            self.service_var.set(self.config['email_service'])
            self.on_service_change()
        if self.config.get('temp_mail_address'):
            self.tempmail_entry.insert(0, self.config['temp_mail_address'])
        if self.config.get('pin_code'):
            self.pin_entry.insert(0, self.config['pin_code'])

    def save_config(self):
        """保存配置"""
        try:
            # 验证输入
            domain = self.domain_entry.get().strip()
            if not domain:
                messagebox.showerror("错误", "请输入邮箱后缀")
                return

            if '@' in domain or not '.' in domain:
                messagebox.showerror("错误", "请输入正确的域名格式，如 example.com")
                return

            length = self.length_entry.get().strip()
            if length and (not length.isdigit() or int(length) < 1 or int(length) > 32):
                messagebox.showerror("错误", "随机字符串位数必须是1-32之间的数字")
                return

            service = self.service_var.get()
            if not service:
                messagebox.showerror("错误", "请选择邮箱服务")
                return

            # TempMail.Plus验证
            if service == "tempmailplus":
                temp_mail = self.tempmail_entry.get().strip()
                pin_code = self.pin_entry.get().strip()

                if not temp_mail:
                    messagebox.showerror("错误", "请输入TempMail.Plus邮箱地址")
                    return

                if not pin_code:
                    messagebox.showerror("错误", "请输入PIN码")
                    return

                # 验证邮箱格式
                email_pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
                if not re.match(email_pattern, temp_mail):
                    messagebox.showerror("错误", "请输入有效的邮箱地址格式")
                    return

                self.config['temp_mail_address'] = temp_mail
                self.config['pin_code'] = pin_code

            # 保存配置
            self.config['email_domain'] = domain
            self.config['random_length'] = int(length) if length else 12
            self.config['email_service'] = service

            if self.save_config_to_file():
                messagebox.showinfo("成功", "配置保存成功！")
                self.log_message("✅ 配置保存成功")
            else:
                messagebox.showerror("错误", "配置保存失败")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置时出错: {e}")

    def generate_random_email(self):
        """生成随机邮箱"""
        length = self.config.get('random_length', 12)
        domain = self.config.get('email_domain', '')

        if not domain:
            raise Exception("未设置邮箱域名")

        characters = string.ascii_letters + string.digits
        random_str = ''.join(random.choice(characters) for _ in range(length))
        return f"{random_str}@{domain}"

    def create_email_api(self):
        """创建邮箱API实例"""
        service = self.config.get('email_service', '')

        if service == 'tempmailplus':
            return TempMailPlusAPI(self.config)
        elif service == '1secmail':
            return OneSecMailAPI(self.config)
        else:
            raise Exception(f"不支持的邮箱服务: {service}")

    def test_email(self):
        """测试邮箱连接"""
        def test_thread():
            try:
                self.log_message("🔍 开始测试邮箱连接...")

                # 创建邮箱API
                email_api = self.create_email_api()

                # 生成测试邮箱
                test_email = self.generate_random_email()
                email_api.set_email_address(test_email)

                self.log_message(f"📧 测试邮箱: {test_email}")

                # 测试获取邮件
                emails = email_api.get_emails()

                if emails:
                    self.log_message(f"✅ 邮箱连接成功，找到 {len(emails)} 封邮件")
                    for email in emails[:3]:  # 显示前3封
                        self.log_message(f"  📨 {email.get('subject', '无主题')}")
                else:
                    self.log_message("✅ 邮箱连接成功，暂无邮件")

                messagebox.showinfo("测试成功", "邮箱连接测试成功！")

            except Exception as e:
                error_msg = f"❌ 邮箱测试失败: {e}"
                self.log_message(error_msg)
                messagebox.showerror("测试失败", str(e))

        threading.Thread(target=test_thread, daemon=True).start()

    def open_login_page(self):
        """打开Augment登录页面"""
        url = "https://login.augmentcode.com/u/login/identifier"
        webbrowser.open(url)
        self.log_message("🌐 已在浏览器中打开Augment登录页面")

    def generate_email(self):
        """生成并显示随机邮箱"""
        try:
            if not self.config.get('email_domain'):
                messagebox.showerror("错误", "请先保存配置")
                return

            email = self.generate_random_email()
            self.log_message(f"📧 生成的邮箱: {email}")

            # 复制到剪贴板
            self.root.clipboard_clear()
            self.root.clipboard_append(email)

            messagebox.showinfo("邮箱生成成功", f"邮箱已生成并复制到剪贴板：\n\n{email}")

        except Exception as e:
            messagebox.showerror("错误", f"生成邮箱失败: {e}")

    def get_verification_code_manual(self):
        """手动获取验证码"""
        def get_code_thread():
            try:
                if not self.config.get('email_domain'):
                    messagebox.showerror("错误", "请先保存配置")
                    return

                # 询问用户邮箱地址
                email = tk.simpledialog.askstring(
                    "输入邮箱",
                    "请输入要检查的邮箱地址:",
                    initialvalue=f"example@{self.config.get('email_domain', '')}"
                )

                if not email:
                    return

                self.log_message(f"🔍 开始检查邮箱: {email}")

                # 创建邮箱API
                email_api = self.create_email_api()
                email_api.set_email_address(email)

                # 获取邮件
                emails = email_api.get_emails()

                if emails:
                    self.log_message(f"✅ 找到 {len(emails)} 封包含验证码的邮件:")
                    for email_data in emails:
                        code = email_data.get('verification_code', '')
                        subject = email_data.get('subject', '无主题')
                        self.log_message(f"  📨 {subject} - 验证码: {code}")

                        # 复制第一个验证码到剪贴板
                        if code:
                            self.root.clipboard_clear()
                            self.root.clipboard_append(code)
                            messagebox.showinfo("验证码获取成功", f"验证码已复制到剪贴板：\n\n{code}")
                            break
                else:
                    self.log_message("❌ 未找到包含验证码的邮件")
                    messagebox.showinfo("提示", "未找到包含验证码的邮件")

            except Exception as e:
                error_msg = f"❌ 获取验证码失败: {e}"
                self.log_message(error_msg)
                messagebox.showerror("获取失败", str(e))

        threading.Thread(target=get_code_thread, daemon=True).start()

    def run(self):
        """运行应用"""
        self.log_message("🎉 Augment续杯工具启动成功！")
        self.log_message("💡 使用说明：")
        self.log_message("   1. 配置您的域名和邮箱服务")
        self.log_message("   2. 点击'保存配置'")
        self.log_message("   3. 点击'生成邮箱'获取随机邮箱")
        self.log_message("   4. 点击'打开登录页'在浏览器中登录")
        self.log_message("   5. 点击'获取验证码'自动获取验证码")
        self.log_message("")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("👋 程序退出")

# 导入简单对话框
import tkinter.simpledialog

def main():
    """主函数"""
    try:
        app = AugmentRefillApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        messagebox.showerror("错误", f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
