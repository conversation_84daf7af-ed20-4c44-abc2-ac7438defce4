<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>Augment续杯</title>
  <style>
    body {
      width: 280px;
      padding: 10px;
      font-family: Arial, sans-serif;
      background-color: #f9f9f9;
    }
    h1 {
      font-size: 18px;
      text-align: center;
      margin-bottom: 10px;
      color: #333;
    }
    p {
      font-size: 14px;
      line-height: 1.4;
      color: #555;
    }
    .form-group {
      margin-bottom: 15px;
      position: relative;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      font-size: 14px;
      color: #444;
    }
    input[type="text"], input[type="number"], input[type="password"], select {
      width: 100%;
      padding: 10px;
      box-sizing: border-box;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
      transition: all 0.3s ease;
      background-color: white;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    input[type="text"]:focus, input[type="number"]:focus, input[type="password"]:focus, select:focus {
      border-color: #4285f4;
      outline: none;
      box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
    }
    /* 美化数字输入框 */
    .number-input-container {
      display: flex;
      align-items: center;
      position: relative;
    }
    .number-input-container input[type="number"] {
      -moz-appearance: textfield; /* Firefox */
    }
    .number-input-container input[type="number"]::-webkit-inner-spin-button,
    .number-input-container input[type="number"]::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
    .number-controls {
      position: absolute;
      right: 8px;
      display: flex;
      flex-direction: column;
      height: 70%;
    }
    .number-control-btn {
      background: none;
      border: none;
      color: #666;
      font-size: 12px;
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .number-control-btn:hover {
      color: #4285f4;
    }
    .current-value {
      position: absolute;
      right: 35px;
      top: 50%;
      transform: translateY(-50%);
      color: #888;
      font-size: 12px;
    }
    button {
      background-color: #4285f4;
      color: white;
      border: none;
      padding: 10px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      width: 100%;
      transition: background-color 0.3s ease;
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    button:hover {
      background-color: #3367d6;
    }
    .status {
      margin-top: 10px;
      font-size: 12px;
      color: #4caf50;
      text-align: center;
      height: 15px;
      transition: all 0.3s ease;
    }
    a {
      color: #4285f4;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .input-tip {
      font-size: 12px;
      color: #777;
      margin-top: 4px;
      margin-bottom: 0;
      font-style: italic;
    }
  </style>
</head>
<body>
  <h1>Augment续杯</h1>
  <p>在Augment登录页面添加续杯功能，自动生成随机邮箱并填入表单。</p>

  <div class="form-group">
    <label for="emailDomain">邮箱后缀:</label>
    <input type="text" id="emailDomain" placeholder="例如: example.com">
    <p class="input-tip">请输入你们自己的域名邮箱后缀 如 example.com</p>
  </div>

  <div class="form-group">
    <label for="randomLength">随机字符串位数:</label>
    <div class="number-input-container">
      <input type="number" id="randomLength" min="1" max="32" placeholder="默认: 12">
      <span class="current-value" id="currentValue"></span>
      <div class="number-controls">
        <button type="button" class="number-control-btn" id="increaseBtn">▲</button>
        <button type="button" class="number-control-btn" id="decreaseBtn">▼</button>
      </div>
    </div>
  </div>

  <div class="form-group">
    <label for="emailService">邮箱服务类型:</label>
    <select id="emailService">
      <option value="">选择邮箱服务</option>
      <option value="1secmail">1secmail (免费)</option>
      <option value="guerrillamail">GuerrillaMail (免费)</option>
      <option value="tempmailplus">TempMail.Plus (需要PIN码)</option>
      <option value="tempmail">Temp-Mail (需要API密钥)</option>
      <option value="tenminutemail">10MinuteMail (需要API密钥)</option>
      <option value="mailinator">Mailinator (需要API密钥)</option>
    </select>
    <p class="input-tip">选择您要使用的一次性邮箱服务</p>
  </div>

  <div class="form-group" id="pinCodeGroup" style="display: none;">
    <label for="tempMailAddress">TempMail.Plus邮箱地址:</label>
    <input type="email" id="tempMailAddress" placeholder="输入您在TempMail.Plus的邮箱地址">
    <p class="input-tip">您在TempMail.Plus网站的固定邮箱地址，用于接收转发的验证码</p>
  </div>

  <div class="form-group" id="pinCodeInputGroup" style="display: none;">
    <label for="pinCode">PIN码:</label>
    <input type="password" id="pinCode" placeholder="输入TempMail.Plus的PIN码">
    <p class="input-tip">在TempMail.Plus网站设置的PIN码，用于保护您的邮箱</p>
  </div>

  <div class="form-group" id="apiKeyGroup" style="display: none;">
    <label for="apiKey">API密钥:</label>
    <input type="password" id="apiKey" placeholder="输入API密钥">
    <p class="input-tip">某些邮箱服务需要API密钥才能访问</p>
  </div>

  <button id="saveButton">保存设置</button>
  <div class="status" id="statusMessage"></div>

  <p>请访问 <a href="https://login.augmentcode.com/" target="_blank">Augment登录页面</a> 使用此功能。</p>
  <script src="popup.js"></script>
</body>
</html>
