# 🚀 Augment续杯工具 - 桌面版

一个基于Python的桌面应用程序，用于自动化Augment登录和验证码获取流程。

## ✨ 功能特点

- 🎨 **现代化界面**：基于CustomTkinter的美观深色主题界面
- 🤖 **全自动化**：自动生成邮箱、填入验证码、完成登录
- 📧 **多邮箱支持**：支持TempMail.Plus、1SecMail等邮箱服务
- 🔧 **智能配置**：可保存配置，支持自定义域名和随机字符串长度
- 📋 **实时日志**：详细的操作日志，方便调试和监控
- 🌐 **自动驱动管理**：自动下载和管理ChromeDriver

## 🖼️ 界面预览

软件采用现代化的深色主题设计，界面简洁美观：

- **基础配置区域**：设置邮箱后缀和随机字符串位数
- **邮箱服务配置**：选择邮箱服务并配置相关参数
- **操作按钮区域**：保存配置、开始续杯、停止、测试邮箱
- **实时日志显示**：显示详细的操作过程和状态信息

## 📋 系统要求

- **操作系统**：Windows 10/11
- **Python版本**：Python 3.8 或更高版本
- **浏览器**：Google Chrome（最新版本）
- **网络**：稳定的互联网连接

## 🚀 快速开始

### 方法一：一键安装（推荐）

1. **下载项目文件**到本地目录
2. **双击运行** `install.bat` 进行自动安装
3. **安装完成后**，双击 `run.bat` 启动程序

### 方法二：手动安装

1. **安装Python依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **运行程序**：
   ```bash
   python augment_refill_app.py
   ```

## ⚙️ 配置说明

### 基础配置

- **邮箱后缀**：您自己的域名（如：`example.com`）
- **随机字符串位数**：生成邮箱的随机部分长度（默认12位）

### 邮箱服务配置

#### TempMail.Plus（推荐）
- **邮箱地址**：您在TempMail.Plus创建的邮箱（如：`<EMAIL>`）
- **PIN码**：在TempMail.Plus网站设置的PIN码

#### 1SecMail
- 无需额外配置，直接使用

## 🔄 工作流程

1. **生成随机邮箱**：`随机12位字符@您的域名.com`
2. **Cloudflare转发**：您的域名收到邮件后转发到TempMail.Plus邮箱
3. **自动获取验证码**：程序用PIN码访问TempMail.Plus获取验证码
4. **自动填入提交**：验证码自动填入并提交完成登录

## 📖 使用步骤

1. **启动程序**：双击 `run.bat` 或运行Python文件
2. **配置参数**：
   - 输入您的邮箱后缀
   - 选择邮箱服务（推荐TempMail.Plus）
   - 输入TempMail.Plus邮箱地址和PIN码
3. **保存配置**：点击"💾 保存配置"按钮
4. **测试连接**：点击"📧 测试邮箱"验证配置（可选）
5. **开始续杯**：点击"🚀 开始续杯"启动自动化流程
6. **监控日志**：观察日志区域了解执行状态

## 🛠️ 故障排除

### 常见问题

1. **浏览器启动失败**
   - 确保已安装Google Chrome浏览器
   - 程序会自动下载ChromeDriver，请确保网络连接正常

2. **邮箱连接失败**
   - 检查TempMail.Plus邮箱地址和PIN码是否正确
   - 确认网络连接正常
   - 尝试手动访问TempMail.Plus网站验证

3. **验证码获取超时**
   - 检查Cloudflare邮件转发配置
   - 确认邮箱服务正常工作
   - 可以手动输入验证码作为备选方案

### 日志分析

程序提供详细的日志信息：
- ✅ 绿色勾号：操作成功
- ❌ 红色叉号：操作失败
- ⏳ 沙漏：等待中
- 🔍 放大镜：查找/检测中

## 📄 文件说明

- `augment_refill_app.py`：主程序文件
- `requirements.txt`：Python依赖列表
- `install.bat`：自动安装脚本
- `run.bat`：程序启动脚本
- `augment_config.json`：配置文件（运行后自动生成）

## ⚠️ 注意事项

1. **合法使用**：请确保在合法合规的前提下使用本工具
2. **域名配置**：请使用您自己控制的域名
3. **网络安全**：建议在安全的网络环境下使用
4. **数据保护**：配置文件包含敏感信息，请妥善保管

---

**Augment续杯工具** - 让登录更简单 🎉
