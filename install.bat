@echo off
echo ========================================
echo     Augment续杯工具 - 安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境
    echo 请先安装Python 3.8或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装依赖包...
pip install -r requirements.txt

if errorlevel 1 (
    echo.
    echo 依赖安装失败，尝试使用国内镜像源...
    pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt
)

echo.
echo 正在下载ChromeDriver...
python -c "from webdriver_manager.chrome import ChromeDriverManager; ChromeDriverManager().install()"

echo.
echo ========================================
echo 安装完成！
echo.
echo 使用方法：
echo 1. 双击运行 run.bat
echo 2. 或者在命令行中运行：python augment_refill_app.py
echo ========================================
pause
