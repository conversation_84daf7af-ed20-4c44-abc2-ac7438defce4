#!/usr/bin/env python3
"""
Augment自动注册工具 - SeleniumBase版本
专门用于处理Cloudflare Turnstile验证
"""

import string
import random
import time

try:
    from seleniumbase import SB
    SELENIUMBASE_AVAILABLE = True
    print("✅ SeleniumBase已安装")
except ImportError:
    SELENIUMBASE_AVAILABLE = False
    print("❌ SeleniumBase未安装，请运行 install_seleniumbase.bat")
    input("按回车键退出...")
    exit(1)

def generate_email(domain="gmail.com"):
    """生成随机邮箱"""
    chars = string.ascii_letters + string.digits
    random_str = ''.join(random.choice(chars) for _ in range(12))
    return f"{random_str}@{domain}"

def augment_auto_register():
    """使用SeleniumBase自动注册Augment账号"""
    print("🚀 启动Augment自动注册工具 (SeleniumBase版本)")
    print("=" * 50)
    
    # 生成邮箱
    email = generate_email()
    print(f"📧 生成邮箱: {email}")
    
    try:
        # 使用SeleniumBase UC Mode
        print("🤖 启动SeleniumBase UC Mode...")
        with SB(uc=True, test=True) as sb:
            # 步骤1: 打开Augment网站
            print("🌐 正在打开Augment网站...")
            sb.uc_open_with_reconnect("https://www.augmentcode.com/", reconnect_time=3)
            print("✅ 网站已打开")
            
            # 步骤2: 查找并点击Sign In按钮
            print("🔍 正在查找Sign In按钮...")
            sign_in_selectors = [
                "a[href='https://app.augmentcode.com']",
                "a:contains('Sign in')",
                "a:contains('Sign In')"
            ]
            
            sign_in_clicked = False
            for selector in sign_in_selectors:
                try:
                    if sb.is_element_visible(selector):
                        print(f"✅ 找到Sign In按钮: {selector}")
                        sb.uc_click(selector, reconnect_time=2)
                        print("🎯 已点击Sign In按钮")
                        sign_in_clicked = True
                        break
                except Exception as e:
                    print(f"⚠️ 选择器失败: {selector} - {e}")
                    continue
            
            if not sign_in_clicked:
                print("❌ 未找到Sign In按钮")
                return False
            
            # 步骤3: 等待登录页面加载
            print("⏳ 等待登录页面加载...")
            sb.sleep(3)
            
            # 步骤4: 输入邮箱
            print("📧 正在输入邮箱...")
            email_selectors = [
                "input[name='username']",
                "input[id='username']",
                "input[inputmode='email']"
            ]
            
            email_filled = False
            for selector in email_selectors:
                try:
                    if sb.is_element_visible(selector):
                        sb.type(selector, email)
                        print(f"✅ 已输入邮箱: {email}")
                        email_filled = True
                        break
                except Exception as e:
                    print(f"⚠️ 邮箱输入失败: {selector} - {e}")
                    continue
            
            if not email_filled:
                print("❌ 未找到邮箱输入框")
                return False
            
            # 步骤5: 等待Cloudflare Turnstile加载
            print("🤖 等待Cloudflare Turnstile加载...")
            print("⏳ 等待8秒确保验证组件完全加载...")
            sb.sleep(8)
            
            # 步骤6: 使用SeleniumBase专用方法处理Cloudflare Turnstile
            print("🎯 正在处理Cloudflare Turnstile验证...")
            try:
                # 方法1: 尝试自动处理验证
                print("🔧 尝试方法1: uc_gui_handle_captcha()")
                sb.uc_gui_handle_captcha()
                print("✅ 自动处理验证成功！")
                
            except Exception as e1:
                print(f"⚠️ 自动处理失败: {e1}")
                try:
                    # 方法2: 尝试手动点击
                    print("🔧 尝试方法2: uc_gui_click_captcha()")
                    sb.uc_gui_click_captcha()
                    print("✅ 手动点击验证成功！")
                    
                except Exception as e2:
                    print(f"❌ 手动点击也失败: {e2}")
                    print("⚠️ 验证处理失败，需要手动完成")
                    return False
            
            # 成功消息
            print("\n" + "=" * 50)
            print("🎉 SeleniumBase自动化成功！")
            print("=" * 50)
            print(f"✅ 已自动打开Augment网站")
            print(f"✅ 已自动点击Sign In按钮")
            print(f"✅ 已自动输入邮箱: {email}")
            print(f"✅ 已自动处理Cloudflare Turnstile验证")
            print("\n📋 接下来请手动完成:")
            print("1. 等待验证完全通过")
            print("2. 点击继续/登录按钮")
            print("3. 完成注册流程")
            print("4. 使用邮箱获取验证码")
            print("\n🌐 浏览器将保持打开状态供您操作")
            print("=" * 50)
            
            # 保持浏览器打开
            input("\n按回车键关闭浏览器...")
            return True
            
    except Exception as e:
        print(f"❌ 自动化失败: {e}")
        return False

def main():
    """主函数"""
    if not SELENIUMBASE_AVAILABLE:
        return
    
    print("Augment自动注册工具 - SeleniumBase版本")
    print("专门用于处理Cloudflare Turnstile验证")
    print()
    
    while True:
        print("\n请选择操作:")
        print("1. 🚀 开始自动注册")
        print("2. ❌ 退出程序")
        
        choice = input("\n请输入选择 (1-2): ").strip()
        
        if choice == "1":
            success = augment_auto_register()
            if success:
                print("\n✅ 自动化完成！")
            else:
                print("\n❌ 自动化失败，请检查网络连接或手动完成")
                
        elif choice == "2":
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
