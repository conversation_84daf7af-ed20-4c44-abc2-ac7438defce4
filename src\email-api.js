// 邮箱API模块 - 支持多种一次性邮箱服务

class EmailAPI {
  constructor(config) {
    this.config = config;
    this.emailAddress = null;
  }

  // 根据配置创建邮箱API实例
  static create(config) {
    switch (config.emailService) {
      case '1secmail':
        return new OneSecMailAPI(config);
      case 'guerrillamail':
        return new GuerrillaMailAPI(config);
      case 'tempmail':
        return new TempMailAPI(config);
      case 'tenminutemail':
        return new TenMinuteMailAPI(config);
      case 'mailinator':
        return new MailinatorAPI(config);
      default:
        throw new Error('不支持的邮箱服务类型');
    }
  }

  // 抽象方法 - 子类必须实现
  async getEmails() {
    throw new Error('子类必须实现 getEmails 方法');
  }

  // 从邮件内容中提取验证码
  extractVerificationCode(emailContent) {
    // 常见的验证码模式
    const patterns = [
      /验证码[：:\s]*([A-Z0-9]{4,8})/i,
      /verification code[：:\s]*([A-Z0-9]{4,8})/i,
      /code[：:\s]*([A-Z0-9]{4,8})/i,
      /([A-Z0-9]{6})/g, // 6位数字字母组合
      /([0-9]{4,8})/g   // 4-8位数字
    ];

    for (const pattern of patterns) {
      const match = emailContent.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return null;
  }

  // 设置邮箱地址
  setEmailAddress(email) {
    this.emailAddress = email;
  }
}

// 1SecMail API实现
class OneSecMailAPI extends EmailAPI {
  async getEmails() {
    if (!this.emailAddress) {
      throw new Error('邮箱地址未设置');
    }

    const [login, domain] = this.emailAddress.split('@');
    const url = `https://www.1secmail.com/api/v1/?action=getMessages&login=${login}&domain=${domain}`;

    try {
      const response = await fetch(url);
      const emails = await response.json();
      
      if (!Array.isArray(emails)) {
        return [];
      }

      // 获取最新邮件的详细内容
      const results = [];
      for (const email of emails.slice(0, 5)) { // 只检查最新的5封邮件
        const detailUrl = `https://www.1secmail.com/api/v1/?action=readMessage&login=${login}&domain=${domain}&id=${email.id}`;
        const detailResponse = await fetch(detailUrl);
        const detail = await detailResponse.json();
        
        if (detail.body || detail.textBody) {
          const content = detail.textBody || detail.body;
          const code = this.extractVerificationCode(content);
          if (code) {
            results.push({
              id: email.id,
              subject: email.subject,
              content: content,
              verificationCode: code,
              date: email.date
            });
          }
        }
      }
      
      return results;
    } catch (error) {
      console.error('1SecMail API错误:', error);
      throw error;
    }
  }
}

// GuerrillaMail API实现
class GuerrillaMailAPI extends EmailAPI {
  constructor(config) {
    super(config);
    this.sessionId = null;
  }

  async getEmails() {
    try {
      // 如果没有session，先创建一个
      if (!this.sessionId) {
        const sessionResponse = await fetch('https://api.guerrillamail.com/ajax.php?f=get_email_address');
        const sessionData = await sessionResponse.json();
        this.sessionId = sessionData.sid_token;
      }

      // 获取邮件列表
      const url = `https://api.guerrillamail.com/ajax.php?f=get_email_list&offset=0&sid_token=${this.sessionId}`;
      const response = await fetch(url);
      const data = await response.json();

      if (!data.list || !Array.isArray(data.list)) {
        return [];
      }

      // 获取邮件详细内容
      const results = [];
      for (const email of data.list.slice(0, 5)) {
        const detailUrl = `https://api.guerrillamail.com/ajax.php?f=fetch_email&email_id=${email.mail_id}&sid_token=${this.sessionId}`;
        const detailResponse = await fetch(detailUrl);
        const detail = await detailResponse.json();
        
        if (detail.mail_body) {
          const code = this.extractVerificationCode(detail.mail_body);
          if (code) {
            results.push({
              id: email.mail_id,
              subject: email.mail_subject,
              content: detail.mail_body,
              verificationCode: code,
              date: email.mail_timestamp
            });
          }
        }
      }

      return results;
    } catch (error) {
      console.error('GuerrillaMail API错误:', error);
      throw error;
    }
  }
}

// Temp-Mail API实现 (需要API密钥)
class TempMailAPI extends EmailAPI {
  async getEmails() {
    if (!this.config.apiKey) {
      throw new Error('Temp-Mail需要API密钥');
    }

    if (!this.emailAddress) {
      throw new Error('邮箱地址未设置');
    }

    try {
      const url = `https://api.temp-mail.org/request/mail/id/${this.emailAddress}/format/json/`;
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`
        }
      });

      const emails = await response.json();
      
      if (!Array.isArray(emails)) {
        return [];
      }

      const results = [];
      for (const email of emails.slice(0, 5)) {
        const code = this.extractVerificationCode(email.mail_text || email.mail_html);
        if (code) {
          results.push({
            id: email.mail_id,
            subject: email.mail_subject,
            content: email.mail_text || email.mail_html,
            verificationCode: code,
            date: email.mail_timestamp
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Temp-Mail API错误:', error);
      throw error;
    }
  }
}

// 10MinuteMail API实现 (需要API密钥)
class TenMinuteMailAPI extends EmailAPI {
  async getEmails() {
    if (!this.config.apiKey) {
      throw new Error('10MinuteMail需要API密钥');
    }

    // 10MinuteMail的具体API实现
    // 注意：这里需要根据实际的API文档来实现
    throw new Error('10MinuteMail API暂未实现，请使用其他邮箱服务');
  }
}

// Mailinator API实现 (需要API密钥)
class MailinatorAPI extends EmailAPI {
  async getEmails() {
    if (!this.config.apiKey) {
      throw new Error('Mailinator需要API密钥');
    }

    if (!this.emailAddress) {
      throw new Error('邮箱地址未设置');
    }

    try {
      const [inbox] = this.emailAddress.split('@');
      const url = `https://api.mailinator.com/api/inbox?to=${inbox}`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': this.config.apiKey
        }
      });

      const data = await response.json();
      
      if (!data.messages || !Array.isArray(data.messages)) {
        return [];
      }

      const results = [];
      for (const message of data.messages.slice(0, 5)) {
        // 获取邮件详细内容
        const detailUrl = `https://api.mailinator.com/api/email?id=${message.id}`;
        const detailResponse = await fetch(detailUrl, {
          headers: {
            'Authorization': this.config.apiKey
          }
        });
        const detail = await detailResponse.json();
        
        if (detail.data && detail.data.parts) {
          const textPart = detail.data.parts.find(part => part.headers && part.headers['content-type'] && part.headers['content-type'].includes('text'));
          if (textPart && textPart.body) {
            const code = this.extractVerificationCode(textPart.body);
            if (code) {
              results.push({
                id: message.id,
                subject: message.subject,
                content: textPart.body,
                verificationCode: code,
                date: message.time
              });
            }
          }
        }
      }

      return results;
    } catch (error) {
      console.error('Mailinator API错误:', error);
      throw error;
    }
  }
}

// 导出EmailAPI类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EmailAPI;
} else {
  window.EmailAPI = EmailAPI;
}
