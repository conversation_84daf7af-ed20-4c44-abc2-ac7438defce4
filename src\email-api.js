// 邮箱API模块 - 支持多种一次性邮箱服务

class EmailAPI {
  constructor(config) {
    this.config = config;
    this.emailAddress = null;
  }

  // 根据配置创建邮箱API实例
  static create(config) {
    switch (config.emailService) {
      case '1secmail':
        return new OneSecMailAPI(config);
      case 'guerrillamail':
        return new GuerrillaMailAPI(config);
      case 'tempmail':
        return new TempMailAPI(config);
      case 'tempmailplus':
        return new TempMailPlusAPI(config);
      case 'tenminutemail':
        return new TenMinuteMailAPI(config);
      case 'mailinator':
        return new MailinatorAPI(config);
      default:
        throw new Error('不支持的邮箱服务类型');
    }
  }

  // 抽象方法 - 子类必须实现
  async getEmails() {
    throw new Error('子类必须实现 getEmails 方法');
  }

  // 从邮件内容中提取验证码
  extractVerificationCode(emailContent) {
    // 常见的验证码模式
    const patterns = [
      /验证码[：:\s]*([A-Z0-9]{4,8})/i,
      /verification code[：:\s]*([A-Z0-9]{4,8})/i,
      /code[：:\s]*([A-Z0-9]{4,8})/i,
      /([A-Z0-9]{6})/g, // 6位数字字母组合
      /([0-9]{4,8})/g   // 4-8位数字
    ];

    for (const pattern of patterns) {
      const match = emailContent.match(pattern);
      if (match) {
        return match[1];
      }
    }
    return null;
  }

  // 设置邮箱地址
  setEmailAddress(email) {
    this.emailAddress = email;
  }
}

// 1SecMail API实现
class OneSecMailAPI extends EmailAPI {
  async getEmails() {
    if (!this.emailAddress) {
      throw new Error('邮箱地址未设置');
    }

    const [login, domain] = this.emailAddress.split('@');
    const url = `https://www.1secmail.com/api/v1/?action=getMessages&login=${login}&domain=${domain}`;

    try {
      const response = await fetch(url);
      const emails = await response.json();
      
      if (!Array.isArray(emails)) {
        return [];
      }

      // 获取最新邮件的详细内容
      const results = [];
      for (const email of emails.slice(0, 5)) { // 只检查最新的5封邮件
        const detailUrl = `https://www.1secmail.com/api/v1/?action=readMessage&login=${login}&domain=${domain}&id=${email.id}`;
        const detailResponse = await fetch(detailUrl);
        const detail = await detailResponse.json();
        
        if (detail.body || detail.textBody) {
          const content = detail.textBody || detail.body;
          const code = this.extractVerificationCode(content);
          if (code) {
            results.push({
              id: email.id,
              subject: email.subject,
              content: content,
              verificationCode: code,
              date: email.date
            });
          }
        }
      }
      
      return results;
    } catch (error) {
      console.error('1SecMail API错误:', error);
      throw error;
    }
  }
}

// GuerrillaMail API实现
class GuerrillaMailAPI extends EmailAPI {
  constructor(config) {
    super(config);
    this.sessionId = null;
  }

  async getEmails() {
    try {
      // 如果没有session，先创建一个
      if (!this.sessionId) {
        const sessionResponse = await fetch('https://api.guerrillamail.com/ajax.php?f=get_email_address');
        const sessionData = await sessionResponse.json();
        this.sessionId = sessionData.sid_token;
      }

      // 获取邮件列表
      const url = `https://api.guerrillamail.com/ajax.php?f=get_email_list&offset=0&sid_token=${this.sessionId}`;
      const response = await fetch(url);
      const data = await response.json();

      if (!data.list || !Array.isArray(data.list)) {
        return [];
      }

      // 获取邮件详细内容
      const results = [];
      for (const email of data.list.slice(0, 5)) {
        const detailUrl = `https://api.guerrillamail.com/ajax.php?f=fetch_email&email_id=${email.mail_id}&sid_token=${this.sessionId}`;
        const detailResponse = await fetch(detailUrl);
        const detail = await detailResponse.json();
        
        if (detail.mail_body) {
          const code = this.extractVerificationCode(detail.mail_body);
          if (code) {
            results.push({
              id: email.mail_id,
              subject: email.mail_subject,
              content: detail.mail_body,
              verificationCode: code,
              date: email.mail_timestamp
            });
          }
        }
      }

      return results;
    } catch (error) {
      console.error('GuerrillaMail API错误:', error);
      throw error;
    }
  }
}

// Temp-Mail API实现 (需要API密钥)
class TempMailAPI extends EmailAPI {
  async getEmails() {
    if (!this.config.apiKey) {
      throw new Error('Temp-Mail需要API密钥');
    }

    if (!this.emailAddress) {
      throw new Error('邮箱地址未设置');
    }

    try {
      const url = `https://api.temp-mail.org/request/mail/id/${this.emailAddress}/format/json/`;
      const response = await fetch(url, {
        headers: {
          'Authorization': `Bearer ${this.config.apiKey}`
        }
      });

      const emails = await response.json();

      if (!Array.isArray(emails)) {
        return [];
      }

      const results = [];
      for (const email of emails.slice(0, 5)) {
        const code = this.extractVerificationCode(email.mail_text || email.mail_html);
        if (code) {
          results.push({
            id: email.mail_id,
            subject: email.mail_subject,
            content: email.mail_text || email.mail_html,
            verificationCode: code,
            date: email.mail_timestamp
          });
        }
      }

      return results;
    } catch (error) {
      console.error('Temp-Mail API错误:', error);
      throw error;
    }
  }
}

// TempMail.Plus API实现 (需要邮箱地址和PIN码)
class TempMailPlusAPI extends EmailAPI {
  async getEmails() {
    if (!this.config.pinCode) {
      throw new Error('TempMail.Plus需要PIN码');
    }

    if (!this.emailAddress) {
      throw new Error('邮箱地址未设置');
    }

    try {
      // 由于 TempMail.Plus 没有公开的API，我们需要模拟浏览器行为
      // 或者提供用户友好的提示

      const [username, domain] = this.emailAddress.split('@');

      console.log('TempMail.Plus: 正在尝试获取邮件...');
      console.log('TempMail.Plus: 邮箱地址:', this.emailAddress);
      console.log('TempMail.Plus: PIN码:', this.config.pinCode);

      // 尝试通过可能的API端点获取邮件
      // 注意：这些端点可能需要根据实际情况调整
      const possibleEndpoints = [
        `https://tempmail.plus/api/v1/inbox/${username}`,
        `https://tempmail.plus/api/inbox/${username}`,
        `https://tempmail.plus/inbox/${username}`,
      ];

      for (const endpoint of possibleEndpoints) {
        try {
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'X-Pin-Code': this.config.pinCode,
              'Authorization': `PIN ${this.config.pinCode}`,
            }
          });

          if (response.ok) {
            const data = await response.json();
            console.log('TempMail.Plus: 成功获取数据:', data);

            // 尝试解析邮件数据
            const emails = data.emails || data.messages || data.inbox || [];

            if (Array.isArray(emails)) {
              const results = [];
              for (const email of emails.slice(0, 5)) {
                const content = email.body || email.content || email.text || email.html || '';
                const code = this.extractVerificationCode(content);
                if (code) {
                  results.push({
                    id: email.id || email._id,
                    subject: email.subject || email.title,
                    content: content,
                    verificationCode: code,
                    date: email.date || email.timestamp || email.created_at
                  });
                }
              }

              if (results.length > 0) {
                return results;
              }
            }
          }
        } catch (endpointError) {
          console.log(`TempMail.Plus: 端点 ${endpoint} 失败:`, endpointError.message);
          continue;
        }
      }

      // 如果所有API尝试都失败，提供用户指导
      throw new Error('无法通过API获取邮件，请手动检查');

    } catch (error) {
      console.error('TempMail.Plus API错误:', error);

      // 提供用户友好的错误处理
      return await this.handleManualCheck();
    }
  }

  // 手动检查处理
  async handleManualCheck() {
    const [username, domain] = this.emailAddress.split('@');
    const webUrl = `https://tempmail.plus/zh/#${username}@${domain}`;

    console.log('TempMail.Plus: 自动获取失败，请手动检查');
    console.log('TempMail.Plus: 网页地址:', webUrl);
    console.log('TempMail.Plus: 邮箱地址:', this.emailAddress);
    console.log('TempMail.Plus: PIN码:', this.config.pinCode);

    // 显示用户提示
    if (typeof window !== 'undefined') {
      const message = `TempMail.Plus自动获取失败\n\n请手动操作：\n1. 打开 ${webUrl}\n2. 输入PIN码: ${this.config.pinCode}\n3. 查看验证码并手动输入\n\n邮箱: ${this.emailAddress}`;

      // 创建一个更友好的提示框
      this.showUserGuidance(message, webUrl);
    }

    // 返回空数组，让用户手动处理
    return [];
  }

  // 显示用户指导
  showUserGuidance(message, webUrl) {
    // 创建一个浮动提示框
    const guidanceDiv = document.createElement('div');
    guidanceDiv.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      border: 2px solid #4285f4;
      border-radius: 10px;
      padding: 20px;
      z-index: 10001;
      max-width: 400px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.3);
      font-family: Arial, sans-serif;
    `;

    guidanceDiv.innerHTML = `
      <h3 style="margin-top: 0; color: #4285f4;">TempMail.Plus 手动操作指南</h3>
      <p>自动获取验证码失败，请按以下步骤手动操作：</p>
      <ol>
        <li>点击下方按钮打开邮箱页面</li>
        <li>输入PIN码: <strong>${this.config.pinCode}</strong></li>
        <li>查看收到的验证码</li>
        <li>返回此页面手动输入验证码</li>
      </ol>
      <p><strong>邮箱地址:</strong> ${this.emailAddress}</p>
      <div style="text-align: center; margin-top: 15px;">
        <button id="openTempMail" style="background: #4285f4; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;">打开邮箱</button>
        <button id="closeGuidance" style="background: #ccc; color: black; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">关闭</button>
      </div>
    `;

    document.body.appendChild(guidanceDiv);

    // 添加事件监听器
    document.getElementById('openTempMail').addEventListener('click', () => {
      window.open(webUrl, '_blank');
    });

    document.getElementById('closeGuidance').addEventListener('click', () => {
      guidanceDiv.remove();
    });

    // 10秒后自动关闭
    setTimeout(() => {
      if (guidanceDiv.parentNode) {
        guidanceDiv.remove();
      }
    }, 30000);
  }
}

// 10MinuteMail API实现 (需要API密钥)
class TenMinuteMailAPI extends EmailAPI {
  async getEmails() {
    if (!this.config.apiKey) {
      throw new Error('10MinuteMail需要API密钥');
    }

    // 10MinuteMail的具体API实现
    // 注意：这里需要根据实际的API文档来实现
    throw new Error('10MinuteMail API暂未实现，请使用其他邮箱服务');
  }
}

// Mailinator API实现 (需要API密钥)
class MailinatorAPI extends EmailAPI {
  async getEmails() {
    if (!this.config.apiKey) {
      throw new Error('Mailinator需要API密钥');
    }

    if (!this.emailAddress) {
      throw new Error('邮箱地址未设置');
    }

    try {
      const [inbox] = this.emailAddress.split('@');
      const url = `https://api.mailinator.com/api/inbox?to=${inbox}`;
      
      const response = await fetch(url, {
        headers: {
          'Authorization': this.config.apiKey
        }
      });

      const data = await response.json();
      
      if (!data.messages || !Array.isArray(data.messages)) {
        return [];
      }

      const results = [];
      for (const message of data.messages.slice(0, 5)) {
        // 获取邮件详细内容
        const detailUrl = `https://api.mailinator.com/api/email?id=${message.id}`;
        const detailResponse = await fetch(detailUrl, {
          headers: {
            'Authorization': this.config.apiKey
          }
        });
        const detail = await detailResponse.json();
        
        if (detail.data && detail.data.parts) {
          const textPart = detail.data.parts.find(part => part.headers && part.headers['content-type'] && part.headers['content-type'].includes('text'));
          if (textPart && textPart.body) {
            const code = this.extractVerificationCode(textPart.body);
            if (code) {
              results.push({
                id: message.id,
                subject: message.subject,
                content: textPart.body,
                verificationCode: code,
                date: message.time
              });
            }
          }
        }
      }

      return results;
    } catch (error) {
      console.error('Mailinator API错误:', error);
      throw error;
    }
  }
}

// 导出EmailAPI类
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EmailAPI;
} else {
  window.EmailAPI = EmailAPI;
}
