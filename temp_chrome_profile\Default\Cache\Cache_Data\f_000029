{"version": 3, "file": "recorder.js", "sources": ["../../../node_modules/.pnpm/@rrweb+record@2.0.0-alpha.17_patch_hash=46frarym7rupyfspsu67b3afhe/node_modules/@rrweb/record/dist/record.js", "../../../node_modules/.pnpm/@rrweb+rrweb-plugin-console-record@2.0.0-alpha.17_patch_hash=ytsspyi7p3hvqcq64vqb7wb6bu_rrweb@2.0.0-alpha.17/node_modules/@rrweb/rrweb-plugin-console-record/dist/rrweb-plugin-console-record.js", "../src/utils/globals.ts", "../src/utils/type-utils.ts", "../src/utils/logger.ts", "../src/utils/index.ts", "../src/utils/request-utils.ts", "../src/extensions/replay/rrweb-plugins/patch.ts", "../src/extensions/replay/external/denylist.ts", "../src/extensions/replay/config.ts", "../src/extensions/replay/external/network-plugin.ts", "../src/entrypoints/recorder.ts"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\nvar _a;\nvar __defProp$1 = Object.defineProperty;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField$1 = (obj, key, value) => __defNormalProp$1(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\nvar NodeType$2 = /* @__PURE__ */ ((NodeType2) => {\n  NodeType2[NodeType2[\"Document\"] = 0] = \"Document\";\n  NodeType2[NodeType2[\"DocumentType\"] = 1] = \"DocumentType\";\n  NodeType2[NodeType2[\"Element\"] = 2] = \"Element\";\n  NodeType2[NodeType2[\"Text\"] = 3] = \"Text\";\n  NodeType2[NodeType2[\"CDATA\"] = 4] = \"CDATA\";\n  NodeType2[NodeType2[\"Comment\"] = 5] = \"Comment\";\n  return NodeType2;\n})(NodeType$2 || {});\nconst testableAccessors$1 = {\n  Node: [\"childNodes\", \"parentNode\", \"parentElement\", \"textContent\"],\n  ShadowRoot: [\"host\", \"styleSheets\"],\n  Element: [\"shadowRoot\", \"querySelector\", \"querySelectorAll\"],\n  MutationObserver: []\n};\nconst testableMethods$1 = {\n  Node: [\"contains\", \"getRootNode\"],\n  ShadowRoot: [\"getSelection\"],\n  Element: [],\n  MutationObserver: [\"constructor\"]\n};\n\n/*\nAngular zone patches many things and can pass the untainted checks below, causing performance issues\nAngular zone, puts the unpatched originals on the window, and the names for hose on the zone object.\nSo, we get the unpatched versions from the window object if they exist.\nYou can rename Zone, but this is a good enough proxy to avoid going to an iframe to get the untainted versions.\nsee: https://github.com/angular/angular/issues/26948\n*/\nfunction angularZoneUnpatchedAlternative(key) {\n  const angularUnpatchedVersionSymbol = (\n    globalThis\n  )?.Zone?.__symbol__?.(key);\n  if (\n    angularUnpatchedVersionSymbol &&\n    (globalThis)[angularUnpatchedVersionSymbol]\n  ) {\n    return (globalThis)[\n      angularUnpatchedVersionSymbol\n    ];\n  } else {\n    return undefined;\n  }\n}\n\nconst untaintedBasePrototype$1 = {};\nfunction getUntaintedPrototype$1(key) {\n  if (untaintedBasePrototype$1[key])\n    return untaintedBasePrototype$1[key];\n  const defaultObj = angularZoneUnpatchedAlternative(key) || globalThis[key];\n  const defaultPrototype = defaultObj.prototype;\n  const accessorNames = key in testableAccessors$1 ? testableAccessors$1[key] : void 0;\n  const isUntaintedAccessors = Boolean(\n    accessorNames && // @ts-expect-error 2345\n    accessorNames.every(\n      (accessor) => {\n        var _a2, _b;\n        return Boolean(\n          (_b = (_a2 = Object.getOwnPropertyDescriptor(defaultPrototype, accessor)) == null ? void 0 : _a2.get) == null ? void 0 : _b.toString().includes(\"[native code]\")\n        );\n      }\n    )\n  );\n  const methodNames = key in testableMethods$1 ? testableMethods$1[key] : void 0;\n  const isUntaintedMethods = Boolean(\n    methodNames && methodNames.every(\n      // @ts-expect-error 2345\n      (method) => {\n        var _a2;\n        return typeof defaultPrototype[method] === \"function\" && ((_a2 = defaultPrototype[method]) == null ? void 0 : _a2.toString().includes(\"[native code]\"));\n      }\n    )\n  );\n  if (isUntaintedAccessors && isUntaintedMethods) {\n    untaintedBasePrototype$1[key] = defaultObj.prototype;\n    return defaultObj.prototype;\n  }\n  try {\n    const iframeEl = document.createElement(\"iframe\");\n    document.body.appendChild(iframeEl);\n    const win = iframeEl.contentWindow;\n    if (!win) return defaultObj.prototype;\n    const untaintedObject = win[key].prototype;\n    document.body.removeChild(iframeEl);\n    if (!untaintedObject) return defaultObj.prototype;\n    return untaintedBasePrototype$1[key] = untaintedObject;\n  } catch {\n    return defaultObj.prototype;\n  }\n}\nconst untaintedAccessorCache$1 = {};\nfunction getUntaintedAccessor$1(key, instance, accessor) {\n  var _a2;\n  const cacheKey = `${key}.${String(accessor)}`;\n  if (untaintedAccessorCache$1[cacheKey])\n    return untaintedAccessorCache$1[cacheKey].call(\n      instance\n    );\n  const untaintedPrototype = getUntaintedPrototype$1(key);\n  const untaintedAccessor = (_a2 = Object.getOwnPropertyDescriptor(\n    untaintedPrototype,\n    accessor\n  )) == null ? void 0 : _a2.get;\n  if (!untaintedAccessor) return instance[accessor];\n  untaintedAccessorCache$1[cacheKey] = untaintedAccessor;\n  return untaintedAccessor.call(instance);\n}\nconst untaintedMethodCache$1 = {};\nfunction getUntaintedMethod$1(key, instance, method) {\n  const cacheKey = `${key}.${String(method)}`;\n  if (untaintedMethodCache$1[cacheKey])\n    return untaintedMethodCache$1[cacheKey].bind(\n      instance\n    );\n  const untaintedPrototype = getUntaintedPrototype$1(key);\n  const untaintedMethod = untaintedPrototype[method];\n  if (typeof untaintedMethod !== \"function\") return instance[method];\n  untaintedMethodCache$1[cacheKey] = untaintedMethod;\n  return untaintedMethod.bind(instance);\n}\nfunction childNodes$1(n2) {\n  return getUntaintedAccessor$1(\"Node\", n2, \"childNodes\");\n}\nfunction parentNode$1(n2) {\n  return getUntaintedAccessor$1(\"Node\", n2, \"parentNode\");\n}\nfunction parentElement$1(n2) {\n  return getUntaintedAccessor$1(\"Node\", n2, \"parentElement\");\n}\nfunction textContent$1(n2) {\n  return getUntaintedAccessor$1(\"Node\", n2, \"textContent\");\n}\nfunction contains$1(n2, other) {\n  return getUntaintedMethod$1(\"Node\", n2, \"contains\")(other);\n}\nfunction getRootNode$1(n2) {\n  return getUntaintedMethod$1(\"Node\", n2, \"getRootNode\")();\n}\nfunction host$1(n2) {\n  if (!n2 || !(\"host\" in n2)) return null;\n  return getUntaintedAccessor$1(\"ShadowRoot\", n2, \"host\");\n}\nfunction styleSheets$1(n2) {\n  return n2.styleSheets;\n}\nfunction shadowRoot$1(n2) {\n  if (!n2 || !(\"shadowRoot\" in n2)) return null;\n  return getUntaintedAccessor$1(\"Element\", n2, \"shadowRoot\");\n}\nfunction querySelector$1(n2, selectors) {\n  return getUntaintedAccessor$1(\"Element\", n2, \"querySelector\")(selectors);\n}\nfunction querySelectorAll$1(n2, selectors) {\n  return getUntaintedAccessor$1(\"Element\", n2, \"querySelectorAll\")(selectors);\n}\nfunction mutationObserverCtor$1() {\n  return getUntaintedPrototype$1(\"MutationObserver\").constructor;\n}\nconst index$1 = {\n  childNodes: childNodes$1,\n  parentNode: parentNode$1,\n  parentElement: parentElement$1,\n  textContent: textContent$1,\n  contains: contains$1,\n  getRootNode: getRootNode$1,\n  host: host$1,\n  styleSheets: styleSheets$1,\n  shadowRoot: shadowRoot$1,\n  querySelector: querySelector$1,\n  querySelectorAll: querySelectorAll$1,\n  mutationObserver: mutationObserverCtor$1\n};\nfunction isElement(n2) {\n  return n2.nodeType === n2.ELEMENT_NODE;\n}\nfunction isShadowRoot(n2) {\n  const hostEl = (\n    // anchor and textarea elements also have a `host` property\n    // but only shadow roots have a `mode` property\n    n2 && \"host\" in n2 && \"mode\" in n2 && index$1.host(n2) || null\n  );\n  return Boolean(\n    hostEl && \"shadowRoot\" in hostEl && index$1.shadowRoot(hostEl) === n2\n  );\n}\nfunction isNativeShadowDom(shadowRoot2) {\n  return Object.prototype.toString.call(shadowRoot2) === \"[object ShadowRoot]\";\n}\nfunction fixBrowserCompatibilityIssuesInCSS(cssText) {\n  if (cssText.includes(\" background-clip: text;\") && !cssText.includes(\" -webkit-background-clip: text;\")) {\n    cssText = cssText.replace(\n      /\\sbackground-clip:\\s*text;/g,\n      \" -webkit-background-clip: text; background-clip: text;\"\n    );\n  }\n  return cssText;\n}\nfunction escapeImportStatement(rule2) {\n  const { cssText } = rule2;\n  if (cssText.split('\"').length < 3) return cssText;\n  const statement = [\"@import\", `url(${JSON.stringify(rule2.href)})`];\n  if (rule2.layerName === \"\") {\n    statement.push(`layer`);\n  } else if (rule2.layerName) {\n    statement.push(`layer(${rule2.layerName})`);\n  }\n  if (rule2.supportsText) {\n    statement.push(`supports(${rule2.supportsText})`);\n  }\n  if (rule2.media.length) {\n    statement.push(rule2.media.mediaText);\n  }\n  return statement.join(\" \") + \";\";\n}\nfunction stringifyStylesheet(s2) {\n  try {\n    const rules2 = s2.rules || s2.cssRules;\n    if (!rules2) {\n      return null;\n    }\n    const stringifiedRules = Array.from(\n      rules2,\n      (rule2) => stringifyRule(rule2, s2.href)\n    ).join(\"\");\n    return fixBrowserCompatibilityIssuesInCSS(stringifiedRules);\n  } catch (error) {\n    return null;\n  }\n}\nfunction stringifyRule(rule2, sheetHref) {\n  if (isCSSImportRule(rule2)) {\n    let importStringified;\n    try {\n      importStringified = // for same-origin stylesheets,\n      // we can access the imported stylesheet rules directly\n      stringifyStylesheet(rule2.styleSheet) || // work around browser issues with the raw string `@import url(...)` statement\n      escapeImportStatement(rule2);\n    } catch (error) {\n      importStringified = rule2.cssText;\n    }\n    if (rule2.styleSheet.href) {\n      return absolutifyURLs(importStringified, rule2.styleSheet.href);\n    }\n    return importStringified;\n  } else {\n    let ruleStringified = rule2.cssText;\n    if (isCSSStyleRule(rule2) && rule2.selectorText.includes(\":\")) {\n      ruleStringified = fixSafariColons(ruleStringified);\n    }\n    if (sheetHref) {\n      return absolutifyURLs(ruleStringified, sheetHref);\n    }\n    return ruleStringified;\n  }\n}\nfunction fixSafariColons(cssStringified) {\n  const regex = /(\\[(?:[\\w-]+)[^\\\\])(:(?:[\\w-]+)\\])/gm;\n  return cssStringified.replace(regex, \"$1\\\\$2\");\n}\nfunction isCSSImportRule(rule2) {\n  return \"styleSheet\" in rule2;\n}\nfunction isCSSStyleRule(rule2) {\n  return \"selectorText\" in rule2;\n}\nfunction findStylesheet(doc, href) {\n  return Array.from(doc.styleSheets).find((s) => s.href === href);\n}\nclass Mirror {\n  constructor() {\n    __publicField$1(this, \"idNodeMap\", /* @__PURE__ */ new Map());\n    __publicField$1(this, \"nodeMetaMap\", /* @__PURE__ */ new WeakMap());\n  }\n  getId(n2) {\n    var _a2;\n    if (!n2) return -1;\n    const id = (_a2 = this.getMeta(n2)) == null ? void 0 : _a2.id;\n    return id ?? -1;\n  }\n  getNode(id) {\n    return this.idNodeMap.get(id) || null;\n  }\n  getIds() {\n    return Array.from(this.idNodeMap.keys());\n  }\n  getMeta(n2) {\n    return this.nodeMetaMap.get(n2) || null;\n  }\n  // removes the node from idNodeMap\n  // doesn't remove the node from nodeMetaMap\n  removeNodeFromMap(n2) {\n    const id = this.getId(n2);\n    this.idNodeMap.delete(id);\n    if (n2.childNodes) {\n      n2.childNodes.forEach(\n        (childNode) => this.removeNodeFromMap(childNode)\n      );\n    }\n  }\n  has(id) {\n    return this.idNodeMap.has(id);\n  }\n  hasNode(node2) {\n    return this.nodeMetaMap.has(node2);\n  }\n  add(n2, meta) {\n    const id = meta.id;\n    this.idNodeMap.set(id, n2);\n    this.nodeMetaMap.set(n2, meta);\n  }\n  replace(id, n2) {\n    const oldNode = this.getNode(id);\n    if (oldNode) {\n      const meta = this.nodeMetaMap.get(oldNode);\n      if (meta) this.nodeMetaMap.set(n2, meta);\n    }\n    this.idNodeMap.set(id, n2);\n  }\n  reset() {\n    this.idNodeMap = /* @__PURE__ */ new Map();\n    this.nodeMetaMap = /* @__PURE__ */ new WeakMap();\n  }\n}\nfunction createMirror$2() {\n  return new Mirror();\n}\nfunction maskInputValue({\n  element,\n  maskInputOptions,\n  tagName,\n  type,\n  value,\n  maskInputFn\n}) {\n  let text = value || \"\";\n  const actualType = type && toLowerCase(type);\n  if (maskInputOptions[tagName.toLowerCase()] || actualType && maskInputOptions[actualType]) {\n    if (maskInputFn) {\n      text = maskInputFn(text, element);\n    } else {\n      text = \"*\".repeat(text.length);\n    }\n  }\n  return text;\n}\nfunction toLowerCase(str) {\n  return str.toLowerCase();\n}\nconst ORIGINAL_ATTRIBUTE_NAME = \"__rrweb_original__\";\nfunction is2DCanvasBlank(canvas) {\n  const ctx = canvas.getContext(\"2d\");\n  if (!ctx) return true;\n  const chunkSize = 50;\n  for (let x2 = 0; x2 < canvas.width; x2 += chunkSize) {\n    for (let y = 0; y < canvas.height; y += chunkSize) {\n      const getImageData = ctx.getImageData;\n      const originalGetImageData = ORIGINAL_ATTRIBUTE_NAME in getImageData ? getImageData[ORIGINAL_ATTRIBUTE_NAME] : getImageData;\n      const pixelBuffer = new Uint32Array(\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-unsafe-member-access\n        originalGetImageData.call(\n          ctx,\n          x2,\n          y,\n          Math.min(chunkSize, canvas.width - x2),\n          Math.min(chunkSize, canvas.height - y)\n        ).data.buffer\n      );\n      if (pixelBuffer.some((pixel) => pixel !== 0)) return false;\n    }\n  }\n  return true;\n}\nfunction getInputType(element) {\n  const type = element.type;\n  return element.hasAttribute(\"data-rr-is-password\") ? \"password\" : type ? (\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    toLowerCase(type)\n  ) : null;\n}\nfunction extractFileExtension(path, baseURL) {\n  let url;\n  try {\n    url = new URL(path, baseURL ?? window.location.href);\n  } catch (err) {\n    return null;\n  }\n  const regex = /\\.([0-9a-z]+)(?:$)/i;\n  const match = url.pathname.match(regex);\n  return (match == null ? void 0 : match[1]) ?? null;\n}\nfunction extractOrigin(url) {\n  let origin = \"\";\n  if (url.indexOf(\"//\") > -1) {\n    origin = url.split(\"/\").slice(0, 3).join(\"/\");\n  } else {\n    origin = url.split(\"/\")[0];\n  }\n  origin = origin.split(\"?\")[0];\n  return origin;\n}\nconst URL_IN_CSS_REF = /url\\((?:(')([^']*)'|(\")(.*?)\"|([^)]*))\\)/gm;\nconst URL_PROTOCOL_MATCH = /^(?:[a-z+]+:)?\\/\\//i;\nconst URL_WWW_MATCH = /^www\\..*/i;\nconst DATA_URI = /^(data:)([^,]*),(.*)/i;\nfunction absolutifyURLs(cssText, href) {\n  return (cssText || \"\").replace(\n    URL_IN_CSS_REF,\n    (origin, quote1, path1, quote2, path2, path3) => {\n      const filePath = path1 || path2 || path3;\n      const maybeQuote = quote1 || quote2 || \"\";\n      if (!filePath) {\n        return origin;\n      }\n      if (URL_PROTOCOL_MATCH.test(filePath) || URL_WWW_MATCH.test(filePath)) {\n        return `url(${maybeQuote}${filePath}${maybeQuote})`;\n      }\n      if (DATA_URI.test(filePath)) {\n        return `url(${maybeQuote}${filePath}${maybeQuote})`;\n      }\n      if (filePath[0] === \"/\") {\n        return `url(${maybeQuote}${extractOrigin(href) + filePath}${maybeQuote})`;\n      }\n      const stack = href.split(\"/\");\n      const parts = filePath.split(\"/\");\n      stack.pop();\n      for (const part of parts) {\n        if (part === \".\") {\n          continue;\n        } else if (part === \"..\") {\n          stack.pop();\n        } else {\n          stack.push(part);\n        }\n      }\n      return `url(${maybeQuote}${stack.join(\"/\")}${maybeQuote})`;\n    }\n  );\n}\nlet _id = 1;\nconst tagNameRegex = new RegExp(\"[^a-z0-9-_:]\");\nconst IGNORED_NODE = -2;\nfunction genId() {\n  return _id++;\n}\nfunction getValidTagName$1(element) {\n  if (element instanceof HTMLFormElement) {\n    return \"form\";\n  }\n  const processedTagName = toLowerCase(element.tagName);\n  if (tagNameRegex.test(processedTagName)) {\n    return \"div\";\n  }\n  return processedTagName;\n}\nlet canvasService;\nlet canvasCtx;\nconst SRCSET_NOT_SPACES = /^[^ \\t\\n\\r\\u000c]+/;\nconst SRCSET_COMMAS_OR_SPACES = /^[, \\t\\n\\r\\u000c]+/;\nfunction getAbsoluteSrcsetString(doc, attributeValue) {\n  if (attributeValue.trim() === \"\") {\n    return attributeValue;\n  }\n  let pos = 0;\n  function collectCharacters(regEx) {\n    let chars2;\n    const match = regEx.exec(attributeValue.substring(pos));\n    if (match) {\n      chars2 = match[0];\n      pos += chars2.length;\n      return chars2;\n    }\n    return \"\";\n  }\n  const output = [];\n  while (true) {\n    collectCharacters(SRCSET_COMMAS_OR_SPACES);\n    if (pos >= attributeValue.length) {\n      break;\n    }\n    let url = collectCharacters(SRCSET_NOT_SPACES);\n    if (url.slice(-1) === \",\") {\n      url = absoluteToDoc(doc, url.substring(0, url.length - 1));\n      output.push(url);\n    } else {\n      let descriptorsStr = \"\";\n      url = absoluteToDoc(doc, url);\n      let inParens = false;\n      while (true) {\n        const c2 = attributeValue.charAt(pos);\n        if (c2 === \"\") {\n          output.push((url + descriptorsStr).trim());\n          break;\n        } else if (!inParens) {\n          if (c2 === \",\") {\n            pos += 1;\n            output.push((url + descriptorsStr).trim());\n            break;\n          } else if (c2 === \"(\") {\n            inParens = true;\n          }\n        } else {\n          if (c2 === \")\") {\n            inParens = false;\n          }\n        }\n        descriptorsStr += c2;\n        pos += 1;\n      }\n    }\n  }\n  return output.join(\", \");\n}\nconst cachedDocument = /* @__PURE__ */ new WeakMap();\nfunction absoluteToDoc(doc, attributeValue) {\n  if (!attributeValue || attributeValue.trim() === \"\") {\n    return attributeValue;\n  }\n  return getHref(doc, attributeValue);\n}\nfunction isSVGElement(el) {\n  return Boolean(el.tagName === \"svg\" || el.ownerSVGElement);\n}\nfunction getHref(doc, customHref) {\n  let a2 = cachedDocument.get(doc);\n  if (!a2) {\n    a2 = doc.createElement(\"a\");\n    cachedDocument.set(doc, a2);\n  }\n  if (!customHref) {\n    customHref = \"\";\n  } else if (customHref.startsWith(\"blob:\") || customHref.startsWith(\"data:\")) {\n    return customHref;\n  }\n  a2.setAttribute(\"href\", customHref);\n  return a2.href;\n}\nfunction transformAttribute(doc, tagName, name, value) {\n  if (!value) {\n    return value;\n  }\n  if (name === \"src\" || name === \"href\" && !(tagName === \"use\" && value[0] === \"#\")) {\n    return absoluteToDoc(doc, value);\n  } else if (name === \"xlink:href\" && value[0] !== \"#\") {\n    return absoluteToDoc(doc, value);\n  } else if (name === \"background\" && (tagName === \"table\" || tagName === \"td\" || tagName === \"th\")) {\n    return absoluteToDoc(doc, value);\n  } else if (name === \"srcset\") {\n    return getAbsoluteSrcsetString(doc, value);\n  } else if (name === \"style\") {\n    return absolutifyURLs(value, getHref(doc));\n  } else if (tagName === \"object\" && name === \"data\") {\n    return absoluteToDoc(doc, value);\n  }\n  return value;\n}\nfunction ignoreAttribute(tagName, name, _value) {\n  return (tagName === \"video\" || tagName === \"audio\") && name === \"autoplay\";\n}\nfunction _isBlockedElement(element, blockClass, blockSelector) {\n  try {\n    if (typeof blockClass === \"string\") {\n      if (element.classList.contains(blockClass)) {\n        return true;\n      }\n    } else {\n      for (let eIndex = element.classList.length; eIndex--; ) {\n        const className = element.classList[eIndex];\n        if (blockClass.test(className)) {\n          return true;\n        }\n      }\n    }\n    if (blockSelector) {\n      return element.matches(blockSelector);\n    }\n  } catch (e2) {\n  }\n  return false;\n}\nfunction classMatchesRegex(node2, regex, checkAncestors) {\n  if (!node2) return false;\n  if (node2.nodeType !== node2.ELEMENT_NODE) {\n    if (!checkAncestors) return false;\n    return classMatchesRegex(index$1.parentNode(node2), regex, checkAncestors);\n  }\n  for (let eIndex = node2.classList.length; eIndex--; ) {\n    const className = node2.classList[eIndex];\n    if (regex.test(className)) {\n      return true;\n    }\n  }\n  if (!checkAncestors) return false;\n  return classMatchesRegex(index$1.parentNode(node2), regex, checkAncestors);\n}\nfunction needMaskingText(node2, maskTextClass, maskTextSelector, checkAncestors) {\n  let el;\n  if (isElement(node2)) {\n    el = node2;\n    if (!index$1.childNodes(el).length) {\n      return false;\n    }\n  } else if (index$1.parentElement(node2) === null) {\n    return false;\n  } else {\n    el = index$1.parentElement(node2);\n  }\n  try {\n    if (typeof maskTextClass === \"string\") {\n      if (checkAncestors) {\n        if (el.closest(`.${maskTextClass}`)) return true;\n      } else {\n        if (el.classList.contains(maskTextClass)) return true;\n      }\n    } else {\n      if (classMatchesRegex(el, maskTextClass, checkAncestors)) return true;\n    }\n    if (maskTextSelector) {\n      if (checkAncestors) {\n        if (el.closest(maskTextSelector)) return true;\n      } else {\n        if (el.matches(maskTextSelector)) return true;\n      }\n    }\n  } catch (e2) {\n  }\n  return false;\n}\nfunction onceIframeLoaded(iframeEl, listener, iframeLoadTimeout) {\n  const win = iframeEl.contentWindow;\n  if (!win) {\n    return;\n  }\n  let fired = false;\n  let readyState;\n  try {\n    readyState = win.document.readyState;\n  } catch (error) {\n    return;\n  }\n  if (readyState !== \"complete\") {\n    const timer = setTimeout(() => {\n      if (!fired) {\n        listener();\n        fired = true;\n      }\n    }, iframeLoadTimeout);\n    iframeEl.addEventListener(\"load\", () => {\n      clearTimeout(timer);\n      fired = true;\n      listener();\n    });\n    return;\n  }\n  const blankUrl = \"about:blank\";\n  if (win.location.href !== blankUrl || iframeEl.src === blankUrl || iframeEl.src === \"\") {\n    setTimeout(listener, 0);\n    return iframeEl.addEventListener(\"load\", listener);\n  }\n  iframeEl.addEventListener(\"load\", listener);\n}\nfunction onceStylesheetLoaded(link, listener, styleSheetLoadTimeout) {\n  let fired = false;\n  let styleSheetLoaded;\n  try {\n    styleSheetLoaded = link.sheet;\n  } catch (error) {\n    return;\n  }\n  if (styleSheetLoaded) return;\n  const timer = setTimeout(() => {\n    if (!fired) {\n      listener();\n      fired = true;\n    }\n  }, styleSheetLoadTimeout);\n  link.addEventListener(\"load\", () => {\n    clearTimeout(timer);\n    fired = true;\n    listener();\n  });\n}\nfunction serializeNode(n2, options) {\n  const {\n    doc,\n    mirror: mirror2,\n    blockClass,\n    blockSelector,\n    needsMask,\n    inlineStylesheet,\n    maskInputOptions = {},\n    maskTextFn,\n    maskInputFn,\n    dataURLOptions = {},\n    inlineImages,\n    recordCanvas,\n    keepIframeSrcFn,\n    newlyAddedElement = false\n  } = options;\n  const rootId = getRootId(doc, mirror2);\n  switch (n2.nodeType) {\n    case n2.DOCUMENT_NODE:\n      if (n2.compatMode !== \"CSS1Compat\") {\n        return {\n          type: NodeType$2.Document,\n          childNodes: [],\n          compatMode: n2.compatMode\n          // probably \"BackCompat\"\n        };\n      } else {\n        return {\n          type: NodeType$2.Document,\n          childNodes: []\n        };\n      }\n    case n2.DOCUMENT_TYPE_NODE:\n      return {\n        type: NodeType$2.DocumentType,\n        name: n2.name,\n        publicId: n2.publicId,\n        systemId: n2.systemId,\n        rootId\n      };\n    case n2.ELEMENT_NODE:\n      return serializeElementNode(n2, {\n        doc,\n        blockClass,\n        blockSelector,\n        inlineStylesheet,\n        maskInputOptions,\n        maskInputFn,\n        dataURLOptions,\n        inlineImages,\n        recordCanvas,\n        keepIframeSrcFn,\n        newlyAddedElement,\n        rootId\n      });\n    case n2.TEXT_NODE:\n      return serializeTextNode(n2, {\n        doc,\n        needsMask,\n        maskTextFn,\n        rootId\n      });\n    case n2.CDATA_SECTION_NODE:\n      return {\n        type: NodeType$2.CDATA,\n        textContent: \"\",\n        rootId\n      };\n    case n2.COMMENT_NODE:\n      return {\n        type: NodeType$2.Comment,\n        textContent: index$1.textContent(n2) || \"\",\n        rootId\n      };\n    default:\n      return false;\n  }\n}\nfunction getRootId(doc, mirror2) {\n  if (!mirror2.hasNode(doc)) return void 0;\n  const docId = mirror2.getId(doc);\n  return docId === 1 ? void 0 : docId;\n}\nfunction serializeTextNode(n2, options) {\n  var _a2;\n  const { needsMask, maskTextFn, rootId } = options;\n  const parent = index$1.parentNode(n2);\n  const parentTagName = parent && parent.tagName;\n  let text = index$1.textContent(n2);\n  const isStyle = parentTagName === \"STYLE\" ? true : void 0;\n  const isScript = parentTagName === \"SCRIPT\" ? true : void 0;\n  if (isStyle && text) {\n    try {\n      if (n2.nextSibling || n2.previousSibling) {\n      } else if ((_a2 = parent.sheet) == null ? void 0 : _a2.cssRules) {\n        text = stringifyStylesheet(parent.sheet);\n      }\n    } catch (err) {\n      console.warn(\n        `Cannot get CSS styles from text's parentNode. Error: ${err}`,\n        n2\n      );\n    }\n    text = absolutifyURLs(text, getHref(options.doc));\n  }\n  if (isScript) {\n    text = \"SCRIPT_PLACEHOLDER\";\n  }\n  if (!isStyle && !isScript && text && needsMask) {\n    text = maskTextFn ? maskTextFn(text, index$1.parentElement(n2)) : text.replace(/[\\S]/g, \"*\");\n  }\n  return {\n    type: NodeType$2.Text,\n    textContent: text || \"\",\n    isStyle,\n    rootId\n  };\n}\n\n/**\n * in production, we've seen elements like\n * `<link type=\"text/css\" rel=\"stylesheet\" id=\"dark-mode-custom-link\"></link>`\n * while HTMLLinkElement suggests an href is always present\n * the w3c spec is less specific\n * and regardless we've seen at least one instance of this in the wild\n * let's be defensive and make sure this is typed as possibly undefined\n */\nfunction hrefFrom(n) {\n  return n?.href;\n}\n\nfunction serializeElementNode(n2, options) {\n  const {\n    doc,\n    blockClass,\n    blockSelector,\n    inlineStylesheet,\n    maskInputOptions = {},\n    maskInputFn,\n    dataURLOptions = {},\n    inlineImages,\n    recordCanvas,\n    keepIframeSrcFn,\n    newlyAddedElement = false,\n    rootId\n  } = options;\n  const needBlock = _isBlockedElement(n2, blockClass, blockSelector);\n  const tagName = getValidTagName$1(n2);\n  let attributes = {};\n  const len = n2.attributes.length;\n  for (let i2 = 0; i2 < len; i2++) {\n    const attr = n2.attributes[i2];\n    if (!ignoreAttribute(tagName, attr.name, attr.value)) {\n      attributes[attr.name] = transformAttribute(\n        doc,\n        tagName,\n        toLowerCase(attr.name),\n        attr.value\n      );\n    }\n  }\n  if (tagName === \"link\" && inlineStylesheet) {\n    const href = hrefFrom(n2);\n    if (href) {\n      let stylesheet = findStylesheet(doc, href);\n      if (!stylesheet && href.includes('.css')) {\n        const rootDomain = window.location.origin;\n        const stylesheetPath = href.replace(window.location.href, '');\n        const potentialStylesheetHref = rootDomain + '/' + stylesheetPath;\n        stylesheet = findStylesheet(doc, potentialStylesheetHref);\n      }\n      let cssText = null;\n      if (stylesheet) {\n        cssText = stringifyStylesheet(stylesheet);\n      }\n      if (cssText) {\n        delete attributes.rel;\n        delete attributes.href;\n        attributes._cssText = cssText;\n      }\n    }\n  }\n  if (tagName === \"style\" && n2.sheet && // TODO: Currently we only try to get dynamic stylesheet when it is an empty style element\n  !(n2.innerText || index$1.textContent(n2) || \"\").trim().length) {\n    const cssText = stringifyStylesheet(\n      n2.sheet\n    );\n    if (cssText) {\n      attributes._cssText = cssText;\n    }\n  }\n  if (tagName === \"input\" || tagName === \"textarea\" || tagName === \"select\") {\n    const value = n2.value;\n    const checked = n2.checked;\n    if (attributes.type !== \"radio\" && attributes.type !== \"checkbox\" && attributes.type !== \"submit\" && attributes.type !== \"button\" && value) {\n      attributes.value = maskInputValue({\n        element: n2,\n        type: getInputType(n2),\n        tagName,\n        value,\n        maskInputOptions,\n        maskInputFn\n      });\n    } else if (checked) {\n      attributes.checked = checked;\n    }\n  }\n  if (tagName === \"option\") {\n    if (n2.selected && !maskInputOptions[\"select\"]) {\n      attributes.selected = true;\n    } else {\n      delete attributes.selected;\n    }\n  }\n  if (tagName === \"dialog\" && n2.open) {\n    try {\n      attributes.rr_open_mode = n2.matches(\"dialog:modal\") ? \"modal\" : \"non-modal\";\n    } catch {\n      // likely this is safari not able to deal with the `:modal` selector\n      // we can't detect whether the dialog is modal or non-modal open, so have to guess\n      // hopefully this is only safari 15.4 and 15.5\n      attributes.rr_open_mode = \"modal\"\n      attributes.ph_rr_could_not_detect_modal = true\n    }\n  }\n  if (tagName === \"canvas\" && recordCanvas) {\n    if (n2.__context === \"2d\") {\n      if (!is2DCanvasBlank(n2)) {\n        attributes.rr_dataURL = n2.toDataURL(\n          dataURLOptions.type,\n          dataURLOptions.quality\n        );\n      }\n    } else if (!(\"__context\" in n2)) {\n      const canvasDataURL = n2.toDataURL(\n        dataURLOptions.type,\n        dataURLOptions.quality\n      );\n      const blankCanvas = doc.createElement(\"canvas\");\n      blankCanvas.width = n2.width;\n      blankCanvas.height = n2.height;\n      const blankCanvasDataURL = blankCanvas.toDataURL(\n        dataURLOptions.type,\n        dataURLOptions.quality\n      );\n      if (canvasDataURL !== blankCanvasDataURL) {\n        attributes.rr_dataURL = canvasDataURL;\n      }\n    }\n  }\n  if (tagName === \"img\" && inlineImages) {\n    if (!canvasService) {\n      canvasService = doc.createElement(\"canvas\");\n      canvasCtx = canvasService.getContext(\"2d\");\n    }\n    const image = n2;\n    const imageSrc = image.currentSrc || image.getAttribute(\"src\") || \"<unknown-src>\";\n    const priorCrossOrigin = image.crossOrigin;\n    const recordInlineImage = () => {\n      image.removeEventListener(\"load\", recordInlineImage);\n      try {\n        canvasService.width = image.naturalWidth;\n        canvasService.height = image.naturalHeight;\n        canvasCtx.drawImage(image, 0, 0);\n        attributes.rr_dataURL = canvasService.toDataURL(\n          dataURLOptions.type,\n          dataURLOptions.quality\n        );\n      } catch (err) {\n        if (image.crossOrigin !== \"anonymous\") {\n          image.crossOrigin = \"anonymous\";\n          if (image.complete && image.naturalWidth !== 0)\n            recordInlineImage();\n          else image.addEventListener(\"load\", recordInlineImage);\n          return;\n        } else {\n          console.warn(\n            `Cannot inline img src=${imageSrc}! Error: ${err}`\n          );\n        }\n      }\n      if (image.crossOrigin === \"anonymous\") {\n        priorCrossOrigin ? attributes.crossOrigin = priorCrossOrigin : image.removeAttribute(\"crossorigin\");\n      }\n    };\n    if (image.complete && image.naturalWidth !== 0) recordInlineImage();\n    else image.addEventListener(\"load\", recordInlineImage);\n  }\n  if (tagName === \"audio\" || tagName === \"video\") {\n    const mediaAttributes = attributes;\n    mediaAttributes.rr_mediaState = n2.paused ? \"paused\" : \"played\";\n    mediaAttributes.rr_mediaCurrentTime = n2.currentTime;\n    mediaAttributes.rr_mediaPlaybackRate = n2.playbackRate;\n    mediaAttributes.rr_mediaMuted = n2.muted;\n    mediaAttributes.rr_mediaLoop = n2.loop;\n    mediaAttributes.rr_mediaVolume = n2.volume;\n  }\n  if (!newlyAddedElement) {\n    if (n2.scrollLeft) {\n      attributes.rr_scrollLeft = n2.scrollLeft;\n    }\n    if (n2.scrollTop) {\n      attributes.rr_scrollTop = n2.scrollTop;\n    }\n  }\n  if (needBlock) {\n    const { width, height } = n2.getBoundingClientRect();\n    attributes = {\n      class: attributes.class,\n      rr_width: `${width}px`,\n      rr_height: `${height}px`\n    };\n  }\n  if (tagName === \"iframe\" && !keepIframeSrcFn(attributes.src)) {\n    if (!n2.contentDocument) {\n      attributes.rr_src = attributes.src;\n    }\n    delete attributes.src;\n  }\n  let isCustomElement;\n  try {\n    if (customElements.get(tagName)) isCustomElement = true;\n  } catch (e2) {\n  }\n  return {\n    type: NodeType$2.Element,\n    tagName,\n    attributes,\n    childNodes: [],\n    isSVG: isSVGElement(n2) || void 0,\n    needBlock,\n    rootId,\n    isCustom: isCustomElement\n  };\n}\nfunction lowerIfExists(maybeAttr) {\n  if (maybeAttr === void 0 || maybeAttr === null) {\n    return \"\";\n  } else {\n    return maybeAttr.toLowerCase();\n  }\n}\nfunction slimDOMExcluded(sn, slimDOMOptions) {\n  if (slimDOMOptions.comment && sn.type === NodeType$2.Comment) {\n    return true;\n  } else if (sn.type === NodeType$2.Element) {\n    if (slimDOMOptions.script && // script tag\n    (sn.tagName === \"script\" || // (module)preload link\n    sn.tagName === \"link\" && (sn.attributes.rel === \"preload\" || sn.attributes.rel === \"modulepreload\") && sn.attributes.as === \"script\" || // prefetch link\n    sn.tagName === \"link\" && sn.attributes.rel === \"prefetch\" && typeof sn.attributes.href === \"string\" && extractFileExtension(sn.attributes.href) === \"js\")) {\n      return true;\n    } else if (slimDOMOptions.headFavicon && (sn.tagName === \"link\" && sn.attributes.rel === \"shortcut icon\" || sn.tagName === \"meta\" && (lowerIfExists(sn.attributes.name).match(\n      /^msapplication-tile(image|color)$/\n    ) || lowerIfExists(sn.attributes.name) === \"application-name\" || lowerIfExists(sn.attributes.rel) === \"icon\" || lowerIfExists(sn.attributes.rel) === \"apple-touch-icon\" || lowerIfExists(sn.attributes.rel) === \"shortcut icon\"))) {\n      return true;\n    } else if (sn.tagName === \"meta\") {\n      if (slimDOMOptions.headMetaDescKeywords && lowerIfExists(sn.attributes.name).match(/^description|keywords$/)) {\n        return true;\n      } else if (slimDOMOptions.headMetaSocial && (lowerIfExists(sn.attributes.property).match(/^(og|twitter|fb):/) || // og = opengraph (facebook)\n      lowerIfExists(sn.attributes.name).match(/^(og|twitter):/) || lowerIfExists(sn.attributes.name) === \"pinterest\")) {\n        return true;\n      } else if (slimDOMOptions.headMetaRobots && (lowerIfExists(sn.attributes.name) === \"robots\" || lowerIfExists(sn.attributes.name) === \"googlebot\" || lowerIfExists(sn.attributes.name) === \"bingbot\")) {\n        return true;\n      } else if (slimDOMOptions.headMetaHttpEquiv && sn.attributes[\"http-equiv\"] !== void 0) {\n        return true;\n      } else if (slimDOMOptions.headMetaAuthorship && (lowerIfExists(sn.attributes.name) === \"author\" || lowerIfExists(sn.attributes.name) === \"generator\" || lowerIfExists(sn.attributes.name) === \"framework\" || lowerIfExists(sn.attributes.name) === \"publisher\" || lowerIfExists(sn.attributes.name) === \"progid\" || lowerIfExists(sn.attributes.property).match(/^article:/) || lowerIfExists(sn.attributes.property).match(/^product:/))) {\n        return true;\n      } else if (slimDOMOptions.headMetaVerification && (lowerIfExists(sn.attributes.name) === \"google-site-verification\" || lowerIfExists(sn.attributes.name) === \"yandex-verification\" || lowerIfExists(sn.attributes.name) === \"csrf-token\" || lowerIfExists(sn.attributes.name) === \"p:domain_verify\" || lowerIfExists(sn.attributes.name) === \"verify-v1\" || lowerIfExists(sn.attributes.name) === \"verification\" || lowerIfExists(sn.attributes.name) === \"shopify-checkout-api-token\")) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\nfunction serializeNodeWithId(n2, options) {\n  const {\n    doc,\n    mirror: mirror2,\n    blockClass,\n    blockSelector,\n    maskTextClass,\n    maskTextSelector,\n    skipChild = false,\n    inlineStylesheet = true,\n    maskInputOptions = {},\n    maskTextFn,\n    maskInputFn,\n    slimDOMOptions,\n    dataURLOptions = {},\n    inlineImages = false,\n    recordCanvas = false,\n    onSerialize,\n    onIframeLoad,\n    iframeLoadTimeout = 5e3,\n    onStylesheetLoad,\n    stylesheetLoadTimeout = 5e3,\n    keepIframeSrcFn = () => false,\n    newlyAddedElement = false\n  } = options;\n  let { needsMask } = options;\n  let { preserveWhiteSpace = true } = options;\n  if (!needsMask) {\n    const checkAncestors = needsMask === void 0;\n    needsMask = needMaskingText(\n      n2,\n      maskTextClass,\n      maskTextSelector,\n      checkAncestors\n    );\n  }\n  const _serializedNode = serializeNode(n2, {\n    doc,\n    mirror: mirror2,\n    blockClass,\n    blockSelector,\n    needsMask,\n    inlineStylesheet,\n    maskInputOptions,\n    maskTextFn,\n    maskInputFn,\n    dataURLOptions,\n    inlineImages,\n    recordCanvas,\n    keepIframeSrcFn,\n    newlyAddedElement\n  });\n  if (!_serializedNode) {\n    console.warn(n2, \"not serialized\");\n    return null;\n  }\n  let id;\n  if (mirror2.hasNode(n2)) {\n    id = mirror2.getId(n2);\n  } else if (slimDOMExcluded(_serializedNode, slimDOMOptions) || !preserveWhiteSpace && _serializedNode.type === NodeType$2.Text && !_serializedNode.isStyle && !_serializedNode.textContent.replace(/^\\s+|\\s+$/gm, \"\").length) {\n    id = IGNORED_NODE;\n  } else {\n    id = genId();\n  }\n  const serializedNode = Object.assign(_serializedNode, { id });\n  mirror2.add(n2, serializedNode);\n  if (id === IGNORED_NODE) {\n    return null;\n  }\n  if (onSerialize) {\n    onSerialize(n2);\n  }\n  let recordChild = !skipChild;\n  if (serializedNode.type === NodeType$2.Element) {\n    recordChild = recordChild && !serializedNode.needBlock;\n    delete serializedNode.needBlock;\n    const shadowRootEl = index$1.shadowRoot(n2);\n    if (shadowRootEl && isNativeShadowDom(shadowRootEl))\n      serializedNode.isShadowHost = true;\n  }\n  if ((serializedNode.type === NodeType$2.Document || serializedNode.type === NodeType$2.Element) && recordChild) {\n    if (slimDOMOptions.headWhitespace && serializedNode.type === NodeType$2.Element && serializedNode.tagName === \"head\") {\n      preserveWhiteSpace = false;\n    }\n    const bypassOptions = {\n      doc,\n      mirror: mirror2,\n      blockClass,\n      blockSelector,\n      needsMask,\n      maskTextClass,\n      maskTextSelector,\n      skipChild,\n      inlineStylesheet,\n      maskInputOptions,\n      maskTextFn,\n      maskInputFn,\n      slimDOMOptions,\n      dataURLOptions,\n      inlineImages,\n      recordCanvas,\n      preserveWhiteSpace,\n      onSerialize,\n      onIframeLoad,\n      iframeLoadTimeout,\n      onStylesheetLoad,\n      stylesheetLoadTimeout,\n      keepIframeSrcFn\n    };\n    if (serializedNode.type === NodeType$2.Element && serializedNode.tagName === \"textarea\" && serializedNode.attributes.value !== void 0) ;\n    else {\n      for (const childN of Array.from(index$1.childNodes(n2))) {\n        const serializedChildNode = serializeNodeWithId(childN, bypassOptions);\n        if (serializedChildNode) {\n          serializedNode.childNodes.push(serializedChildNode);\n        }\n      }\n    }\n    let shadowRootEl = null;\n    if (isElement(n2) && (shadowRootEl = index$1.shadowRoot(n2))) {\n      for (const childN of Array.from(index$1.childNodes(shadowRootEl))) {\n        const serializedChildNode = serializeNodeWithId(childN, bypassOptions);\n        if (serializedChildNode) {\n          isNativeShadowDom(shadowRootEl) && (serializedChildNode.isShadow = true);\n          serializedNode.childNodes.push(serializedChildNode);\n        }\n      }\n    }\n  }\n  const parent = index$1.parentNode(n2);\n  if (parent && isShadowRoot(parent) && isNativeShadowDom(parent)) {\n    serializedNode.isShadow = true;\n  }\n  if (serializedNode.type === NodeType$2.Element && serializedNode.tagName === \"iframe\") {\n    onceIframeLoaded(\n      n2,\n      () => {\n        const iframeDoc = n2.contentDocument;\n        if (iframeDoc && onIframeLoad) {\n          const serializedIframeNode = serializeNodeWithId(iframeDoc, {\n            doc: iframeDoc,\n            mirror: mirror2,\n            blockClass,\n            blockSelector,\n            needsMask,\n            maskTextClass,\n            maskTextSelector,\n            skipChild: false,\n            inlineStylesheet,\n            maskInputOptions,\n            maskTextFn,\n            maskInputFn,\n            slimDOMOptions,\n            dataURLOptions,\n            inlineImages,\n            recordCanvas,\n            preserveWhiteSpace,\n            onSerialize,\n            onIframeLoad,\n            iframeLoadTimeout,\n            onStylesheetLoad,\n            stylesheetLoadTimeout,\n            keepIframeSrcFn\n          });\n          if (serializedIframeNode) {\n            onIframeLoad(\n              n2,\n              serializedIframeNode\n            );\n          }\n        }\n      },\n      iframeLoadTimeout\n    );\n  }\n  if (serializedNode.type === NodeType$2.Element && serializedNode.tagName === \"link\" && typeof serializedNode.attributes.rel === \"string\" && (serializedNode.attributes.rel === \"stylesheet\" || serializedNode.attributes.rel === \"preload\" && typeof serializedNode.attributes.href === \"string\" && extractFileExtension(serializedNode.attributes.href) === \"css\")) {\n    onceStylesheetLoaded(\n      n2,\n      () => {\n        if (onStylesheetLoad) {\n          const serializedLinkNode = serializeNodeWithId(n2, {\n            doc,\n            mirror: mirror2,\n            blockClass,\n            blockSelector,\n            needsMask,\n            maskTextClass,\n            maskTextSelector,\n            skipChild: false,\n            inlineStylesheet,\n            maskInputOptions,\n            maskTextFn,\n            maskInputFn,\n            slimDOMOptions,\n            dataURLOptions,\n            inlineImages,\n            recordCanvas,\n            preserveWhiteSpace,\n            onSerialize,\n            onIframeLoad,\n            iframeLoadTimeout,\n            onStylesheetLoad,\n            stylesheetLoadTimeout,\n            keepIframeSrcFn\n          });\n          if (serializedLinkNode) {\n            onStylesheetLoad(\n              n2,\n              serializedLinkNode\n            );\n          }\n        }\n      },\n      stylesheetLoadTimeout\n    );\n  }\n  return serializedNode;\n}\nfunction snapshot(n2, options) {\n  const {\n    mirror: mirror2 = new Mirror(),\n    blockClass = \"rr-block\",\n    blockSelector = null,\n    maskTextClass = \"rr-mask\",\n    maskTextSelector = null,\n    inlineStylesheet = true,\n    inlineImages = false,\n    recordCanvas = false,\n    maskAllInputs = false,\n    maskTextFn,\n    maskInputFn,\n    slimDOM = false,\n    dataURLOptions,\n    preserveWhiteSpace,\n    onSerialize,\n    onIframeLoad,\n    iframeLoadTimeout,\n    onStylesheetLoad,\n    stylesheetLoadTimeout,\n    keepIframeSrcFn = () => false\n  } = options || {};\n  const maskInputOptions = maskAllInputs === true ? {\n    color: true,\n    date: true,\n    \"datetime-local\": true,\n    email: true,\n    month: true,\n    number: true,\n    range: true,\n    search: true,\n    tel: true,\n    text: true,\n    time: true,\n    url: true,\n    week: true,\n    textarea: true,\n    select: true,\n    password: true\n  } : maskAllInputs === false ? {\n    password: true\n  } : maskAllInputs;\n  const slimDOMOptions = slimDOM === true || slimDOM === \"all\" ? (\n    // if true: set of sensible options that should not throw away any information\n    {\n      script: true,\n      comment: true,\n      headFavicon: true,\n      headWhitespace: true,\n      headMetaDescKeywords: slimDOM === \"all\",\n      // destructive\n      headMetaSocial: true,\n      headMetaRobots: true,\n      headMetaHttpEquiv: true,\n      headMetaAuthorship: true,\n      headMetaVerification: true\n    }\n  ) : slimDOM === false ? {} : slimDOM;\n  return serializeNodeWithId(n2, {\n    doc: n2,\n    mirror: mirror2,\n    blockClass,\n    blockSelector,\n    maskTextClass,\n    maskTextSelector,\n    skipChild: false,\n    inlineStylesheet,\n    maskInputOptions,\n    maskTextFn,\n    maskInputFn,\n    slimDOMOptions,\n    dataURLOptions,\n    inlineImages,\n    recordCanvas,\n    preserveWhiteSpace,\n    onSerialize,\n    onIframeLoad,\n    iframeLoadTimeout,\n    onStylesheetLoad,\n    stylesheetLoadTimeout,\n    keepIframeSrcFn,\n    newlyAddedElement: false\n  });\n}\nfunction getDefaultExportFromCjs$1(x2) {\n  return x2 && x2.__esModule && Object.prototype.hasOwnProperty.call(x2, \"default\") ? x2[\"default\"] : x2;\n}\nfunction getAugmentedNamespace$1(n2) {\n  if (n2.__esModule) return n2;\n  var f2 = n2.default;\n  if (typeof f2 == \"function\") {\n    var a2 = function a22() {\n      if (this instanceof a22) {\n        return Reflect.construct(f2, arguments, this.constructor);\n      }\n      return f2.apply(this, arguments);\n    };\n    a2.prototype = f2.prototype;\n  } else a2 = {};\n  Object.defineProperty(a2, \"__esModule\", { value: true });\n  Object.keys(n2).forEach(function(k) {\n    var d = Object.getOwnPropertyDescriptor(n2, k);\n    Object.defineProperty(a2, k, d.get ? d : {\n      enumerable: true,\n      get: function() {\n        return n2[k];\n      }\n    });\n  });\n  return a2;\n}\n// Removed postcss\nclass BaseRRNode {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any\n  constructor(..._args) {\n    __publicField2(this, \"parentElement\", null);\n    __publicField2(this, \"parentNode\", null);\n    __publicField2(this, \"ownerDocument\");\n    __publicField2(this, \"firstChild\", null);\n    __publicField2(this, \"lastChild\", null);\n    __publicField2(this, \"previousSibling\", null);\n    __publicField2(this, \"nextSibling\", null);\n    __publicField2(this, \"ELEMENT_NODE\", 1);\n    __publicField2(this, \"TEXT_NODE\", 3);\n    __publicField2(this, \"nodeType\");\n    __publicField2(this, \"nodeName\");\n    __publicField2(this, \"RRNodeType\");\n  }\n  get childNodes() {\n    const childNodes2 = [];\n    let childIterator = this.firstChild;\n    while (childIterator) {\n      childNodes2.push(childIterator);\n      childIterator = childIterator.nextSibling;\n    }\n    return childNodes2;\n  }\n  contains(node2) {\n    if (!(node2 instanceof BaseRRNode)) return false;\n    else if (node2.ownerDocument !== this.ownerDocument) return false;\n    else if (node2 === this) return true;\n    while (node2.parentNode) {\n      if (node2.parentNode === this) return true;\n      node2 = node2.parentNode;\n    }\n    return false;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  appendChild(_newChild) {\n    throw new Error(\n      `RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.`\n    );\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  insertBefore(_newChild, _refChild) {\n    throw new Error(\n      `RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.`\n    );\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  removeChild(_node) {\n    throw new Error(\n      `RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.`\n    );\n  }\n  toString() {\n    return \"RRNode\";\n  }\n}\nconst testableAccessors = {\n  Node: [\"childNodes\", \"parentNode\", \"parentElement\", \"textContent\"],\n  ShadowRoot: [\"host\", \"styleSheets\"],\n  Element: [\"shadowRoot\", \"querySelector\", \"querySelectorAll\"],\n  MutationObserver: []\n};\nconst testableMethods = {\n  Node: [\"contains\", \"getRootNode\"],\n  ShadowRoot: [\"getSelection\"],\n  Element: [],\n  MutationObserver: [\"constructor\"]\n};\n/*\nAngular zone patches many things and can pass the untainted checks below, causing performance issues\nAngular zone, puts the unpatched originals on the window, and the names for hose on the zone object.\nSo, we get the unpatched versions from the window object if they exist.\nYou can rename Zone, but this is a good enough proxy to avoid going to an iframe to get the untainted versions.\nsee: https://github.com/angular/angular/issues/26948\n*/\nfunction angularZoneUnpatchedAlternative$1(key) {\n  const angularUnpatchedVersionSymbol = (\n    globalThis\n  )?.Zone?.__symbol__?.(key);\n  if (\n    angularUnpatchedVersionSymbol &&\n    (globalThis)[angularUnpatchedVersionSymbol]\n  ) {\n    return (globalThis)[\n      angularUnpatchedVersionSymbol\n    ];\n  } else {\n    return undefined;\n  }\n}\n\nconst untaintedBasePrototype = {};\nfunction getUntaintedPrototype(key) {\n  if (untaintedBasePrototype[key])\n    return untaintedBasePrototype[key];\n  const defaultObj = angularZoneUnpatchedAlternative$1(key) ||globalThis[key];\n  const defaultPrototype = defaultObj.prototype;\n  const accessorNames = key in testableAccessors ? testableAccessors[key] : void 0;\n  const isUntaintedAccessors = Boolean(\n    accessorNames && // @ts-expect-error 2345\n    accessorNames.every(\n      (accessor) => {\n        var _a2, _b;\n        return Boolean(\n          (_b = (_a2 = Object.getOwnPropertyDescriptor(defaultPrototype, accessor)) == null ? void 0 : _a2.get) == null ? void 0 : _b.toString().includes(\"[native code]\")\n        );\n      }\n    )\n  );\n  const methodNames = key in testableMethods ? testableMethods[key] : void 0;\n  const isUntaintedMethods = Boolean(\n    methodNames && methodNames.every(\n      // @ts-expect-error 2345\n      (method) => {\n        var _a2;\n        return typeof defaultPrototype[method] === \"function\" && ((_a2 = defaultPrototype[method]) == null ? void 0 : _a2.toString().includes(\"[native code]\"));\n      }\n    )\n  );\n  if (isUntaintedAccessors && isUntaintedMethods) {\n    untaintedBasePrototype[key] = defaultObj.prototype;\n    return defaultObj.prototype;\n  }\n  try {\n    const iframeEl = document.createElement(\"iframe\");\n    document.body.appendChild(iframeEl);\n    const win = iframeEl.contentWindow;\n    if (!win) return defaultObj.prototype;\n    const untaintedObject = win[key].prototype;\n    document.body.removeChild(iframeEl);\n    if (!untaintedObject) return defaultPrototype;\n    return untaintedBasePrototype[key] = untaintedObject;\n  } catch {\n    return defaultPrototype;\n  }\n}\nconst untaintedAccessorCache = {};\nfunction getUntaintedAccessor(key, instance, accessor) {\n  var _a2;\n  const cacheKey = `${key}.${String(accessor)}`;\n  if (untaintedAccessorCache[cacheKey])\n    return untaintedAccessorCache[cacheKey].call(\n      instance\n    );\n  const untaintedPrototype = getUntaintedPrototype(key);\n  const untaintedAccessor = (_a2 = Object.getOwnPropertyDescriptor(\n    untaintedPrototype,\n    accessor\n  )) == null ? void 0 : _a2.get;\n  if (!untaintedAccessor) return instance[accessor];\n  untaintedAccessorCache[cacheKey] = untaintedAccessor;\n  return untaintedAccessor.call(instance);\n}\nconst untaintedMethodCache = {};\nfunction getUntaintedMethod(key, instance, method) {\n  const cacheKey = `${key}.${String(method)}`;\n  if (untaintedMethodCache[cacheKey])\n    return untaintedMethodCache[cacheKey].bind(\n      instance\n    );\n  const untaintedPrototype = getUntaintedPrototype(key);\n  const untaintedMethod = untaintedPrototype[method];\n  if (typeof untaintedMethod !== \"function\") return instance[method];\n  untaintedMethodCache[cacheKey] = untaintedMethod;\n  return untaintedMethod.bind(instance);\n}\nfunction childNodes(n2) {\n  return getUntaintedAccessor(\"Node\", n2, \"childNodes\");\n}\nfunction parentNode(n2) {\n  return getUntaintedAccessor(\"Node\", n2, \"parentNode\");\n}\nfunction parentElement(n2) {\n  return getUntaintedAccessor(\"Node\", n2, \"parentElement\");\n}\nfunction textContent(n2) {\n  return getUntaintedAccessor(\"Node\", n2, \"textContent\");\n}\nfunction contains(n2, other) {\n  return getUntaintedMethod(\"Node\", n2, \"contains\")(other);\n}\nfunction getRootNode(n2) {\n  return getUntaintedMethod(\"Node\", n2, \"getRootNode\")();\n}\nfunction host(n2) {\n  if (!n2 || !(\"host\" in n2)) return null;\n  return getUntaintedAccessor(\"ShadowRoot\", n2, \"host\");\n}\nfunction styleSheets(n2) {\n  return n2.styleSheets;\n}\nfunction shadowRoot(n2) {\n  if (!n2 || !(\"shadowRoot\" in n2)) return null;\n  return getUntaintedAccessor(\"Element\", n2, \"shadowRoot\");\n}\nfunction querySelector(n2, selectors) {\n  return getUntaintedAccessor(\"Element\", n2, \"querySelector\")(selectors);\n}\nfunction querySelectorAll(n2, selectors) {\n  return getUntaintedAccessor(\"Element\", n2, \"querySelectorAll\")(selectors);\n}\nfunction mutationObserverCtor() {\n  return getUntaintedPrototype(\"MutationObserver\").constructor;\n}\nconst index = {\n  childNodes,\n  parentNode,\n  parentElement,\n  textContent,\n  contains,\n  getRootNode,\n  host,\n  styleSheets,\n  shadowRoot,\n  querySelector,\n  querySelectorAll,\n  mutationObserver: mutationObserverCtor\n};\nfunction on(type, fn, target = document) {\n  const options = { capture: true, passive: true };\n  target.addEventListener(type, fn, options);\n  return () => target.removeEventListener(type, fn, options);\n}\nconst DEPARTED_MIRROR_ACCESS_WARNING = \"Please stop import mirror directly. Instead of that,\\r\\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\\r\\nor you can use record.mirror to access the mirror instance during recording.\";\nlet _mirror = {\n  map: {},\n  getId() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return -1;\n  },\n  getNode() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return null;\n  },\n  removeNodeFromMap() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  },\n  has() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return false;\n  },\n  reset() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  }\n};\nif (typeof window !== \"undefined\" && window.Proxy && window.Reflect) {\n  _mirror = new Proxy(_mirror, {\n    get(target, prop, receiver) {\n      if (prop === \"map\") {\n        console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n      }\n      return Reflect.get(target, prop, receiver);\n    }\n  });\n}\nfunction throttle(func, wait, options = {}) {\n  let timeout = null;\n  let previous = 0;\n  return function(...args) {\n    const now = Date.now();\n    if (!previous && options.leading === false) {\n      previous = now;\n    }\n    const remaining = wait - (now - previous);\n    const context = this;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      func.apply(context, args);\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(() => {\n        previous = options.leading === false ? 0 : Date.now();\n        timeout = null;\n        func.apply(context, args);\n      }, remaining);\n    }\n  };\n}\nfunction hookSetter(target, key, d, isRevoked, win = window) {\n  const original = win.Object.getOwnPropertyDescriptor(target, key);\n  win.Object.defineProperty(\n    target,\n    key,\n    isRevoked ? d : {\n      set(value) {\n        setTimeout(() => {\n          d.set.call(this, value);\n        }, 0);\n        if (original && original.set) {\n          original.set.call(this, value);\n        }\n      }\n    }\n  );\n  return () => hookSetter(target, key, original || {}, true);\n}\nfunction patch(source, name, replacement) {\n  try {\n    if (!(name in source)) {\n      return () => {\n      };\n    }\n    const original = source[name];\n    const wrapped = replacement(original);\n    if (typeof wrapped === \"function\") {\n      wrapped.prototype = wrapped.prototype || {};\n      Object.defineProperties(wrapped, {\n        __rrweb_original__: {\n          enumerable: false,\n          value: original\n        }\n      });\n    }\n    source[name] = wrapped;\n    return () => {\n      source[name] = original;\n    };\n  } catch {\n    return () => {\n    };\n  }\n}\nlet nowTimestamp = Date.now;\nif (!/* @__PURE__ */ /[1-9][0-9]{12}/.test(Date.now().toString())) {\n  nowTimestamp = () => (/* @__PURE__ */ new Date()).getTime();\n}\nfunction getWindowScroll(win) {\n  var _a2, _b, _c, _d;\n  const doc = win.document;\n  return {\n    left: doc.scrollingElement ? doc.scrollingElement.scrollLeft : win.pageXOffset !== void 0 ? win.pageXOffset : doc.documentElement.scrollLeft || (doc == null ? void 0 : doc.body) && ((_a2 = index.parentElement(doc.body)) == null ? void 0 : _a2.scrollLeft) || ((_b = doc == null ? void 0 : doc.body) == null ? void 0 : _b.scrollLeft) || 0,\n    top: doc.scrollingElement ? doc.scrollingElement.scrollTop : win.pageYOffset !== void 0 ? win.pageYOffset : (doc == null ? void 0 : doc.documentElement.scrollTop) || (doc == null ? void 0 : doc.body) && ((_c = index.parentElement(doc.body)) == null ? void 0 : _c.scrollTop) || ((_d = doc == null ? void 0 : doc.body) == null ? void 0 : _d.scrollTop) || 0\n  };\n}\nfunction getWindowHeight() {\n  return window.innerHeight || document.documentElement && document.documentElement.clientHeight || document.body && document.body.clientHeight;\n}\nfunction getWindowWidth() {\n  return window.innerWidth || document.documentElement && document.documentElement.clientWidth || document.body && document.body.clientWidth;\n}\nfunction closestElementOfNode(node2) {\n  if (!node2) {\n    return null;\n  }\n  const el = node2.nodeType === node2.ELEMENT_NODE ? node2 : index.parentElement(node2);\n  return el;\n}\nfunction isBlocked(node2, blockClass, blockSelector, checkAncestors) {\n  if (!node2) {\n    return false;\n  }\n  const el = closestElementOfNode(node2);\n  if (!el) {\n    return false;\n  }\n  try {\n    if (typeof blockClass === \"string\") {\n      if (el.classList.contains(blockClass)) return true;\n      if (checkAncestors && el.closest(\".\" + blockClass) !== null) return true;\n    } else {\n      if (classMatchesRegex(el, blockClass, checkAncestors)) return true;\n    }\n  } catch (e2) {\n  }\n  if (blockSelector) {\n    if (el.matches(blockSelector)) return true;\n    if (checkAncestors && el.closest(blockSelector) !== null) return true;\n  }\n  return false;\n}\nfunction isSerialized(n2, mirror2) {\n  return mirror2.getId(n2) !== -1;\n}\nfunction isIgnored(n2, mirror2, slimDOMOptions) {\n  if (n2.tagName === \"TITLE\" && slimDOMOptions.headTitleMutations) {\n    return true;\n  }\n  return mirror2.getId(n2) === IGNORED_NODE;\n}\nfunction isAncestorRemoved(target, mirror2) {\n  if (isShadowRoot(target)) {\n    return false;\n  }\n  const id = mirror2.getId(target);\n  if (!mirror2.has(id)) {\n    return true;\n  }\n  const parent = index.parentNode(target);\n  if (parent && parent.nodeType === target.DOCUMENT_NODE) {\n    return false;\n  }\n  if (!parent) {\n    return true;\n  }\n  return isAncestorRemoved(parent, mirror2);\n}\nfunction legacy_isTouchEvent(event) {\n  return Boolean(event.changedTouches);\n}\nfunction polyfill$1(win = window) {\n  if (\"NodeList\" in win && !win.NodeList.prototype.forEach) {\n    win.NodeList.prototype.forEach = Array.prototype.forEach;\n  }\n  if (\"DOMTokenList\" in win && !win.DOMTokenList.prototype.forEach) {\n    win.DOMTokenList.prototype.forEach = Array.prototype.forEach;\n  }\n}\nfunction isSerializedIframe(n2, mirror2) {\n  return Boolean(n2.nodeName === \"IFRAME\" && mirror2.getMeta(n2));\n}\nfunction isSerializedStylesheet(n2, mirror2) {\n  return Boolean(\n    n2.nodeName === \"LINK\" && n2.nodeType === n2.ELEMENT_NODE && n2.getAttribute && n2.getAttribute(\"rel\") === \"stylesheet\" && mirror2.getMeta(n2)\n  );\n}\nfunction hasShadowRoot(n2) {\n  if (!n2) return false;\n  if (n2 instanceof BaseRRNode && \"shadowRoot\" in n2) {\n    return Boolean(n2.shadowRoot);\n  }\n  return Boolean(index.shadowRoot(n2));\n}\nclass StyleSheetMirror {\n  constructor() {\n    __publicField(this, \"id\", 1);\n    __publicField(this, \"styleIDMap\", /* @__PURE__ */ new WeakMap());\n    __publicField(this, \"idStyleMap\", /* @__PURE__ */ new Map());\n  }\n  getId(stylesheet) {\n    return this.styleIDMap.get(stylesheet) ?? -1;\n  }\n  has(stylesheet) {\n    return this.styleIDMap.has(stylesheet);\n  }\n  /**\n   * @returns If the stylesheet is in the mirror, returns the id of the stylesheet. If not, return the new assigned id.\n   */\n  add(stylesheet, id) {\n    if (this.has(stylesheet)) return this.getId(stylesheet);\n    let newId;\n    if (id === void 0) {\n      newId = this.id++;\n    } else newId = id;\n    this.styleIDMap.set(stylesheet, newId);\n    this.idStyleMap.set(newId, stylesheet);\n    return newId;\n  }\n  getStyle(id) {\n    return this.idStyleMap.get(id) || null;\n  }\n  reset() {\n    this.styleIDMap = /* @__PURE__ */ new WeakMap();\n    this.idStyleMap = /* @__PURE__ */ new Map();\n    this.id = 1;\n  }\n  generateId() {\n    return this.id++;\n  }\n}\nfunction getShadowHost(n2) {\n  var _a2;\n  let shadowHost = null;\n  if (\"getRootNode\" in n2 && ((_a2 = index.getRootNode(n2)) == null ? void 0 : _a2.nodeType) === Node.DOCUMENT_FRAGMENT_NODE && index.host(index.getRootNode(n2)))\n    shadowHost = index.host(index.getRootNode(n2));\n  return shadowHost;\n}\nfunction getRootShadowHost(n2) {\n  let rootShadowHost = n2;\n  let shadowHost;\n  while (shadowHost = getShadowHost(rootShadowHost))\n    rootShadowHost = shadowHost;\n  return rootShadowHost;\n}\nfunction shadowHostInDom(n2) {\n  const doc = n2.ownerDocument;\n  if (!doc) return false;\n  const shadowHost = getRootShadowHost(n2);\n  return index.contains(doc, shadowHost);\n}\nfunction inDom(n2) {\n  const doc = n2.ownerDocument;\n  if (!doc) return false;\n  return index.contains(doc, n2) || shadowHostInDom(n2);\n}\nvar EventType = /* @__PURE__ */ ((EventType2) => {\n  EventType2[EventType2[\"DomContentLoaded\"] = 0] = \"DomContentLoaded\";\n  EventType2[EventType2[\"Load\"] = 1] = \"Load\";\n  EventType2[EventType2[\"FullSnapshot\"] = 2] = \"FullSnapshot\";\n  EventType2[EventType2[\"IncrementalSnapshot\"] = 3] = \"IncrementalSnapshot\";\n  EventType2[EventType2[\"Meta\"] = 4] = \"Meta\";\n  EventType2[EventType2[\"Custom\"] = 5] = \"Custom\";\n  EventType2[EventType2[\"Plugin\"] = 6] = \"Plugin\";\n  return EventType2;\n})(EventType || {});\nvar IncrementalSource = /* @__PURE__ */ ((IncrementalSource2) => {\n  IncrementalSource2[IncrementalSource2[\"Mutation\"] = 0] = \"Mutation\";\n  IncrementalSource2[IncrementalSource2[\"MouseMove\"] = 1] = \"MouseMove\";\n  IncrementalSource2[IncrementalSource2[\"MouseInteraction\"] = 2] = \"MouseInteraction\";\n  IncrementalSource2[IncrementalSource2[\"Scroll\"] = 3] = \"Scroll\";\n  IncrementalSource2[IncrementalSource2[\"ViewportResize\"] = 4] = \"ViewportResize\";\n  IncrementalSource2[IncrementalSource2[\"Input\"] = 5] = \"Input\";\n  IncrementalSource2[IncrementalSource2[\"TouchMove\"] = 6] = \"TouchMove\";\n  IncrementalSource2[IncrementalSource2[\"MediaInteraction\"] = 7] = \"MediaInteraction\";\n  IncrementalSource2[IncrementalSource2[\"StyleSheetRule\"] = 8] = \"StyleSheetRule\";\n  IncrementalSource2[IncrementalSource2[\"CanvasMutation\"] = 9] = \"CanvasMutation\";\n  IncrementalSource2[IncrementalSource2[\"Font\"] = 10] = \"Font\";\n  IncrementalSource2[IncrementalSource2[\"Log\"] = 11] = \"Log\";\n  IncrementalSource2[IncrementalSource2[\"Drag\"] = 12] = \"Drag\";\n  IncrementalSource2[IncrementalSource2[\"StyleDeclaration\"] = 13] = \"StyleDeclaration\";\n  IncrementalSource2[IncrementalSource2[\"Selection\"] = 14] = \"Selection\";\n  IncrementalSource2[IncrementalSource2[\"AdoptedStyleSheet\"] = 15] = \"AdoptedStyleSheet\";\n  IncrementalSource2[IncrementalSource2[\"CustomElement\"] = 16] = \"CustomElement\";\n  return IncrementalSource2;\n})(IncrementalSource || {});\nvar MouseInteractions = /* @__PURE__ */ ((MouseInteractions2) => {\n  MouseInteractions2[MouseInteractions2[\"MouseUp\"] = 0] = \"MouseUp\";\n  MouseInteractions2[MouseInteractions2[\"MouseDown\"] = 1] = \"MouseDown\";\n  MouseInteractions2[MouseInteractions2[\"Click\"] = 2] = \"Click\";\n  MouseInteractions2[MouseInteractions2[\"ContextMenu\"] = 3] = \"ContextMenu\";\n  MouseInteractions2[MouseInteractions2[\"DblClick\"] = 4] = \"DblClick\";\n  MouseInteractions2[MouseInteractions2[\"Focus\"] = 5] = \"Focus\";\n  MouseInteractions2[MouseInteractions2[\"Blur\"] = 6] = \"Blur\";\n  MouseInteractions2[MouseInteractions2[\"TouchStart\"] = 7] = \"TouchStart\";\n  MouseInteractions2[MouseInteractions2[\"TouchMove_Departed\"] = 8] = \"TouchMove_Departed\";\n  MouseInteractions2[MouseInteractions2[\"TouchEnd\"] = 9] = \"TouchEnd\";\n  MouseInteractions2[MouseInteractions2[\"TouchCancel\"] = 10] = \"TouchCancel\";\n  return MouseInteractions2;\n})(MouseInteractions || {});\nvar PointerTypes = /* @__PURE__ */ ((PointerTypes2) => {\n  PointerTypes2[PointerTypes2[\"Mouse\"] = 0] = \"Mouse\";\n  PointerTypes2[PointerTypes2[\"Pen\"] = 1] = \"Pen\";\n  PointerTypes2[PointerTypes2[\"Touch\"] = 2] = \"Touch\";\n  return PointerTypes2;\n})(PointerTypes || {});\nvar CanvasContext = /* @__PURE__ */ ((CanvasContext2) => {\n  CanvasContext2[CanvasContext2[\"2D\"] = 0] = \"2D\";\n  CanvasContext2[CanvasContext2[\"WebGL\"] = 1] = \"WebGL\";\n  CanvasContext2[CanvasContext2[\"WebGL2\"] = 2] = \"WebGL2\";\n  return CanvasContext2;\n})(CanvasContext || {});\nvar MediaInteractions = /* @__PURE__ */ ((MediaInteractions2) => {\n  MediaInteractions2[MediaInteractions2[\"Play\"] = 0] = \"Play\";\n  MediaInteractions2[MediaInteractions2[\"Pause\"] = 1] = \"Pause\";\n  MediaInteractions2[MediaInteractions2[\"Seeked\"] = 2] = \"Seeked\";\n  MediaInteractions2[MediaInteractions2[\"VolumeChange\"] = 3] = \"VolumeChange\";\n  MediaInteractions2[MediaInteractions2[\"RateChange\"] = 4] = \"RateChange\";\n  return MediaInteractions2;\n})(MediaInteractions || {});\nfunction isNodeInLinkedList(n2) {\n  return \"__ln\" in n2;\n}\nclass DoubleLinkedList {\n  constructor() {\n    __publicField(this, \"length\", 0);\n    __publicField(this, \"head\", null);\n    __publicField(this, \"tail\", null);\n  }\n  get(position) {\n    if (position >= this.length) {\n      throw new Error(\"Position outside of list range\");\n    }\n    let current = this.head;\n    for (let index2 = 0; index2 < position; index2++) {\n      current = (current == null ? void 0 : current.next) || null;\n    }\n    return current;\n  }\n  addNode(n2) {\n    const node2 = {\n      value: n2,\n      previous: null,\n      next: null\n    };\n    n2.__ln = node2;\n    if (n2.previousSibling && isNodeInLinkedList(n2.previousSibling)) {\n      const current = n2.previousSibling.__ln.next;\n      node2.next = current;\n      node2.previous = n2.previousSibling.__ln;\n      n2.previousSibling.__ln.next = node2;\n      if (current) {\n        current.previous = node2;\n      }\n    } else if (n2.nextSibling && isNodeInLinkedList(n2.nextSibling) && n2.nextSibling.__ln.previous) {\n      const current = n2.nextSibling.__ln.previous;\n      node2.previous = current;\n      node2.next = n2.nextSibling.__ln;\n      n2.nextSibling.__ln.previous = node2;\n      if (current) {\n        current.next = node2;\n      }\n    } else {\n      if (this.head) {\n        this.head.previous = node2;\n      }\n      node2.next = this.head;\n      this.head = node2;\n    }\n    if (node2.next === null) {\n      this.tail = node2;\n    }\n    this.length++;\n  }\n  removeNode(n2) {\n    const current = n2.__ln;\n    if (!this.head) {\n      return;\n    }\n    if (!current.previous) {\n      this.head = current.next;\n      if (this.head) {\n        this.head.previous = null;\n      } else {\n        this.tail = null;\n      }\n    } else {\n      current.previous.next = current.next;\n      if (current.next) {\n        current.next.previous = current.previous;\n      } else {\n        this.tail = current.previous;\n      }\n    }\n    if (n2.__ln) {\n      delete n2.__ln;\n    }\n    this.length--;\n  }\n}\nconst moveKey = (id, parentId) => `${id}@${parentId}`;\nclass MutationBuffer {\n  constructor() {\n    __publicField(this, \"frozen\", false);\n    __publicField(this, \"locked\", false);\n    __publicField(this, \"texts\", []);\n    __publicField(this, \"attributes\", []);\n    __publicField(this, \"attributeMap\", /* @__PURE__ */ new WeakMap());\n    __publicField(this, \"removes\", []);\n    __publicField(this, \"mapRemoves\", []);\n    __publicField(this, \"movedMap\", {});\n    __publicField(this, \"addedSet\", /* @__PURE__ */ new Set());\n    __publicField(this, \"movedSet\", /* @__PURE__ */ new Set());\n    __publicField(this, \"droppedSet\", /* @__PURE__ */ new Set());\n    __publicField(this, \"mutationCb\");\n    __publicField(this, \"blockClass\");\n    __publicField(this, \"blockSelector\");\n    __publicField(this, \"maskTextClass\");\n    __publicField(this, \"maskTextSelector\");\n    __publicField(this, \"inlineStylesheet\");\n    __publicField(this, \"maskInputOptions\");\n    __publicField(this, \"maskTextFn\");\n    __publicField(this, \"maskInputFn\");\n    __publicField(this, \"keepIframeSrcFn\");\n    __publicField(this, \"recordCanvas\");\n    __publicField(this, \"inlineImages\");\n    __publicField(this, \"slimDOMOptions\");\n    __publicField(this, \"dataURLOptions\");\n    __publicField(this, \"doc\");\n    __publicField(this, \"mirror\");\n    __publicField(this, \"iframeManager\");\n    __publicField(this, \"stylesheetManager\");\n    __publicField(this, \"shadowDomManager\");\n    __publicField(this, \"canvasManager\");\n    __publicField(this, \"processedNodeManager\");\n    __publicField(this, \"unattachedDoc\");\n    __publicField(this, \"processMutations\", (mutations) => {\n      mutations.forEach(this.processMutation);\n      this.emit();\n    });\n    __publicField(this, \"emit\", () => {\n      if (this.frozen || this.locked) {\n        return;\n      }\n      const adds = [];\n      const addedIds = /* @__PURE__ */ new Set();\n      const addList = new DoubleLinkedList();\n      const getNextId = (n2) => {\n        let ns = n2;\n        let nextId = IGNORED_NODE;\n        while (nextId === IGNORED_NODE) {\n          ns = ns && ns.nextSibling;\n          nextId = ns && this.mirror.getId(ns);\n        }\n        return nextId;\n      };\n      const pushAdd = (n2) => {\n        const parent = index.parentNode(n2);\n        if (!parent || !inDom(n2) || parent.tagName === \"TEXTAREA\") {\n          return;\n        }\n        const parentId = isShadowRoot(parent) ? this.mirror.getId(getShadowHost(n2)) : this.mirror.getId(parent);\n        const nextId = getNextId(n2);\n        if (parentId === -1 || nextId === -1) {\n          return addList.addNode(n2);\n        }\n        const sn = serializeNodeWithId(n2, {\n          doc: this.doc,\n          mirror: this.mirror,\n          blockClass: this.blockClass,\n          blockSelector: this.blockSelector,\n          maskTextClass: this.maskTextClass,\n          maskTextSelector: this.maskTextSelector,\n          skipChild: true,\n          newlyAddedElement: true,\n          inlineStylesheet: this.inlineStylesheet,\n          maskInputOptions: this.maskInputOptions,\n          maskTextFn: this.maskTextFn,\n          maskInputFn: this.maskInputFn,\n          slimDOMOptions: this.slimDOMOptions,\n          dataURLOptions: this.dataURLOptions,\n          recordCanvas: this.recordCanvas,\n          inlineImages: this.inlineImages,\n          onSerialize: (currentN) => {\n            if (isSerializedIframe(currentN, this.mirror)) {\n              this.iframeManager.addIframe(currentN);\n            }\n            if (isSerializedStylesheet(currentN, this.mirror)) {\n              this.stylesheetManager.trackLinkElement(\n                currentN\n              );\n            }\n            if (hasShadowRoot(n2)) {\n              this.shadowDomManager.addShadowRoot(index.shadowRoot(n2), this.doc);\n            }\n          },\n          onIframeLoad: (iframe, childSn) => {\n            this.iframeManager.attachIframe(iframe, childSn);\n            this.shadowDomManager.observeAttachShadow(iframe);\n          },\n          onStylesheetLoad: (link, childSn) => {\n            this.stylesheetManager.attachLinkElement(link, childSn);\n          }\n        });\n        if (sn) {\n          adds.push({\n            parentId,\n            nextId,\n            node: sn\n          });\n          addedIds.add(sn.id);\n        }\n      };\n      while (this.mapRemoves.length) {\n        this.mirror.removeNodeFromMap(this.mapRemoves.shift());\n      }\n      for (const n2 of this.movedSet) {\n        if (isParentRemoved(this.removes, n2, this.mirror) && !this.movedSet.has(index.parentNode(n2))) {\n          continue;\n        }\n        pushAdd(n2);\n      }\n      for (const n2 of this.addedSet) {\n        if (!isAncestorInSet(this.droppedSet, n2) && !isParentRemoved(this.removes, n2, this.mirror)) {\n          pushAdd(n2);\n        } else if (isAncestorInSet(this.movedSet, n2)) {\n          pushAdd(n2);\n        } else {\n          this.droppedSet.add(n2);\n        }\n      }\n      let candidate = null;\n      while (addList.length) {\n        let node2 = null;\n        if (candidate) {\n          const parentId = this.mirror.getId(index.parentNode(candidate.value));\n          const nextId = getNextId(candidate.value);\n          if (parentId !== -1 && nextId !== -1) {\n            node2 = candidate;\n          }\n        }\n        if (!node2) {\n          let tailNode = addList.tail;\n          while (tailNode) {\n            const _node = tailNode;\n            tailNode = tailNode.previous;\n            if (_node) {\n              const parentId = this.mirror.getId(index.parentNode(_node.value));\n              const nextId = getNextId(_node.value);\n              if (nextId === -1) continue;\n              else if (parentId !== -1) {\n                node2 = _node;\n                break;\n              } else {\n                const unhandledNode = _node.value;\n                const parent = index.parentNode(unhandledNode);\n                if (parent && parent.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n                  const shadowHost = index.host(parent);\n                  const parentId2 = this.mirror.getId(shadowHost);\n                  if (parentId2 !== -1) {\n                    node2 = _node;\n                    break;\n                  }\n                }\n              }\n            }\n          }\n        }\n        if (!node2) {\n          while (addList.head) {\n            addList.removeNode(addList.head.value);\n          }\n          break;\n        }\n        candidate = node2.previous;\n        addList.removeNode(node2.value);\n        pushAdd(node2.value);\n      }\n      const payload = {\n        texts: this.texts.map((text) => {\n          const n2 = text.node;\n          const parent = index.parentNode(n2);\n          if (parent && parent.tagName === \"TEXTAREA\") {\n            this.genTextAreaValueMutation(parent);\n          }\n          return {\n            id: this.mirror.getId(n2),\n            value: text.value\n          };\n        }).filter((text) => !addedIds.has(text.id)).filter((text) => this.mirror.has(text.id)),\n        attributes: this.attributes.map((attribute) => {\n          const { attributes } = attribute;\n          if (typeof attributes.style === \"string\") {\n            const diffAsStr = JSON.stringify(attribute.styleDiff);\n            const unchangedAsStr = JSON.stringify(attribute._unchangedStyles);\n            if (diffAsStr.length < attributes.style.length) {\n              if ((diffAsStr + unchangedAsStr).split(\"var(\").length === attributes.style.split(\"var(\").length) {\n                attributes.style = attribute.styleDiff;\n              }\n            }\n          }\n          return {\n            id: this.mirror.getId(attribute.node),\n            attributes\n          };\n        }).filter((attribute) => !addedIds.has(attribute.id)).filter((attribute) => this.mirror.has(attribute.id)),\n        removes: this.removes,\n        adds\n      };\n      if (!payload.texts.length && !payload.attributes.length && !payload.removes.length && !payload.adds.length) {\n        return;\n      }\n      this.texts = [];\n      this.attributes = [];\n      this.attributeMap = /* @__PURE__ */ new WeakMap();\n      this.removes = [];\n      this.addedSet = /* @__PURE__ */ new Set();\n      this.movedSet = /* @__PURE__ */ new Set();\n      this.droppedSet = /* @__PURE__ */ new Set();\n      this.movedMap = {};\n      this.mutationCb(payload);\n    });\n    __publicField(this, \"genTextAreaValueMutation\", (textarea) => {\n      let item = this.attributeMap.get(textarea);\n      if (!item) {\n        item = {\n          node: textarea,\n          attributes: {},\n          styleDiff: {},\n          _unchangedStyles: {}\n        };\n        this.attributes.push(item);\n        this.attributeMap.set(textarea, item);\n      }\n      item.attributes.value = Array.from(\n        index.childNodes(textarea),\n        (cn) => index.textContent(cn) || \"\"\n      ).join(\"\");\n    });\n    __publicField(this, \"processMutation\", (m) => {\n      if (isIgnored(m.target, this.mirror, this.slimDOMOptions)) {\n        return;\n      }\n      switch (m.type) {\n        case \"characterData\": {\n          const value = index.textContent(m.target);\n          if (!isBlocked(m.target, this.blockClass, this.blockSelector, false) && value !== m.oldValue) {\n            this.texts.push({\n              value: needMaskingText(\n                m.target,\n                this.maskTextClass,\n                this.maskTextSelector,\n                true\n                // checkAncestors\n              ) && value ? this.maskTextFn ? this.maskTextFn(value, closestElementOfNode(m.target)) : value.replace(/[\\S]/g, \"*\") : value,\n              node: m.target\n            });\n          }\n          break;\n        }\n        case \"attributes\": {\n          const target = m.target;\n          let attributeName = m.attributeName;\n          let value = m.target.getAttribute(attributeName);\n          if (attributeName === \"value\") {\n            const type = getInputType(target);\n            value = maskInputValue({\n              element: target,\n              maskInputOptions: this.maskInputOptions,\n              tagName: target.tagName,\n              type,\n              value,\n              maskInputFn: this.maskInputFn\n            });\n          }\n          if (isBlocked(m.target, this.blockClass, this.blockSelector, false) || value === m.oldValue) {\n            return;\n          }\n          let item = this.attributeMap.get(m.target);\n          if (target.tagName === \"IFRAME\" && attributeName === \"src\" && !this.keepIframeSrcFn(value)) {\n            if (!target.contentDocument) {\n              attributeName = \"rr_src\";\n            } else {\n              return;\n            }\n          }\n          if (!item) {\n            item = {\n              node: m.target,\n              attributes: {},\n              styleDiff: {},\n              _unchangedStyles: {}\n            };\n            this.attributes.push(item);\n            this.attributeMap.set(m.target, item);\n          }\n          if (attributeName === \"type\" && target.tagName === \"INPUT\" && (m.oldValue || \"\").toLowerCase() === \"password\") {\n            target.setAttribute(\"data-rr-is-password\", \"true\");\n          }\n          if (!ignoreAttribute(target.tagName, attributeName)) {\n            item.attributes[attributeName] = transformAttribute(\n              this.doc,\n              toLowerCase(target.tagName),\n              toLowerCase(attributeName),\n              value\n            );\n            if (attributeName === \"style\") {\n              if (!this.unattachedDoc) {\n                try {\n                  this.unattachedDoc = document.implementation.createHTMLDocument();\n                } catch (e2) {\n                  this.unattachedDoc = this.doc;\n                }\n              }\n              const old = this.unattachedDoc.createElement(\"span\");\n              if (m.oldValue) {\n                old.setAttribute(\"style\", m.oldValue);\n              }\n              for (const pname of Array.from(target.style)) {\n                const newValue = target.style.getPropertyValue(pname);\n                const newPriority = target.style.getPropertyPriority(pname);\n                if (newValue !== old.style.getPropertyValue(pname) || newPriority !== old.style.getPropertyPriority(pname)) {\n                  if (newPriority === \"\") {\n                    item.styleDiff[pname] = newValue;\n                  } else {\n                    item.styleDiff[pname] = [newValue, newPriority];\n                  }\n                } else {\n                  item._unchangedStyles[pname] = [newValue, newPriority];\n                }\n              }\n              for (const pname of Array.from(old.style)) {\n                if (target.style.getPropertyValue(pname) === \"\") {\n                  item.styleDiff[pname] = false;\n                }\n              }\n            } else if (attributeName === \"open\" && target.tagName === \"DIALOG\") {\n              if (target.matches(\"dialog:modal\")) {\n                item.attributes[\"rr_open_mode\"] = \"modal\";\n              } else {\n                item.attributes[\"rr_open_mode\"] = \"non-modal\";\n              }\n            }\n          }\n          break;\n        }\n        case \"childList\": {\n          if (isBlocked(m.target, this.blockClass, this.blockSelector, true))\n            return;\n          if (m.target.tagName === \"TEXTAREA\") {\n            this.genTextAreaValueMutation(m.target);\n            return;\n          }\n          m.addedNodes.forEach((n2) => this.genAdds(n2, m.target));\n          m.removedNodes.forEach((n2) => {\n            const nodeId = this.mirror.getId(n2);\n            const parentId = isShadowRoot(m.target) ? this.mirror.getId(index.host(m.target)) : this.mirror.getId(m.target);\n            if (isBlocked(m.target, this.blockClass, this.blockSelector, false) || isIgnored(n2, this.mirror, this.slimDOMOptions) || !isSerialized(n2, this.mirror)) {\n              return;\n            }\n            if (this.addedSet.has(n2)) {\n              deepDelete(this.addedSet, n2);\n              this.droppedSet.add(n2);\n            } else if (this.addedSet.has(m.target) && nodeId === -1) ;\n            else if (isAncestorRemoved(m.target, this.mirror)) ;\n            else if (this.movedSet.has(n2) && this.movedMap[moveKey(nodeId, parentId)]) {\n              deepDelete(this.movedSet, n2);\n            } else {\n              this.removes.push({\n                parentId,\n                id: nodeId,\n                isShadow: isShadowRoot(m.target) && isNativeShadowDom(m.target) ? true : void 0\n              });\n            }\n            this.mapRemoves.push(n2);\n          });\n          break;\n        }\n      }\n    });\n    __publicField(this, \"genAdds\", (n2, target) => {\n      if (this.processedNodeManager.inOtherBuffer(n2, this)) return;\n      if (this.addedSet.has(n2) || this.movedSet.has(n2)) return;\n      if (this.mirror.hasNode(n2)) {\n        if (isIgnored(n2, this.mirror, this.slimDOMOptions)) {\n          return;\n        }\n        this.movedSet.add(n2);\n        let targetId = null;\n        if (target && this.mirror.hasNode(target)) {\n          targetId = this.mirror.getId(target);\n        }\n        if (targetId && targetId !== -1) {\n          this.movedMap[moveKey(this.mirror.getId(n2), targetId)] = true;\n        }\n      } else {\n        this.addedSet.add(n2);\n        this.droppedSet.delete(n2);\n      }\n      if (!isBlocked(n2, this.blockClass, this.blockSelector, false)) {\n        index.childNodes(n2).forEach((childN) => this.genAdds(childN));\n        if (hasShadowRoot(n2)) {\n          index.childNodes(index.shadowRoot(n2)).forEach((childN) => {\n            this.processedNodeManager.add(childN, this);\n            this.genAdds(childN, n2);\n          });\n        }\n      }\n    });\n  }\n  init(options) {\n    [\n      \"mutationCb\",\n      \"blockClass\",\n      \"blockSelector\",\n      \"maskTextClass\",\n      \"maskTextSelector\",\n      \"inlineStylesheet\",\n      \"maskInputOptions\",\n      \"maskTextFn\",\n      \"maskInputFn\",\n      \"keepIframeSrcFn\",\n      \"recordCanvas\",\n      \"inlineImages\",\n      \"slimDOMOptions\",\n      \"dataURLOptions\",\n      \"doc\",\n      \"mirror\",\n      \"iframeManager\",\n      \"stylesheetManager\",\n      \"shadowDomManager\",\n      \"canvasManager\",\n      \"processedNodeManager\"\n    ].forEach((key) => {\n      this[key] = options[key];\n    });\n  }\n  freeze() {\n    this.frozen = true;\n    this.canvasManager.freeze();\n  }\n  unfreeze() {\n    this.frozen = false;\n    this.canvasManager.unfreeze();\n    this.emit();\n  }\n  isFrozen() {\n    return this.frozen;\n  }\n  lock() {\n    this.locked = true;\n    this.canvasManager.lock();\n  }\n  unlock() {\n    this.locked = false;\n    this.canvasManager.unlock();\n    this.emit();\n  }\n  reset() {\n    this.shadowDomManager.reset();\n    this.canvasManager.reset();\n  }\n}\nfunction deepDelete(addsSet, n2) {\n  addsSet.delete(n2);\n  index.childNodes(n2).forEach((childN) => deepDelete(addsSet, childN));\n}\nfunction isParentRemoved(removes, n2, mirror2) {\n  if (removes.length === 0) return false;\n  return _isParentRemoved(removes, n2, mirror2);\n}\nfunction _isParentRemoved(removes, n2, mirror2) {\n  let node2 = index.parentNode(n2);\n  while (node2) {\n    const parentId = mirror2.getId(node2);\n    if (removes.some((r2) => r2.id === parentId)) {\n      return true;\n    }\n    node2 = index.parentNode(node2);\n  }\n  return false;\n}\nfunction isAncestorInSet(set, n2) {\n  if (set.size === 0) return false;\n  return _isAncestorInSet(set, n2);\n}\nfunction _isAncestorInSet(set, n2) {\n  const parent = index.parentNode(n2);\n  if (!parent) {\n    return false;\n  }\n  if (set.has(parent)) {\n    return true;\n  }\n  return _isAncestorInSet(set, parent);\n}\nlet errorHandler;\nfunction registerErrorHandler(handler) {\n  errorHandler = handler;\n}\nfunction unregisterErrorHandler() {\n  errorHandler = void 0;\n}\nconst callbackWrapper = (cb) => {\n  if (!errorHandler) {\n    return cb;\n  }\n  const rrwebWrapped = (...rest) => {\n    try {\n      return cb(...rest);\n    } catch (error) {\n      if (errorHandler && errorHandler(error) === true) {\n        return;\n      }\n      throw error;\n    }\n  };\n  return rrwebWrapped;\n};\nconst mutationBuffers = [];\nfunction getEventTarget(event) {\n  try {\n    if (\"composedPath\" in event) {\n      const path = event.composedPath();\n      if (path.length) {\n        return path[0];\n      }\n    } else if (\"path\" in event && event.path.length) {\n      return event.path[0];\n    }\n  } catch {\n  }\n  return event && event.target;\n}\nfunction initMutationObserver(options, rootEl) {\n  const mutationBuffer = new MutationBuffer();\n  mutationBuffers.push(mutationBuffer);\n  mutationBuffer.init(options);\n  const observer = new (mutationObserverCtor())(\n    callbackWrapper(mutationBuffer.processMutations.bind(mutationBuffer))\n  );\n  observer.observe(rootEl, {\n    attributes: true,\n    attributeOldValue: true,\n    characterData: true,\n    characterDataOldValue: true,\n    childList: true,\n    subtree: true\n  });\n  return observer;\n}\nfunction initMoveObserver({\n  mousemoveCb,\n  sampling,\n  doc,\n  mirror: mirror2\n}) {\n  if (sampling.mousemove === false) {\n    return () => {\n    };\n  }\n  const threshold = typeof sampling.mousemove === \"number\" ? sampling.mousemove : 50;\n  const callbackThreshold = typeof sampling.mousemoveCallback === \"number\" ? sampling.mousemoveCallback : 500;\n  let positions = [];\n  let timeBaseline;\n  const wrappedCb = throttle(\n    callbackWrapper(\n      (source) => {\n        const totalOffset = Date.now() - timeBaseline;\n        mousemoveCb(\n          positions.map((p) => {\n            p.timeOffset -= totalOffset;\n            return p;\n          }),\n          source\n        );\n        positions = [];\n        timeBaseline = null;\n      }\n    ),\n    callbackThreshold\n  );\n  const updatePosition = callbackWrapper(\n    throttle(\n      callbackWrapper((evt) => {\n        const target = getEventTarget(evt);\n        const { clientX, clientY } = legacy_isTouchEvent(evt) ? evt.changedTouches[0] : evt;\n        if (!timeBaseline) {\n          timeBaseline = nowTimestamp();\n        }\n        positions.push({\n          x: clientX,\n          y: clientY,\n          id: mirror2.getId(target),\n          timeOffset: nowTimestamp() - timeBaseline\n        });\n        wrappedCb(\n          typeof DragEvent !== \"undefined\" && evt instanceof DragEvent ? IncrementalSource.Drag : evt instanceof MouseEvent ? IncrementalSource.MouseMove : IncrementalSource.TouchMove\n        );\n      }),\n      threshold,\n      {\n        trailing: false\n      }\n    )\n  );\n  const handlers = [\n    on(\"mousemove\", updatePosition, doc),\n    on(\"touchmove\", updatePosition, doc),\n    on(\"drag\", updatePosition, doc)\n  ];\n  return callbackWrapper(() => {\n    handlers.forEach((h) => h());\n  });\n}\nfunction initMouseInteractionObserver({\n  mouseInteractionCb,\n  doc,\n  mirror: mirror2,\n  blockClass,\n  blockSelector,\n  sampling\n}) {\n  if (sampling.mouseInteraction === false) {\n    return () => {\n    };\n  }\n  const disableMap = sampling.mouseInteraction === true || sampling.mouseInteraction === void 0 ? {} : sampling.mouseInteraction;\n  const handlers = [];\n  let currentPointerType = null;\n  const getHandler = (eventKey) => {\n    return (event) => {\n      const target = getEventTarget(event);\n      if (isBlocked(target, blockClass, blockSelector, true)) {\n        return;\n      }\n      let pointerType = null;\n      let thisEventKey = eventKey;\n      if (\"pointerType\" in event) {\n        switch (event.pointerType) {\n          case \"mouse\":\n            pointerType = PointerTypes.Mouse;\n            break;\n          case \"touch\":\n            pointerType = PointerTypes.Touch;\n            break;\n          case \"pen\":\n            pointerType = PointerTypes.Pen;\n            break;\n        }\n        if (pointerType === PointerTypes.Touch) {\n          if (MouseInteractions[eventKey] === MouseInteractions.MouseDown) {\n            thisEventKey = \"TouchStart\";\n          } else if (MouseInteractions[eventKey] === MouseInteractions.MouseUp) {\n            thisEventKey = \"TouchEnd\";\n          }\n        } else if (pointerType === PointerTypes.Pen) ;\n      } else if (legacy_isTouchEvent(event)) {\n        pointerType = PointerTypes.Touch;\n      }\n      if (pointerType !== null) {\n        currentPointerType = pointerType;\n        if (thisEventKey.startsWith(\"Touch\") && pointerType === PointerTypes.Touch || thisEventKey.startsWith(\"Mouse\") && pointerType === PointerTypes.Mouse) {\n          pointerType = null;\n        }\n      } else if (MouseInteractions[eventKey] === MouseInteractions.Click) {\n        pointerType = currentPointerType;\n        currentPointerType = null;\n      }\n      const e2 = legacy_isTouchEvent(event) ? event.changedTouches[0] : event;\n      if (!e2) {\n        return;\n      }\n      const id = mirror2.getId(target);\n      const { clientX, clientY } = e2;\n      callbackWrapper(mouseInteractionCb)({\n        type: MouseInteractions[thisEventKey],\n        id,\n        x: clientX,\n        y: clientY,\n        ...pointerType !== null && { pointerType }\n      });\n    };\n  };\n  Object.keys(MouseInteractions).filter(\n    (key) => Number.isNaN(Number(key)) && !key.endsWith(\"_Departed\") && disableMap[key] !== false\n  ).forEach((eventKey) => {\n    let eventName = toLowerCase(eventKey);\n    const handler = getHandler(eventKey);\n    if (window.PointerEvent) {\n      switch (MouseInteractions[eventKey]) {\n        case MouseInteractions.MouseDown:\n        case MouseInteractions.MouseUp:\n          eventName = eventName.replace(\n            \"mouse\",\n            \"pointer\"\n          );\n          break;\n        case MouseInteractions.TouchStart:\n        case MouseInteractions.TouchEnd:\n          return;\n      }\n    }\n    handlers.push(on(eventName, handler, doc));\n  });\n  return callbackWrapper(() => {\n    handlers.forEach((h) => h());\n  });\n}\nfunction initScrollObserver({\n  scrollCb,\n  doc,\n  mirror: mirror2,\n  blockClass,\n  blockSelector,\n  sampling\n}) {\n  const updatePosition = callbackWrapper(\n    throttle(\n      callbackWrapper((evt) => {\n        const target = getEventTarget(evt);\n        if (!target || isBlocked(target, blockClass, blockSelector, true)) {\n          return;\n        }\n        const id = mirror2.getId(target);\n        if (target === doc && doc.defaultView) {\n          const scrollLeftTop = getWindowScroll(doc.defaultView);\n          scrollCb({\n            id,\n            x: scrollLeftTop.left,\n            y: scrollLeftTop.top\n          });\n        } else {\n          scrollCb({\n            id,\n            x: target.scrollLeft,\n            y: target.scrollTop\n          });\n        }\n      }),\n      sampling.scroll || 100\n    )\n  );\n  return on(\"scroll\", updatePosition, doc);\n}\nfunction initViewportResizeObserver({ viewportResizeCb }, { win }) {\n  let lastH = -1;\n  let lastW = -1;\n  const updateDimension = callbackWrapper(\n    throttle(\n      callbackWrapper(() => {\n        const height = getWindowHeight();\n        const width = getWindowWidth();\n        if (lastH !== height || lastW !== width) {\n          viewportResizeCb({\n            width: Number(width),\n            height: Number(height)\n          });\n          lastH = height;\n          lastW = width;\n        }\n      }),\n      200\n    )\n  );\n  return on(\"resize\", updateDimension, win);\n}\nconst INPUT_TAGS = [\"INPUT\", \"TEXTAREA\", \"SELECT\"];\nconst lastInputValueMap = /* @__PURE__ */ new WeakMap();\nfunction initInputObserver({\n  inputCb,\n  doc,\n  mirror: mirror2,\n  blockClass,\n  blockSelector,\n  ignoreClass,\n  ignoreSelector,\n  maskInputOptions,\n  maskInputFn,\n  sampling,\n  userTriggeredOnInput\n}) {\n  function eventHandler(event) {\n    let target = getEventTarget(event);\n    const userTriggered = event.isTrusted;\n    const tagName = target && target.tagName;\n    if (target && tagName === \"OPTION\") {\n      target = index.parentElement(target);\n    }\n    if (!target || !tagName || INPUT_TAGS.indexOf(tagName) < 0 || isBlocked(target, blockClass, blockSelector, true)) {\n      return;\n    }\n    if (target.classList.contains(ignoreClass) || ignoreSelector && target.matches(ignoreSelector)) {\n      return;\n    }\n    let text = target.value;\n    let isChecked = false;\n    const type = getInputType(target) || \"\";\n    if (type === \"radio\" || type === \"checkbox\") {\n      isChecked = target.checked;\n    } else if (maskInputOptions[tagName.toLowerCase()] || maskInputOptions[type]) {\n      text = maskInputValue({\n        element: target,\n        maskInputOptions,\n        tagName,\n        type,\n        value: text,\n        maskInputFn\n      });\n    }\n    cbWithDedup(\n      target,\n      userTriggeredOnInput ? { text, isChecked, userTriggered } : { text, isChecked }\n    );\n    const name = target.name;\n    if (type === \"radio\" && name && isChecked) {\n      doc.querySelectorAll(`input[type=\"radio\"][name=\"${name}\"]`).forEach((el) => {\n        if (el !== target) {\n          const text2 = el.value;\n          cbWithDedup(\n            el,\n            userTriggeredOnInput ? { text: text2, isChecked: !isChecked, userTriggered: false } : { text: text2, isChecked: !isChecked }\n          );\n        }\n      });\n    }\n  }\n  function cbWithDedup(target, v2) {\n    const lastInputValue = lastInputValueMap.get(target);\n    if (!lastInputValue || lastInputValue.text !== v2.text || lastInputValue.isChecked !== v2.isChecked) {\n      lastInputValueMap.set(target, v2);\n      const id = mirror2.getId(target);\n      callbackWrapper(inputCb)({\n        ...v2,\n        id\n      });\n    }\n  }\n  const events = sampling.input === \"last\" ? [\"change\"] : [\"input\", \"change\"];\n  const handlers = events.map(\n    (eventName) => on(eventName, callbackWrapper(eventHandler), doc)\n  );\n  const currentWindow = doc.defaultView;\n  if (!currentWindow) {\n    return () => {\n      handlers.forEach((h) => h());\n    };\n  }\n  const propertyDescriptor = currentWindow.Object.getOwnPropertyDescriptor(\n    currentWindow.HTMLInputElement.prototype,\n    \"value\"\n  );\n  const hookProperties = [\n    [currentWindow.HTMLInputElement.prototype, \"value\"],\n    [currentWindow.HTMLInputElement.prototype, \"checked\"],\n    [currentWindow.HTMLSelectElement.prototype, \"value\"],\n    [currentWindow.HTMLTextAreaElement.prototype, \"value\"],\n    // Some UI library use selectedIndex to set select value\n    [currentWindow.HTMLSelectElement.prototype, \"selectedIndex\"],\n    [currentWindow.HTMLOptionElement.prototype, \"selected\"]\n  ];\n  if (propertyDescriptor && propertyDescriptor.set) {\n    handlers.push(\n      ...hookProperties.map(\n        (p) => hookSetter(\n          p[0],\n          p[1],\n          {\n            set() {\n              callbackWrapper(eventHandler)({\n                target: this,\n                isTrusted: false\n                // userTriggered to false as this could well be programmatic\n              });\n            }\n          },\n          false,\n          currentWindow\n        )\n      )\n    );\n  }\n  return callbackWrapper(() => {\n    handlers.forEach((h) => h());\n  });\n}\nfunction getNestedCSSRulePositions(rule2) {\n  const positions = [];\n  function recurse(childRule, pos) {\n    if (hasNestedCSSRule(\"CSSGroupingRule\") && childRule.parentRule instanceof CSSGroupingRule || hasNestedCSSRule(\"CSSMediaRule\") && childRule.parentRule instanceof CSSMediaRule || hasNestedCSSRule(\"CSSSupportsRule\") && childRule.parentRule instanceof CSSSupportsRule || hasNestedCSSRule(\"CSSConditionRule\") && childRule.parentRule instanceof CSSConditionRule) {\n      const rules2 = Array.from(\n        childRule.parentRule.cssRules\n      );\n      const index2 = rules2.indexOf(childRule);\n      pos.unshift(index2);\n    } else if (childRule.parentStyleSheet) {\n      const rules2 = Array.from(childRule.parentStyleSheet.cssRules);\n      const index2 = rules2.indexOf(childRule);\n      pos.unshift(index2);\n    }\n    return pos;\n  }\n  return recurse(rule2, positions);\n}\nfunction getIdAndStyleId(sheet, mirror2, styleMirror) {\n  let id, styleId;\n  if (!sheet) return {};\n  if (sheet.ownerNode) id = mirror2.getId(sheet.ownerNode);\n  else styleId = styleMirror.getId(sheet);\n  return {\n    styleId,\n    id\n  };\n}\nfunction initStyleSheetObserver({ styleSheetRuleCb, mirror: mirror2, stylesheetManager }, { win }) {\n  if (!win.CSSStyleSheet || !win.CSSStyleSheet.prototype) {\n    return () => {\n    };\n  }\n  const insertRule = win.CSSStyleSheet.prototype.insertRule;\n  win.CSSStyleSheet.prototype.insertRule = new Proxy(insertRule, {\n    apply: callbackWrapper(\n      (target, thisArg, argumentsList) => {\n        const [rule2, index2] = argumentsList;\n        const { id, styleId } = getIdAndStyleId(\n          thisArg,\n          mirror2,\n          stylesheetManager.styleMirror\n        );\n        if (id && id !== -1 || styleId && styleId !== -1) {\n          styleSheetRuleCb({\n            id,\n            styleId,\n            adds: [{ rule: rule2, index: index2 }]\n          });\n        }\n        return target.apply(thisArg, argumentsList);\n      }\n    )\n  });\n  win.CSSStyleSheet.prototype.addRule = function(selector, styleBlock, index2 = this.cssRules.length) {\n    const rule2 = `${selector} { ${styleBlock} }`;\n    return win.CSSStyleSheet.prototype.insertRule.apply(this, [rule2, index2]);\n  };\n  const deleteRule = win.CSSStyleSheet.prototype.deleteRule;\n  win.CSSStyleSheet.prototype.deleteRule = new Proxy(deleteRule, {\n    apply: callbackWrapper(\n      (target, thisArg, argumentsList) => {\n        const [index2] = argumentsList;\n        const { id, styleId } = getIdAndStyleId(\n          thisArg,\n          mirror2,\n          stylesheetManager.styleMirror\n        );\n        if (id && id !== -1 || styleId && styleId !== -1) {\n          styleSheetRuleCb({\n            id,\n            styleId,\n            removes: [{ index: index2 }]\n          });\n        }\n        return target.apply(thisArg, argumentsList);\n      }\n    )\n  });\n  win.CSSStyleSheet.prototype.removeRule = function(index2) {\n    return win.CSSStyleSheet.prototype.deleteRule.apply(this, [index2]);\n  };\n  let replace;\n  if (win.CSSStyleSheet.prototype.replace) {\n    replace = win.CSSStyleSheet.prototype.replace;\n    win.CSSStyleSheet.prototype.replace = new Proxy(replace, {\n      apply: callbackWrapper(\n        (target, thisArg, argumentsList) => {\n          const [text] = argumentsList;\n          const { id, styleId } = getIdAndStyleId(\n            thisArg,\n            mirror2,\n            stylesheetManager.styleMirror\n          );\n          if (id && id !== -1 || styleId && styleId !== -1) {\n            styleSheetRuleCb({\n              id,\n              styleId,\n              replace: text\n            });\n          }\n          return target.apply(thisArg, argumentsList);\n        }\n      )\n    });\n  }\n  let replaceSync;\n  if (win.CSSStyleSheet.prototype.replaceSync) {\n    replaceSync = win.CSSStyleSheet.prototype.replaceSync;\n    win.CSSStyleSheet.prototype.replaceSync = new Proxy(replaceSync, {\n      apply: callbackWrapper(\n        (target, thisArg, argumentsList) => {\n          const [text] = argumentsList;\n          const { id, styleId } = getIdAndStyleId(\n            thisArg,\n            mirror2,\n            stylesheetManager.styleMirror\n          );\n          if (id && id !== -1 || styleId && styleId !== -1) {\n            styleSheetRuleCb({\n              id,\n              styleId,\n              replaceSync: text\n            });\n          }\n          return target.apply(thisArg, argumentsList);\n        }\n      )\n    });\n  }\n  const supportedNestedCSSRuleTypes = {};\n  if (canMonkeyPatchNestedCSSRule(\"CSSGroupingRule\")) {\n    supportedNestedCSSRuleTypes.CSSGroupingRule = win.CSSGroupingRule;\n  } else {\n    if (canMonkeyPatchNestedCSSRule(\"CSSMediaRule\")) {\n      supportedNestedCSSRuleTypes.CSSMediaRule = win.CSSMediaRule;\n    }\n    if (canMonkeyPatchNestedCSSRule(\"CSSConditionRule\")) {\n      supportedNestedCSSRuleTypes.CSSConditionRule = win.CSSConditionRule;\n    }\n    if (canMonkeyPatchNestedCSSRule(\"CSSSupportsRule\")) {\n      supportedNestedCSSRuleTypes.CSSSupportsRule = win.CSSSupportsRule;\n    }\n  }\n  const unmodifiedFunctions = {};\n  Object.entries(supportedNestedCSSRuleTypes).forEach(([typeKey, type]) => {\n    unmodifiedFunctions[typeKey] = {\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      insertRule: type.prototype.insertRule,\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      deleteRule: type.prototype.deleteRule\n    };\n    type.prototype.insertRule = new Proxy(\n      unmodifiedFunctions[typeKey].insertRule,\n      {\n        apply: callbackWrapper(\n          (target, thisArg, argumentsList) => {\n            const [rule2, index2] = argumentsList;\n            const { id, styleId } = getIdAndStyleId(\n              thisArg.parentStyleSheet,\n              mirror2,\n              stylesheetManager.styleMirror\n            );\n            if (id && id !== -1 || styleId && styleId !== -1) {\n              styleSheetRuleCb({\n                id,\n                styleId,\n                adds: [\n                  {\n                    rule: rule2,\n                    index: [\n                      ...getNestedCSSRulePositions(thisArg),\n                      index2 || 0\n                      // defaults to 0\n                    ]\n                  }\n                ]\n              });\n            }\n            return target.apply(thisArg, argumentsList);\n          }\n        )\n      }\n    );\n    type.prototype.deleteRule = new Proxy(\n      unmodifiedFunctions[typeKey].deleteRule,\n      {\n        apply: callbackWrapper(\n          (target, thisArg, argumentsList) => {\n            const [index2] = argumentsList;\n            const { id, styleId } = getIdAndStyleId(\n              thisArg.parentStyleSheet,\n              mirror2,\n              stylesheetManager.styleMirror\n            );\n            if (id && id !== -1 || styleId && styleId !== -1) {\n              styleSheetRuleCb({\n                id,\n                styleId,\n                removes: [\n                  { index: [...getNestedCSSRulePositions(thisArg), index2] }\n                ]\n              });\n            }\n            return target.apply(thisArg, argumentsList);\n          }\n        )\n      }\n    );\n  });\n  return callbackWrapper(() => {\n    win.CSSStyleSheet.prototype.insertRule = insertRule;\n    win.CSSStyleSheet.prototype.deleteRule = deleteRule;\n    replace && (win.CSSStyleSheet.prototype.replace = replace);\n    replaceSync && (win.CSSStyleSheet.prototype.replaceSync = replaceSync);\n    Object.entries(supportedNestedCSSRuleTypes).forEach(([typeKey, type]) => {\n      type.prototype.insertRule = unmodifiedFunctions[typeKey].insertRule;\n      type.prototype.deleteRule = unmodifiedFunctions[typeKey].deleteRule;\n    });\n  });\n}\nfunction initAdoptedStyleSheetObserver({\n  mirror: mirror2,\n  stylesheetManager\n}, host2) {\n  var _a2, _b, _c;\n  let hostId = null;\n  if (host2.nodeName === \"#document\") hostId = mirror2.getId(host2);\n  else hostId = mirror2.getId(index.host(host2));\n  const patchTarget = host2.nodeName === \"#document\" ? (_a2 = host2.defaultView) == null ? void 0 : _a2.Document : (_c = (_b = host2.ownerDocument) == null ? void 0 : _b.defaultView) == null ? void 0 : _c.ShadowRoot;\n  const originalPropertyDescriptor = (patchTarget == null ? void 0 : patchTarget.prototype) ? Object.getOwnPropertyDescriptor(\n    patchTarget == null ? void 0 : patchTarget.prototype,\n    \"adoptedStyleSheets\"\n  ) : void 0;\n  if (hostId === null || hostId === -1 || !patchTarget || !originalPropertyDescriptor)\n    return () => {\n    };\n  Object.defineProperty(host2, \"adoptedStyleSheets\", {\n    configurable: originalPropertyDescriptor.configurable,\n    enumerable: originalPropertyDescriptor.enumerable,\n    get() {\n      var _a3;\n      return (_a3 = originalPropertyDescriptor.get) == null ? void 0 : _a3.call(this);\n    },\n    set(sheets) {\n      var _a3;\n      const result2 = (_a3 = originalPropertyDescriptor.set) == null ? void 0 : _a3.call(this, sheets);\n      if (hostId !== null && hostId !== -1) {\n        try {\n          stylesheetManager.adoptStyleSheets(sheets, hostId);\n        } catch (e2) {\n        }\n      }\n      return result2;\n    }\n  });\n  return callbackWrapper(() => {\n    Object.defineProperty(host2, \"adoptedStyleSheets\", {\n      configurable: originalPropertyDescriptor.configurable,\n      enumerable: originalPropertyDescriptor.enumerable,\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      get: originalPropertyDescriptor.get,\n      // eslint-disable-next-line @typescript-eslint/unbound-method\n      set: originalPropertyDescriptor.set\n    });\n  });\n}\nfunction initStyleDeclarationObserver({\n  styleDeclarationCb,\n  mirror: mirror2,\n  ignoreCSSAttributes,\n  stylesheetManager\n}, { win }) {\n  const setProperty = win.CSSStyleDeclaration.prototype.setProperty;\n  win.CSSStyleDeclaration.prototype.setProperty = new Proxy(setProperty, {\n    apply: callbackWrapper(\n      (target, thisArg, argumentsList) => {\n        var _a2;\n        const [property, value, priority] = argumentsList;\n        if (ignoreCSSAttributes.has(property)) {\n          return setProperty.apply(thisArg, [property, value, priority]);\n        }\n        const { id, styleId } = getIdAndStyleId(\n          (_a2 = thisArg.parentRule) == null ? void 0 : _a2.parentStyleSheet,\n          mirror2,\n          stylesheetManager.styleMirror\n        );\n        if (id && id !== -1 || styleId && styleId !== -1) {\n          styleDeclarationCb({\n            id,\n            styleId,\n            set: {\n              property,\n              value,\n              priority\n            },\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            index: getNestedCSSRulePositions(thisArg.parentRule)\n          });\n        }\n        return target.apply(thisArg, argumentsList);\n      }\n    )\n  });\n  const removeProperty = win.CSSStyleDeclaration.prototype.removeProperty;\n  win.CSSStyleDeclaration.prototype.removeProperty = new Proxy(removeProperty, {\n    apply: callbackWrapper(\n      (target, thisArg, argumentsList) => {\n        var _a2;\n        const [property] = argumentsList;\n        if (ignoreCSSAttributes.has(property)) {\n          return removeProperty.apply(thisArg, [property]);\n        }\n        const { id, styleId } = getIdAndStyleId(\n          (_a2 = thisArg.parentRule) == null ? void 0 : _a2.parentStyleSheet,\n          mirror2,\n          stylesheetManager.styleMirror\n        );\n        if (id && id !== -1 || styleId && styleId !== -1) {\n          styleDeclarationCb({\n            id,\n            styleId,\n            remove: {\n              property\n            },\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            index: getNestedCSSRulePositions(thisArg.parentRule)\n          });\n        }\n        return target.apply(thisArg, argumentsList);\n      }\n    )\n  });\n  return callbackWrapper(() => {\n    win.CSSStyleDeclaration.prototype.setProperty = setProperty;\n    win.CSSStyleDeclaration.prototype.removeProperty = removeProperty;\n  });\n}\nfunction initMediaInteractionObserver({\n  mediaInteractionCb,\n  blockClass,\n  blockSelector,\n  mirror: mirror2,\n  sampling,\n  doc\n}) {\n  const handler = callbackWrapper(\n    (type) => throttle(\n      callbackWrapper((event) => {\n        const target = getEventTarget(event);\n        if (!target || isBlocked(target, blockClass, blockSelector, true)) {\n          return;\n        }\n        const { currentTime, volume, muted, playbackRate, loop } = target;\n        mediaInteractionCb({\n          type,\n          id: mirror2.getId(target),\n          currentTime,\n          volume,\n          muted,\n          playbackRate,\n          loop\n        });\n      }),\n      sampling.media || 500\n    )\n  );\n  const handlers = [\n    on(\"play\", handler(MediaInteractions.Play), doc),\n    on(\"pause\", handler(MediaInteractions.Pause), doc),\n    on(\"seeked\", handler(MediaInteractions.Seeked), doc),\n    on(\"volumechange\", handler(MediaInteractions.VolumeChange), doc),\n    on(\"ratechange\", handler(MediaInteractions.RateChange), doc)\n  ];\n  return callbackWrapper(() => {\n    handlers.forEach((h) => h());\n  });\n}\nfunction initFontObserver({ fontCb, doc }) {\n  const win = doc.defaultView;\n  if (!win) {\n    return () => {\n    };\n  }\n  const handlers = [];\n  const fontMap = /* @__PURE__ */ new WeakMap();\n  const originalFontFace = win.FontFace;\n  win.FontFace = function FontFace2(family, source, descriptors) {\n    const fontFace = new originalFontFace(family, source, descriptors);\n    fontMap.set(fontFace, {\n      family,\n      buffer: typeof source !== \"string\",\n      descriptors,\n      fontSource: typeof source === \"string\" ? source : JSON.stringify(Array.from(new Uint8Array(source)))\n    });\n    return fontFace;\n  };\n  const restoreHandler = patch(\n    doc.fonts,\n    \"add\",\n    function(original) {\n      return function(fontFace) {\n        setTimeout(\n          callbackWrapper(() => {\n            const p = fontMap.get(fontFace);\n            if (p) {\n              fontCb(p);\n              fontMap.delete(fontFace);\n            }\n          }),\n          0\n        );\n        return original.apply(this, [fontFace]);\n      };\n    }\n  );\n  handlers.push(() => {\n    win.FontFace = originalFontFace;\n  });\n  handlers.push(restoreHandler);\n  return callbackWrapper(() => {\n    handlers.forEach((h) => h());\n  });\n}\nfunction initSelectionObserver(param) {\n  const { doc, mirror: mirror2, blockClass, blockSelector, selectionCb } = param;\n  let collapsed = true;\n  const updateSelection = callbackWrapper(() => {\n    const selection = doc.getSelection();\n    if (!selection || collapsed && (selection == null ? void 0 : selection.isCollapsed)) return;\n    collapsed = selection.isCollapsed || false;\n    const ranges = [];\n    const count = selection.rangeCount || 0;\n    for (let i2 = 0; i2 < count; i2++) {\n      const range = selection.getRangeAt(i2);\n      const { startContainer, startOffset, endContainer, endOffset } = range;\n      const blocked = isBlocked(startContainer, blockClass, blockSelector, true) || isBlocked(endContainer, blockClass, blockSelector, true);\n      if (blocked) continue;\n      ranges.push({\n        start: mirror2.getId(startContainer),\n        startOffset,\n        end: mirror2.getId(endContainer),\n        endOffset\n      });\n    }\n    selectionCb({ ranges });\n  });\n  updateSelection();\n  return on(\"selectionchange\", updateSelection);\n}\nfunction initCustomElementObserver({\n  doc,\n  customElementCb\n}) {\n  const win = doc.defaultView;\n  if (!win || !win.customElements) return () => {\n  };\n  const restoreHandler = patch(\n    win.customElements,\n    \"define\",\n    function(original) {\n      return function(name, constructor, options) {\n        try {\n          customElementCb({\n            define: {\n              name\n            }\n          });\n        } catch (e2) {\n          console.warn(`Custom element callback failed for ${name}`);\n        }\n        return original.apply(this, [name, constructor, options]);\n      };\n    }\n  );\n  return restoreHandler;\n}\nfunction mergeHooks(o2, hooks) {\n  const {\n    mutationCb,\n    mousemoveCb,\n    mouseInteractionCb,\n    scrollCb,\n    viewportResizeCb,\n    inputCb,\n    mediaInteractionCb,\n    styleSheetRuleCb,\n    styleDeclarationCb,\n    canvasMutationCb,\n    fontCb,\n    selectionCb,\n    customElementCb\n  } = o2;\n  o2.mutationCb = (...p) => {\n    if (hooks.mutation) {\n      hooks.mutation(...p);\n    }\n    mutationCb(...p);\n  };\n  o2.mousemoveCb = (...p) => {\n    if (hooks.mousemove) {\n      hooks.mousemove(...p);\n    }\n    mousemoveCb(...p);\n  };\n  o2.mouseInteractionCb = (...p) => {\n    if (hooks.mouseInteraction) {\n      hooks.mouseInteraction(...p);\n    }\n    mouseInteractionCb(...p);\n  };\n  o2.scrollCb = (...p) => {\n    if (hooks.scroll) {\n      hooks.scroll(...p);\n    }\n    scrollCb(...p);\n  };\n  o2.viewportResizeCb = (...p) => {\n    if (hooks.viewportResize) {\n      hooks.viewportResize(...p);\n    }\n    viewportResizeCb(...p);\n  };\n  o2.inputCb = (...p) => {\n    if (hooks.input) {\n      hooks.input(...p);\n    }\n    inputCb(...p);\n  };\n  o2.mediaInteractionCb = (...p) => {\n    if (hooks.mediaInteaction) {\n      hooks.mediaInteaction(...p);\n    }\n    mediaInteractionCb(...p);\n  };\n  o2.styleSheetRuleCb = (...p) => {\n    if (hooks.styleSheetRule) {\n      hooks.styleSheetRule(...p);\n    }\n    styleSheetRuleCb(...p);\n  };\n  o2.styleDeclarationCb = (...p) => {\n    if (hooks.styleDeclaration) {\n      hooks.styleDeclaration(...p);\n    }\n    styleDeclarationCb(...p);\n  };\n  o2.canvasMutationCb = (...p) => {\n    if (hooks.canvasMutation) {\n      hooks.canvasMutation(...p);\n    }\n    canvasMutationCb(...p);\n  };\n  o2.fontCb = (...p) => {\n    if (hooks.font) {\n      hooks.font(...p);\n    }\n    fontCb(...p);\n  };\n  o2.selectionCb = (...p) => {\n    if (hooks.selection) {\n      hooks.selection(...p);\n    }\n    selectionCb(...p);\n  };\n  o2.customElementCb = (...c2) => {\n    if (hooks.customElement) {\n      hooks.customElement(...c2);\n    }\n    customElementCb(...c2);\n  };\n}\nfunction initObservers(o2, hooks = {}) {\n  const currentWindow = o2.doc.defaultView;\n  if (!currentWindow) {\n    return () => {\n    };\n  }\n  mergeHooks(o2, hooks);\n  let mutationObserver;\n  if (o2.recordDOM) {\n    mutationObserver = initMutationObserver(o2, o2.doc);\n  }\n  const mousemoveHandler = initMoveObserver(o2);\n  const mouseInteractionHandler = initMouseInteractionObserver(o2);\n  const scrollHandler = initScrollObserver(o2);\n  const viewportResizeHandler = initViewportResizeObserver(o2, {\n    win: currentWindow\n  });\n  const inputHandler = initInputObserver(o2);\n  const mediaInteractionHandler = initMediaInteractionObserver(o2);\n  let styleSheetObserver = () => {\n  };\n  let adoptedStyleSheetObserver = () => {\n  };\n  let styleDeclarationObserver = () => {\n  };\n  let fontObserver = () => {\n  };\n  if (o2.recordDOM) {\n    styleSheetObserver = initStyleSheetObserver(o2, { win: currentWindow });\n    adoptedStyleSheetObserver = initAdoptedStyleSheetObserver(o2, o2.doc);\n    styleDeclarationObserver = initStyleDeclarationObserver(o2, {\n      win: currentWindow\n    });\n    if (o2.collectFonts) {\n      fontObserver = initFontObserver(o2);\n    }\n  }\n  const selectionObserver = initSelectionObserver(o2);\n  const customElementObserver = initCustomElementObserver(o2);\n  const pluginHandlers = [];\n  for (const plugin3 of o2.plugins) {\n    pluginHandlers.push(\n      plugin3.observer(plugin3.callback, currentWindow, plugin3.options)\n    );\n  }\n  return callbackWrapper(() => {\n    mutationBuffers.forEach((b) => b.reset());\n    mutationObserver == null ? void 0 : mutationObserver.disconnect();\n    mousemoveHandler();\n    mouseInteractionHandler();\n    scrollHandler();\n    viewportResizeHandler();\n    inputHandler();\n    mediaInteractionHandler();\n    styleSheetObserver();\n    adoptedStyleSheetObserver();\n    styleDeclarationObserver();\n    fontObserver();\n    selectionObserver();\n    customElementObserver();\n    pluginHandlers.forEach((h) => h());\n  });\n}\nfunction hasNestedCSSRule(prop) {\n  return typeof window[prop] !== \"undefined\";\n}\nfunction canMonkeyPatchNestedCSSRule(prop) {\n  return Boolean(\n    typeof window[prop] !== \"undefined\" && // Note: Generally, this check _shouldn't_ be necessary\n    // However, in some scenarios (e.g. jsdom) this can sometimes fail, so we check for it here\n    window[prop].prototype && \"insertRule\" in window[prop].prototype && \"deleteRule\" in window[prop].prototype\n  );\n}\nclass CrossOriginIframeMirror {\n  constructor(generateIdFn) {\n    __publicField(this, \"iframeIdToRemoteIdMap\", /* @__PURE__ */ new WeakMap());\n    __publicField(this, \"iframeRemoteIdToIdMap\", /* @__PURE__ */ new WeakMap());\n    this.generateIdFn = generateIdFn;\n  }\n  getId(iframe, remoteId, idToRemoteMap, remoteToIdMap) {\n    const idToRemoteIdMap = idToRemoteMap || this.getIdToRemoteIdMap(iframe);\n    const remoteIdToIdMap = remoteToIdMap || this.getRemoteIdToIdMap(iframe);\n    let id = idToRemoteIdMap.get(remoteId);\n    if (!id) {\n      id = this.generateIdFn();\n      idToRemoteIdMap.set(remoteId, id);\n      remoteIdToIdMap.set(id, remoteId);\n    }\n    return id;\n  }\n  getIds(iframe, remoteId) {\n    const idToRemoteIdMap = this.getIdToRemoteIdMap(iframe);\n    const remoteIdToIdMap = this.getRemoteIdToIdMap(iframe);\n    return remoteId.map(\n      (id) => this.getId(iframe, id, idToRemoteIdMap, remoteIdToIdMap)\n    );\n  }\n  getRemoteId(iframe, id, map) {\n    const remoteIdToIdMap = map || this.getRemoteIdToIdMap(iframe);\n    if (typeof id !== \"number\") return id;\n    const remoteId = remoteIdToIdMap.get(id);\n    if (!remoteId) return -1;\n    return remoteId;\n  }\n  getRemoteIds(iframe, ids) {\n    const remoteIdToIdMap = this.getRemoteIdToIdMap(iframe);\n    return ids.map((id) => this.getRemoteId(iframe, id, remoteIdToIdMap));\n  }\n  reset(iframe) {\n    if (!iframe) {\n      this.iframeIdToRemoteIdMap = /* @__PURE__ */ new WeakMap();\n      this.iframeRemoteIdToIdMap = /* @__PURE__ */ new WeakMap();\n      return;\n    }\n    this.iframeIdToRemoteIdMap.delete(iframe);\n    this.iframeRemoteIdToIdMap.delete(iframe);\n  }\n  getIdToRemoteIdMap(iframe) {\n    let idToRemoteIdMap = this.iframeIdToRemoteIdMap.get(iframe);\n    if (!idToRemoteIdMap) {\n      idToRemoteIdMap = /* @__PURE__ */ new Map();\n      this.iframeIdToRemoteIdMap.set(iframe, idToRemoteIdMap);\n    }\n    return idToRemoteIdMap;\n  }\n  getRemoteIdToIdMap(iframe) {\n    let remoteIdToIdMap = this.iframeRemoteIdToIdMap.get(iframe);\n    if (!remoteIdToIdMap) {\n      remoteIdToIdMap = /* @__PURE__ */ new Map();\n      this.iframeRemoteIdToIdMap.set(iframe, remoteIdToIdMap);\n    }\n    return remoteIdToIdMap;\n  }\n}\nclass IframeManager {\n  constructor(options) {\n    __publicField(this, \"iframes\", /* @__PURE__ */ new WeakMap());\n    __publicField(this, \"crossOriginIframeMap\", /* @__PURE__ */ new WeakMap());\n    __publicField(this, \"crossOriginIframeMirror\", new CrossOriginIframeMirror(genId));\n    __publicField(this, \"crossOriginIframeStyleMirror\");\n    __publicField(this, \"crossOriginIframeRootIdMap\", /* @__PURE__ */ new WeakMap());\n    __publicField(this, \"mirror\");\n    __publicField(this, \"mutationCb\");\n    __publicField(this, \"wrappedEmit\");\n    __publicField(this, \"loadListener\");\n    __publicField(this, \"stylesheetManager\");\n    __publicField(this, \"recordCrossOriginIframes\");\n    this.mutationCb = options.mutationCb;\n    this.wrappedEmit = options.wrappedEmit;\n    this.stylesheetManager = options.stylesheetManager;\n    this.recordCrossOriginIframes = options.recordCrossOriginIframes;\n    this.crossOriginIframeStyleMirror = new CrossOriginIframeMirror(\n      this.stylesheetManager.styleMirror.generateId.bind(\n        this.stylesheetManager.styleMirror\n      )\n    );\n    this.mirror = options.mirror;\n    if (this.recordCrossOriginIframes) {\n      window.addEventListener(\"message\", this.handleMessage.bind(this));\n    }\n  }\n  addIframe(iframeEl) {\n    this.iframes.set(iframeEl, true);\n    if (iframeEl.contentWindow)\n      this.crossOriginIframeMap.set(iframeEl.contentWindow, iframeEl);\n  }\n  addLoadListener(cb) {\n    this.loadListener = cb;\n  }\n  attachIframe(iframeEl, childSn) {\n    var _a2, _b;\n    this.mutationCb({\n      adds: [\n        {\n          parentId: this.mirror.getId(iframeEl),\n          nextId: null,\n          node: childSn\n        }\n      ],\n      removes: [],\n      texts: [],\n      attributes: [],\n      isAttachIframe: true\n    });\n    if (this.recordCrossOriginIframes)\n      (_a2 = iframeEl.contentWindow) == null ? void 0 : _a2.addEventListener(\n        \"message\",\n        this.handleMessage.bind(this)\n      );\n    (_b = this.loadListener) == null ? void 0 : _b.call(this, iframeEl);\n    if (iframeEl.contentDocument && iframeEl.contentDocument.adoptedStyleSheets && iframeEl.contentDocument.adoptedStyleSheets.length > 0)\n      this.stylesheetManager.adoptStyleSheets(\n        iframeEl.contentDocument.adoptedStyleSheets,\n        this.mirror.getId(iframeEl.contentDocument)\n      );\n  }\n  handleMessage(message) {\n    const crossOriginMessageEvent = message;\n    if (crossOriginMessageEvent.data.type !== \"rrweb\" || // To filter out the rrweb messages which are forwarded by some sites.\n    crossOriginMessageEvent.origin !== crossOriginMessageEvent.data.origin)\n      return;\n    const iframeSourceWindow = message.source;\n    if (!iframeSourceWindow) return;\n    const iframeEl = this.crossOriginIframeMap.get(message.source);\n    if (!iframeEl) return;\n    const transformedEvent = this.transformCrossOriginEvent(\n      iframeEl,\n      crossOriginMessageEvent.data.event\n    );\n    if (transformedEvent)\n      this.wrappedEmit(\n        transformedEvent,\n        crossOriginMessageEvent.data.isCheckout\n      );\n  }\n  transformCrossOriginEvent(iframeEl, e2) {\n    var _a2;\n    switch (e2.type) {\n      case EventType.FullSnapshot: {\n        this.crossOriginIframeMirror.reset(iframeEl);\n        this.crossOriginIframeStyleMirror.reset(iframeEl);\n        this.replaceIdOnNode(e2.data.node, iframeEl);\n        const rootId = e2.data.node.id;\n        this.crossOriginIframeRootIdMap.set(iframeEl, rootId);\n        this.patchRootIdOnNode(e2.data.node, rootId);\n        return {\n          timestamp: e2.timestamp,\n          type: EventType.IncrementalSnapshot,\n          data: {\n            source: IncrementalSource.Mutation,\n            adds: [\n              {\n                parentId: this.mirror.getId(iframeEl),\n                nextId: null,\n                node: e2.data.node\n              }\n            ],\n            removes: [],\n            texts: [],\n            attributes: [],\n            isAttachIframe: true\n          }\n        };\n      }\n      case EventType.Meta:\n      case EventType.Load:\n      case EventType.DomContentLoaded: {\n        return false;\n      }\n      case EventType.Plugin: {\n        return e2;\n      }\n      case EventType.Custom: {\n        this.replaceIds(\n          e2.data.payload,\n          iframeEl,\n          [\"id\", \"parentId\", \"previousId\", \"nextId\"]\n        );\n        return e2;\n      }\n      case EventType.IncrementalSnapshot: {\n        switch (e2.data.source) {\n          case IncrementalSource.Mutation: {\n            e2.data.adds.forEach((n2) => {\n              this.replaceIds(n2, iframeEl, [\n                \"parentId\",\n                \"nextId\",\n                \"previousId\"\n              ]);\n              this.replaceIdOnNode(n2.node, iframeEl);\n              const rootId = this.crossOriginIframeRootIdMap.get(iframeEl);\n              rootId && this.patchRootIdOnNode(n2.node, rootId);\n            });\n            e2.data.removes.forEach((n2) => {\n              this.replaceIds(n2, iframeEl, [\"parentId\", \"id\"]);\n            });\n            e2.data.attributes.forEach((n2) => {\n              this.replaceIds(n2, iframeEl, [\"id\"]);\n            });\n            e2.data.texts.forEach((n2) => {\n              this.replaceIds(n2, iframeEl, [\"id\"]);\n            });\n            return e2;\n          }\n          case IncrementalSource.Drag:\n          case IncrementalSource.TouchMove:\n          case IncrementalSource.MouseMove: {\n            e2.data.positions.forEach((p) => {\n              this.replaceIds(p, iframeEl, [\"id\"]);\n            });\n            return e2;\n          }\n          case IncrementalSource.ViewportResize: {\n            return false;\n          }\n          case IncrementalSource.MediaInteraction:\n          case IncrementalSource.MouseInteraction:\n          case IncrementalSource.Scroll:\n          case IncrementalSource.CanvasMutation:\n          case IncrementalSource.Input: {\n            this.replaceIds(e2.data, iframeEl, [\"id\"]);\n            return e2;\n          }\n          case IncrementalSource.StyleSheetRule:\n          case IncrementalSource.StyleDeclaration: {\n            this.replaceIds(e2.data, iframeEl, [\"id\"]);\n            this.replaceStyleIds(e2.data, iframeEl, [\"styleId\"]);\n            return e2;\n          }\n          case IncrementalSource.Font: {\n            return e2;\n          }\n          case IncrementalSource.Selection: {\n            e2.data.ranges.forEach((range) => {\n              this.replaceIds(range, iframeEl, [\"start\", \"end\"]);\n            });\n            return e2;\n          }\n          case IncrementalSource.AdoptedStyleSheet: {\n            this.replaceIds(e2.data, iframeEl, [\"id\"]);\n            this.replaceStyleIds(e2.data, iframeEl, [\"styleIds\"]);\n            (_a2 = e2.data.styles) == null ? void 0 : _a2.forEach((style) => {\n              this.replaceStyleIds(style, iframeEl, [\"styleId\"]);\n            });\n            return e2;\n          }\n        }\n      }\n    }\n    return false;\n  }\n  replace(iframeMirror, obj, iframeEl, keys) {\n    for (const key of keys) {\n      if (!Array.isArray(obj[key]) && typeof obj[key] !== \"number\") continue;\n      if (Array.isArray(obj[key])) {\n        obj[key] = iframeMirror.getIds(\n          iframeEl,\n          obj[key]\n        );\n      } else {\n        obj[key] = iframeMirror.getId(iframeEl, obj[key]);\n      }\n    }\n    return obj;\n  }\n  replaceIds(obj, iframeEl, keys) {\n    return this.replace(this.crossOriginIframeMirror, obj, iframeEl, keys);\n  }\n  replaceStyleIds(obj, iframeEl, keys) {\n    return this.replace(this.crossOriginIframeStyleMirror, obj, iframeEl, keys);\n  }\n  replaceIdOnNode(node2, iframeEl) {\n    this.replaceIds(node2, iframeEl, [\"id\", \"rootId\"]);\n    if (\"childNodes\" in node2) {\n      node2.childNodes.forEach((child) => {\n        this.replaceIdOnNode(child, iframeEl);\n      });\n    }\n  }\n  patchRootIdOnNode(node2, rootId) {\n    if (node2.type !== NodeType$2.Document && !node2.rootId) node2.rootId = rootId;\n    if (\"childNodes\" in node2) {\n      node2.childNodes.forEach((child) => {\n        this.patchRootIdOnNode(child, rootId);\n      });\n    }\n  }\n}\nclass ShadowDomManager {\n  constructor(options) {\n    __publicField(this, \"shadowDoms\", /* @__PURE__ */ new WeakSet());\n    __publicField(this, \"mutationCb\");\n    __publicField(this, \"scrollCb\");\n    __publicField(this, \"bypassOptions\");\n    __publicField(this, \"mirror\");\n    __publicField(this, \"restoreHandlers\", []);\n    this.mutationCb = options.mutationCb;\n    this.scrollCb = options.scrollCb;\n    this.bypassOptions = options.bypassOptions;\n    this.mirror = options.mirror;\n    this.init();\n  }\n  init() {\n    this.reset();\n    this.patchAttachShadow(Element, document);\n  }\n  addShadowRoot(shadowRoot2, doc) {\n    if (!isNativeShadowDom(shadowRoot2)) return;\n    if (this.shadowDoms.has(shadowRoot2)) return;\n    this.shadowDoms.add(shadowRoot2);\n    const observer = initMutationObserver(\n      {\n        ...this.bypassOptions,\n        doc,\n        mutationCb: this.mutationCb,\n        mirror: this.mirror,\n        shadowDomManager: this\n      },\n      shadowRoot2\n    );\n    this.restoreHandlers.push(() => observer.disconnect());\n    this.restoreHandlers.push(\n      initScrollObserver({\n        ...this.bypassOptions,\n        scrollCb: this.scrollCb,\n        // https://gist.github.com/praveenpuglia/0832da687ed5a5d7a0907046c9ef1813\n        // scroll is not allowed to pass the boundary, so we need to listen the shadow document\n        doc: shadowRoot2,\n        mirror: this.mirror\n      })\n    );\n    setTimeout(() => {\n      if (shadowRoot2.adoptedStyleSheets && shadowRoot2.adoptedStyleSheets.length > 0)\n        this.bypassOptions.stylesheetManager.adoptStyleSheets(\n          shadowRoot2.adoptedStyleSheets,\n          this.mirror.getId(index.host(shadowRoot2))\n        );\n      this.restoreHandlers.push(\n        initAdoptedStyleSheetObserver(\n          {\n            mirror: this.mirror,\n            stylesheetManager: this.bypassOptions.stylesheetManager\n          },\n          shadowRoot2\n        )\n      );\n    }, 0);\n  }\n  /**\n   * Monkey patch 'attachShadow' of an IFrameElement to observe newly added shadow doms.\n   */\n  observeAttachShadow(iframeElement) {\n    if (!iframeElement.contentWindow || !iframeElement.contentDocument) return;\n    this.patchAttachShadow(\n      iframeElement.contentWindow.Element,\n      iframeElement.contentDocument\n    );\n  }\n  /**\n   * Patch 'attachShadow' to observe newly added shadow doms.\n   */\n  patchAttachShadow(element, doc) {\n    const manager = this;\n    this.restoreHandlers.push(\n      patch(\n        element.prototype,\n        \"attachShadow\",\n        function(original) {\n          return function(option) {\n            const sRoot = original.call(this, option);\n            const shadowRootEl = index.shadowRoot(this);\n            if (shadowRootEl && inDom(this))\n              manager.addShadowRoot(shadowRootEl, doc);\n            return sRoot;\n          };\n        }\n      )\n    );\n  }\n  reset() {\n    this.restoreHandlers.forEach((handler) => {\n      try {\n        handler();\n      } catch (e2) {\n      }\n    });\n    this.restoreHandlers = [];\n    this.shadowDoms = /* @__PURE__ */ new WeakSet();\n  }\n}\nvar chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nvar lookup = typeof Uint8Array === \"undefined\" ? [] : new Uint8Array(256);\nfor (var i$1 = 0; i$1 < chars.length; i$1++) {\n  lookup[chars.charCodeAt(i$1)] = i$1;\n}\nvar encode = function(arraybuffer) {\n  var bytes = new Uint8Array(arraybuffer), i2, len = bytes.length, base64 = \"\";\n  for (i2 = 0; i2 < len; i2 += 3) {\n    base64 += chars[bytes[i2] >> 2];\n    base64 += chars[(bytes[i2] & 3) << 4 | bytes[i2 + 1] >> 4];\n    base64 += chars[(bytes[i2 + 1] & 15) << 2 | bytes[i2 + 2] >> 6];\n    base64 += chars[bytes[i2 + 2] & 63];\n  }\n  if (len % 3 === 2) {\n    base64 = base64.substring(0, base64.length - 1) + \"=\";\n  } else if (len % 3 === 1) {\n    base64 = base64.substring(0, base64.length - 2) + \"==\";\n  }\n  return base64;\n};\nconst canvasVarMap = /* @__PURE__ */ new Map();\nfunction variableListFor$1(ctx, ctor) {\n  let contextMap = canvasVarMap.get(ctx);\n  if (!contextMap) {\n    contextMap = /* @__PURE__ */ new Map();\n    canvasVarMap.set(ctx, contextMap);\n  }\n  if (!contextMap.has(ctor)) {\n    contextMap.set(ctor, []);\n  }\n  return contextMap.get(ctor);\n}\nconst saveWebGLVar = (value, win, ctx) => {\n  if (!value || !(isInstanceOfWebGLObject(value, win) || typeof value === \"object\"))\n    return;\n  const name = value.constructor.name;\n  const list2 = variableListFor$1(ctx, name);\n  let index2 = list2.indexOf(value);\n  if (index2 === -1) {\n    index2 = list2.length;\n    list2.push(value);\n  }\n  return index2;\n};\nfunction serializeArg(value, win, ctx) {\n  if (value instanceof Array) {\n    return value.map((arg) => serializeArg(arg, win, ctx));\n  } else if (value === null) {\n    return value;\n  } else if (value instanceof Float32Array || value instanceof Float64Array || value instanceof Int32Array || value instanceof Uint32Array || value instanceof Uint8Array || value instanceof Uint16Array || value instanceof Int16Array || value instanceof Int8Array || value instanceof Uint8ClampedArray) {\n    const name = value.constructor.name;\n    return {\n      rr_type: name,\n      args: [Object.values(value)]\n    };\n  } else if (\n    // SharedArrayBuffer disabled on most browsers due to spectre.\n    // More info: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/SharedArrayBuffer/SharedArrayBuffer\n    // value instanceof SharedArrayBuffer ||\n    value instanceof ArrayBuffer\n  ) {\n    const name = value.constructor.name;\n    const base64 = encode(value);\n    return {\n      rr_type: name,\n      base64\n    };\n  } else if (value instanceof DataView) {\n    const name = value.constructor.name;\n    return {\n      rr_type: name,\n      args: [\n        serializeArg(value.buffer, win, ctx),\n        value.byteOffset,\n        value.byteLength\n      ]\n    };\n  } else if (value instanceof HTMLImageElement) {\n    const name = value.constructor.name;\n    const { src } = value;\n    return {\n      rr_type: name,\n      src\n    };\n  } else if (value instanceof HTMLCanvasElement) {\n    const name = \"HTMLImageElement\";\n    const src = value.toDataURL();\n    return {\n      rr_type: name,\n      src\n    };\n  } else if (value instanceof ImageData) {\n    const name = value.constructor.name;\n    return {\n      rr_type: name,\n      args: [serializeArg(value.data, win, ctx), value.width, value.height]\n    };\n  } else if (isInstanceOfWebGLObject(value, win) || typeof value === \"object\") {\n    const name = value.constructor.name;\n    const index2 = saveWebGLVar(value, win, ctx);\n    return {\n      rr_type: name,\n      index: index2\n    };\n  }\n  return value;\n}\nconst serializeArgs = (args, win, ctx) => {\n  return args.map((arg) => serializeArg(arg, win, ctx));\n};\nconst isInstanceOfWebGLObject = (value, win) => {\n  const webGLConstructorNames = [\n    \"WebGLActiveInfo\",\n    \"WebGLBuffer\",\n    \"WebGLFramebuffer\",\n    \"WebGLProgram\",\n    \"WebGLRenderbuffer\",\n    \"WebGLShader\",\n    \"WebGLShaderPrecisionFormat\",\n    \"WebGLTexture\",\n    \"WebGLUniformLocation\",\n    \"WebGLVertexArrayObject\",\n    // In old Chrome versions, value won't be an instanceof WebGLVertexArrayObject.\n    \"WebGLVertexArrayObjectOES\"\n  ];\n  const supportedWebGLConstructorNames = webGLConstructorNames.filter(\n    (name) => typeof win[name] === \"function\"\n  );\n  return Boolean(\n    supportedWebGLConstructorNames.find(\n      (name) => value instanceof win[name]\n    )\n  );\n};\nfunction initCanvas2DMutationObserver(cb, win, blockClass, blockSelector) {\n  const handlers = [];\n  const props2D = Object.getOwnPropertyNames(\n    win.CanvasRenderingContext2D.prototype\n  );\n  for (const prop of props2D) {\n    try {\n      if (typeof win.CanvasRenderingContext2D.prototype[prop] !== \"function\") {\n        continue;\n      }\n      const restoreHandler = patch(\n        win.CanvasRenderingContext2D.prototype,\n        prop,\n        function(original) {\n          return function(...args) {\n            if (!isBlocked(this.canvas, blockClass, blockSelector, true)) {\n              setTimeout(() => {\n                const recordArgs = serializeArgs(args, win, this);\n                cb(this.canvas, {\n                  type: CanvasContext[\"2D\"],\n                  property: prop,\n                  args: recordArgs\n                });\n              }, 0);\n            }\n            return original.apply(this, args);\n          };\n        }\n      );\n      handlers.push(restoreHandler);\n    } catch {\n      const hookHandler = hookSetter(\n        win.CanvasRenderingContext2D.prototype,\n        prop,\n        {\n          set(v2) {\n            cb(this.canvas, {\n              type: CanvasContext[\"2D\"],\n              property: prop,\n              args: [v2],\n              setter: true\n            });\n          }\n        }\n      );\n      handlers.push(hookHandler);\n    }\n  }\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\nfunction getNormalizedContextName(contextType) {\n  return contextType === \"experimental-webgl\" ? \"webgl\" : contextType;\n}\nfunction initCanvasContextObserver(win, blockClass, blockSelector, setPreserveDrawingBufferToTrue) {\n  const handlers = [];\n  try {\n    const restoreHandler = patch(\n      win.HTMLCanvasElement.prototype,\n      \"getContext\",\n      function(original) {\n        return function(contextType, ...args) {\n          if (!isBlocked(this, blockClass, blockSelector, true)) {\n            const ctxName = getNormalizedContextName(contextType);\n            if (!(\"__context\" in this)) this.__context = ctxName;\n            if (setPreserveDrawingBufferToTrue && [\"webgl\", \"webgl2\"].includes(ctxName)) {\n              if (args[0] && typeof args[0] === \"object\") {\n                const contextAttributes = args[0];\n                if (!contextAttributes.preserveDrawingBuffer) {\n                  contextAttributes.preserveDrawingBuffer = true;\n                }\n              } else {\n                args.splice(0, 1, {\n                  preserveDrawingBuffer: true\n                });\n              }\n            }\n          }\n          return original.apply(this, [contextType, ...args]);\n        };\n      }\n    );\n    handlers.push(restoreHandler);\n  } catch {\n    console.error(\"failed to patch HTMLCanvasElement.prototype.getContext\");\n  }\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\nfunction patchGLPrototype(prototype, type, cb, blockClass, blockSelector, win) {\n  const handlers = [];\n  const props = Object.getOwnPropertyNames(prototype);\n  for (const prop of props) {\n    if (\n      //prop.startsWith('get') ||  // e.g. getProgramParameter, but too risky\n      [\n        \"isContextLost\",\n        \"canvas\",\n        \"drawingBufferWidth\",\n        \"drawingBufferHeight\"\n      ].includes(prop)\n    ) {\n      continue;\n    }\n    try {\n      if (typeof prototype[prop] !== \"function\") {\n        continue;\n      }\n      const restoreHandler = patch(\n        prototype,\n        prop,\n        function(original) {\n          return function(...args) {\n            const result2 = original.apply(this, args);\n            saveWebGLVar(result2, win, this);\n            if (\"tagName\" in this.canvas && !isBlocked(this.canvas, blockClass, blockSelector, true)) {\n              const recordArgs = serializeArgs(args, win, this);\n              const mutation = {\n                type,\n                property: prop,\n                args: recordArgs\n              };\n              cb(this.canvas, mutation);\n            }\n            return result2;\n          };\n        }\n      );\n      handlers.push(restoreHandler);\n    } catch {\n      const hookHandler = hookSetter(prototype, prop, {\n        set(v2) {\n          cb(this.canvas, {\n            type,\n            property: prop,\n            args: [v2],\n            setter: true\n          });\n        }\n      });\n      handlers.push(hookHandler);\n    }\n  }\n  return handlers;\n}\nfunction initCanvasWebGLMutationObserver(cb, win, blockClass, blockSelector) {\n  const handlers = [];\n  handlers.push(\n    ...patchGLPrototype(\n      win.WebGLRenderingContext.prototype,\n      CanvasContext.WebGL,\n      cb,\n      blockClass,\n      blockSelector,\n      win\n    )\n  );\n  if (typeof win.WebGL2RenderingContext !== \"undefined\") {\n    handlers.push(\n      ...patchGLPrototype(\n        win.WebGL2RenderingContext.prototype,\n        CanvasContext.WebGL2,\n        cb,\n        blockClass,\n        blockSelector,\n        win\n      )\n    );\n  }\n  return () => {\n    handlers.forEach((h) => h());\n  };\n}\nconst encodedJs = \"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\";\nconst decodeBase64 = (base64) => Uint8Array.from(atob(base64), (c2) => c2.charCodeAt(0));\nconst blob = typeof window !== \"undefined\" && window.Blob && new Blob([decodeBase64(encodedJs)], { type: \"text/javascript;charset=utf-8\" });\nfunction WorkerWrapper(options) {\n  let objURL;\n  try {\n    objURL = blob && (window.URL || window.webkitURL).createObjectURL(blob);\n    if (!objURL) throw \"\";\n    const worker = new Worker(objURL, {\n      name: options == null ? void 0 : options.name\n    });\n    worker.addEventListener(\"error\", () => {\n      (window.URL || window.webkitURL).revokeObjectURL(objURL);\n    });\n    return worker;\n  } catch (e2) {\n    return new Worker(\n      \"data:text/javascript;base64,\" + encodedJs,\n      {\n        name: options == null ? void 0 : options.name\n      }\n    );\n  } finally {\n    objURL && (window.URL || window.webkitURL).revokeObjectURL(objURL);\n  }\n}\nclass CanvasManager {\n  constructor(options) {\n    __publicField(this, \"pendingCanvasMutations\", /* @__PURE__ */ new Map());\n    __publicField(this, \"rafStamps\", { latestId: 0, invokeId: null });\n    __publicField(this, \"mirror\");\n    __publicField(this, \"mutationCb\");\n    __publicField(this, \"resetObservers\");\n    __publicField(this, \"frozen\", false);\n    __publicField(this, \"locked\", false);\n    __publicField(this, \"processMutation\", (target, mutation) => {\n      const newFrame = this.rafStamps.invokeId && this.rafStamps.latestId !== this.rafStamps.invokeId;\n      if (newFrame || !this.rafStamps.invokeId)\n        this.rafStamps.invokeId = this.rafStamps.latestId;\n      if (!this.pendingCanvasMutations.has(target)) {\n        this.pendingCanvasMutations.set(target, []);\n      }\n      this.pendingCanvasMutations.get(target).push(mutation);\n    });\n    const {\n      sampling = \"all\",\n      win,\n      blockClass,\n      blockSelector,\n      recordCanvas,\n      dataURLOptions\n    } = options;\n    this.mutationCb = options.mutationCb;\n    this.mirror = options.mirror;\n    if (recordCanvas && sampling === \"all\")\n      this.initCanvasMutationObserver(win, blockClass, blockSelector);\n    if (recordCanvas && typeof sampling === \"number\")\n      this.initCanvasFPSObserver(sampling, win, blockClass, blockSelector, {\n        dataURLOptions\n      });\n  }\n  reset() {\n    this.pendingCanvasMutations.clear();\n    this.resetObservers && this.resetObservers();\n  }\n  freeze() {\n    this.frozen = true;\n  }\n  unfreeze() {\n    this.frozen = false;\n  }\n  lock() {\n    this.locked = true;\n  }\n  unlock() {\n    this.locked = false;\n  }\n  initCanvasFPSObserver(fps, win, blockClass, blockSelector, options) {\n    const canvasContextReset = initCanvasContextObserver(\n      win,\n      blockClass,\n      blockSelector,\n      true\n    );\n    const snapshotInProgressMap = /* @__PURE__ */ new Map();\n    const worker = new WorkerWrapper();\n    worker.onmessage = (e2) => {\n      const { id } = e2.data;\n      snapshotInProgressMap.set(id, false);\n      if (!(\"base64\" in e2.data)) return;\n      const { base64, type, width, height } = e2.data;\n      this.mutationCb({\n        id,\n        type: CanvasContext[\"2D\"],\n        commands: [\n          {\n            property: \"clearRect\",\n            // wipe canvas\n            args: [0, 0, width, height]\n          },\n          {\n            property: \"drawImage\",\n            // draws (semi-transparent) image\n            args: [\n              {\n                rr_type: \"ImageBitmap\",\n                args: [\n                  {\n                    rr_type: \"Blob\",\n                    data: [{ rr_type: \"ArrayBuffer\", base64 }],\n                    type\n                  }\n                ]\n              },\n              0,\n              0\n            ]\n          }\n        ]\n      });\n    };\n    const timeBetweenSnapshots = 1e3 / fps;\n    let lastSnapshotTime = 0;\n    let rafId;\n    const getCanvas = () => {\n      const matchedCanvas = [];\n      const searchCanvas = (root) => {\n        root.querySelectorAll(\"canvas\").forEach((canvas) => {\n          if (!isBlocked(canvas, blockClass, blockSelector, true)) {\n            matchedCanvas.push(canvas);\n          }\n        });\n        root.querySelectorAll(\"*\").forEach((elem) => {\n          if (elem.shadowRoot) {\n            searchCanvas(elem.shadowRoot);\n          }\n        });\n      };\n      searchCanvas(win.document);\n      return matchedCanvas;\n    };\n    const takeCanvasSnapshots = (timestamp) => {\n      if (lastSnapshotTime && timestamp - lastSnapshotTime < timeBetweenSnapshots) {\n        rafId = requestAnimationFrame(takeCanvasSnapshots);\n        return;\n      }\n      lastSnapshotTime = timestamp;\n      getCanvas().forEach(async (canvas) => {\n        var _a2;\n        const id = this.mirror.getId(canvas);\n        if (snapshotInProgressMap.get(id)) return;\n        if (canvas.width === 0 || canvas.height === 0) return;\n        snapshotInProgressMap.set(id, true);\n        if ([\"webgl\", \"webgl2\"].includes(canvas.__context)) {\n          const context = canvas.getContext(canvas.__context);\n          if (((_a2 = context == null ? void 0 : context.getContextAttributes()) == null ? void 0 : _a2.preserveDrawingBuffer) === false) {\n            context.clear(context.COLOR_BUFFER_BIT);\n          }\n        }\n        // createImageBitmap throws if resizing to 0\n        // Fallback to intrinsic size if canvas has not yet rendered\n        const width = canvas.clientWidth || canvas.width;\n        const height = canvas.clientHeight || canvas.height;\n        const bitmap = await createImageBitmap(canvas, {\n          resizeWidth: width,\n          resizeHeight: height\n        });\n        worker.postMessage(\n          {\n            id,\n            bitmap,\n            width: width,\n            height: height,\n            dataURLOptions: options.dataURLOptions\n          },\n          [bitmap]\n        );\n      });\n      rafId = requestAnimationFrame(takeCanvasSnapshots);\n    };\n    rafId = requestAnimationFrame(takeCanvasSnapshots);\n    this.resetObservers = () => {\n      canvasContextReset();\n      cancelAnimationFrame(rafId);\n    };\n  }\n  initCanvasMutationObserver(win, blockClass, blockSelector) {\n    this.startRAFTimestamping();\n    this.startPendingCanvasMutationFlusher();\n    const canvasContextReset = initCanvasContextObserver(\n      win,\n      blockClass,\n      blockSelector,\n      false\n    );\n    const canvas2DReset = initCanvas2DMutationObserver(\n      this.processMutation.bind(this),\n      win,\n      blockClass,\n      blockSelector\n    );\n    const canvasWebGL1and2Reset = initCanvasWebGLMutationObserver(\n      this.processMutation.bind(this),\n      win,\n      blockClass,\n      blockSelector\n    );\n    this.resetObservers = () => {\n      canvasContextReset();\n      canvas2DReset();\n      canvasWebGL1and2Reset();\n    };\n  }\n  startPendingCanvasMutationFlusher() {\n    requestAnimationFrame(() => this.flushPendingCanvasMutations());\n  }\n  startRAFTimestamping() {\n    const setLatestRAFTimestamp = (timestamp) => {\n      this.rafStamps.latestId = timestamp;\n      requestAnimationFrame(setLatestRAFTimestamp);\n    };\n    requestAnimationFrame(setLatestRAFTimestamp);\n  }\n  flushPendingCanvasMutations() {\n    this.pendingCanvasMutations.forEach(\n      (_values, canvas) => {\n        const id = this.mirror.getId(canvas);\n        this.flushPendingCanvasMutationFor(canvas, id);\n      }\n    );\n    requestAnimationFrame(() => this.flushPendingCanvasMutations());\n  }\n  flushPendingCanvasMutationFor(canvas, id) {\n    if (this.frozen || this.locked) {\n      return;\n    }\n    const valuesWithType = this.pendingCanvasMutations.get(canvas);\n    if (!valuesWithType || id === -1) return;\n    const values = valuesWithType.map((value) => {\n      const { type: type2, ...rest } = value;\n      return rest;\n    });\n    const { type } = valuesWithType[0];\n    this.mutationCb({ id, type, commands: values });\n    this.pendingCanvasMutations.delete(canvas);\n  }\n}\nclass StylesheetManager {\n  constructor(options) {\n    __publicField(this, \"trackedLinkElements\", /* @__PURE__ */ new WeakSet());\n    __publicField(this, \"mutationCb\");\n    __publicField(this, \"adoptedStyleSheetCb\");\n    __publicField(this, \"styleMirror\", new StyleSheetMirror());\n    this.mutationCb = options.mutationCb;\n    this.adoptedStyleSheetCb = options.adoptedStyleSheetCb;\n  }\n  attachLinkElement(linkEl, childSn) {\n    if (\"_cssText\" in childSn.attributes)\n      this.mutationCb({\n        adds: [],\n        removes: [],\n        texts: [],\n        attributes: [\n          {\n            id: childSn.id,\n            attributes: childSn.attributes\n          }\n        ]\n      });\n    this.trackLinkElement(linkEl);\n  }\n  trackLinkElement(linkEl) {\n    if (this.trackedLinkElements.has(linkEl)) return;\n    this.trackedLinkElements.add(linkEl);\n    this.trackStylesheetInLinkElement(linkEl);\n  }\n  adoptStyleSheets(sheets, hostId) {\n    if (sheets.length === 0) return;\n    const adoptedStyleSheetData = {\n      id: hostId,\n      styleIds: []\n    };\n    const styles = [];\n    for (const sheet of sheets) {\n      let styleId;\n      if (!this.styleMirror.has(sheet)) {\n        styleId = this.styleMirror.add(sheet);\n        styles.push({\n          styleId,\n          rules: Array.from(sheet.rules || CSSRule, (r2, index2) => ({\n            rule: stringifyRule(r2, sheet.href),\n            index: index2\n          }))\n        });\n      } else styleId = this.styleMirror.getId(sheet);\n      adoptedStyleSheetData.styleIds.push(styleId);\n    }\n    if (styles.length > 0) adoptedStyleSheetData.styles = styles;\n    this.adoptedStyleSheetCb(adoptedStyleSheetData);\n  }\n  reset() {\n    this.styleMirror.reset();\n    this.trackedLinkElements = /* @__PURE__ */ new WeakSet();\n  }\n  // TODO: take snapshot on stylesheet reload by applying event listener\n  trackStylesheetInLinkElement(_linkEl) {\n  }\n}\nclass ProcessedNodeManager {\n  constructor() {\n    __publicField(this, \"nodeMap\", /* @__PURE__ */ new WeakMap());\n    __publicField(this, \"active\", false);\n  }\n  inOtherBuffer(node2, thisBuffer) {\n    const buffers = this.nodeMap.get(node2);\n    return buffers && Array.from(buffers).some((buffer) => buffer !== thisBuffer);\n  }\n  add(node2, buffer) {\n    if (!this.active) {\n      this.active = true;\n      requestAnimationFrame(() => {\n        this.nodeMap = /* @__PURE__ */ new WeakMap();\n        this.active = false;\n      });\n    }\n    this.nodeMap.set(node2, (this.nodeMap.get(node2) || /* @__PURE__ */ new Set()).add(buffer));\n  }\n  destroy() {\n  }\n}\nlet wrappedEmit;\nlet takeFullSnapshot$1;\nlet canvasManager;\nlet recording = false;\ntry {\n  if (Array.from([1], (x2) => x2 * 2)[0] !== 2) {\n    const cleanFrame = document.createElement(\"iframe\");\n    document.body.appendChild(cleanFrame);\n    Array.from = ((_a = cleanFrame.contentWindow) == null ? void 0 : _a.Array.from) || Array.from;\n    document.body.removeChild(cleanFrame);\n  }\n} catch (err) {\n  console.debug(\"Unable to override Array.from\", err);\n}\nconst mirror = createMirror$2();\nfunction record(options = {}) {\n  const {\n    emit,\n    checkoutEveryNms,\n    checkoutEveryNth,\n    blockClass = \"rr-block\",\n    blockSelector = null,\n    ignoreClass = \"rr-ignore\",\n    ignoreSelector = null,\n    maskTextClass = \"rr-mask\",\n    maskTextSelector = null,\n    inlineStylesheet = true,\n    maskAllInputs,\n    maskInputOptions: _maskInputOptions,\n    slimDOMOptions: _slimDOMOptions,\n    maskInputFn,\n    maskTextFn,\n    hooks,\n    packFn,\n    sampling = {},\n    dataURLOptions = {},\n    mousemoveWait,\n    recordDOM = true,\n    recordCanvas = false,\n    recordCrossOriginIframes = false,\n    recordAfter = options.recordAfter === \"DOMContentLoaded\" ? options.recordAfter : \"load\",\n    userTriggeredOnInput = false,\n    collectFonts = false,\n    inlineImages = false,\n    plugins,\n    keepIframeSrcFn = () => false,\n    ignoreCSSAttributes = /* @__PURE__ */ new Set([]),\n    errorHandler: errorHandler2\n  } = options;\n  registerErrorHandler(errorHandler2);\n  const inEmittingFrame = recordCrossOriginIframes ? window.parent === window : true;\n  let passEmitsToParent = false;\n  if (!inEmittingFrame) {\n    try {\n      if (window.parent.document) {\n        passEmitsToParent = false;\n      }\n    } catch (e2) {\n      passEmitsToParent = true;\n    }\n  }\n  if (inEmittingFrame && !emit) {\n    throw new Error(\"emit function is required\");\n  }\n  if (!inEmittingFrame && !passEmitsToParent) {\n    return () => {\n    };\n  }\n  if (mousemoveWait !== void 0 && sampling.mousemove === void 0) {\n    sampling.mousemove = mousemoveWait;\n  }\n  mirror.reset();\n  const maskInputOptions = maskAllInputs === true ? {\n    color: true,\n    date: true,\n    \"datetime-local\": true,\n    email: true,\n    month: true,\n    number: true,\n    range: true,\n    search: true,\n    tel: true,\n    text: true,\n    time: true,\n    url: true,\n    week: true,\n    textarea: true,\n    select: true,\n    password: true\n  } : _maskInputOptions !== void 0 ? _maskInputOptions : { password: true };\n  const slimDOMOptions = _slimDOMOptions === true || _slimDOMOptions === \"all\" ? {\n    script: true,\n    comment: true,\n    headFavicon: true,\n    headWhitespace: true,\n    headMetaSocial: true,\n    headMetaRobots: true,\n    headMetaHttpEquiv: true,\n    headMetaVerification: true,\n    // the following are off for slimDOMOptions === true,\n    // as they destroy some (hidden) info:\n    headMetaAuthorship: _slimDOMOptions === \"all\",\n    headMetaDescKeywords: _slimDOMOptions === \"all\",\n    headTitleMutations: _slimDOMOptions === \"all\"\n  } : _slimDOMOptions ? _slimDOMOptions : {};\n  polyfill$1();\n  let lastFullSnapshotEvent;\n  let incrementalSnapshotCount = 0;\n  const eventProcessor = (e2) => {\n    for (const plugin3 of plugins || []) {\n      if (plugin3.eventProcessor) {\n        e2 = plugin3.eventProcessor(e2);\n      }\n    }\n    if (packFn && // Disable packing events which will be emitted to parent frames.\n    !passEmitsToParent) {\n      e2 = packFn(e2);\n    }\n    return e2;\n  };\n  wrappedEmit = (r2, isCheckout) => {\n    var _a2;\n    const e2 = r2;\n    e2.timestamp = nowTimestamp();\n    if (((_a2 = mutationBuffers[0]) == null ? void 0 : _a2.isFrozen()) && e2.type !== EventType.FullSnapshot && !(e2.type === EventType.IncrementalSnapshot && e2.data.source === IncrementalSource.Mutation)) {\n      mutationBuffers.forEach((buf) => buf.unfreeze());\n    }\n    if (inEmittingFrame) {\n      emit == null ? void 0 : emit(eventProcessor(e2), isCheckout);\n    } else if (passEmitsToParent) {\n      const message = {\n        type: \"rrweb\",\n        event: eventProcessor(e2),\n        origin: window.location.origin,\n        isCheckout\n      };\n      window.parent.postMessage(message, \"*\");\n    }\n    if (e2.type === EventType.FullSnapshot) {\n      lastFullSnapshotEvent = e2;\n      incrementalSnapshotCount = 0;\n    } else if (e2.type === EventType.IncrementalSnapshot) {\n      if (e2.data.source === IncrementalSource.Mutation && e2.data.isAttachIframe) {\n        return;\n      }\n      incrementalSnapshotCount++;\n      const exceedCount = checkoutEveryNth && incrementalSnapshotCount >= checkoutEveryNth;\n      const exceedTime = checkoutEveryNms && e2.timestamp - lastFullSnapshotEvent.timestamp > checkoutEveryNms;\n      if (exceedCount || exceedTime) {\n        takeFullSnapshot$1(true);\n      }\n    }\n  };\n  const wrappedMutationEmit = (m) => {\n    wrappedEmit({\n      type: EventType.IncrementalSnapshot,\n      data: {\n        source: IncrementalSource.Mutation,\n        ...m\n      }\n    });\n  };\n  const wrappedScrollEmit = (p) => wrappedEmit({\n    type: EventType.IncrementalSnapshot,\n    data: {\n      source: IncrementalSource.Scroll,\n      ...p\n    }\n  });\n  const wrappedCanvasMutationEmit = (p) => wrappedEmit({\n    type: EventType.IncrementalSnapshot,\n    data: {\n      source: IncrementalSource.CanvasMutation,\n      ...p\n    }\n  });\n  const wrappedAdoptedStyleSheetEmit = (a2) => wrappedEmit({\n    type: EventType.IncrementalSnapshot,\n    data: {\n      source: IncrementalSource.AdoptedStyleSheet,\n      ...a2\n    }\n  });\n  const stylesheetManager = new StylesheetManager({\n    mutationCb: wrappedMutationEmit,\n    adoptedStyleSheetCb: wrappedAdoptedStyleSheetEmit\n  });\n  const iframeManager = new IframeManager({\n    mirror,\n    mutationCb: wrappedMutationEmit,\n    stylesheetManager,\n    recordCrossOriginIframes,\n    wrappedEmit\n  });\n  for (const plugin3 of plugins || []) {\n    if (plugin3.getMirror)\n      plugin3.getMirror({\n        nodeMirror: mirror,\n        crossOriginIframeMirror: iframeManager.crossOriginIframeMirror,\n        crossOriginIframeStyleMirror: iframeManager.crossOriginIframeStyleMirror\n      });\n  }\n  const processedNodeManager = new ProcessedNodeManager();\n  canvasManager = new CanvasManager({\n    recordCanvas,\n    mutationCb: wrappedCanvasMutationEmit,\n    win: window,\n    blockClass,\n    blockSelector,\n    mirror,\n    sampling: sampling.canvas,\n    dataURLOptions\n  });\n  const shadowDomManager = new ShadowDomManager({\n    mutationCb: wrappedMutationEmit,\n    scrollCb: wrappedScrollEmit,\n    bypassOptions: {\n      blockClass,\n      blockSelector,\n      maskTextClass,\n      maskTextSelector,\n      inlineStylesheet,\n      maskInputOptions,\n      dataURLOptions,\n      maskTextFn,\n      maskInputFn,\n      recordCanvas,\n      inlineImages,\n      sampling,\n      slimDOMOptions,\n      iframeManager,\n      stylesheetManager,\n      canvasManager,\n      keepIframeSrcFn,\n      processedNodeManager\n    },\n    mirror\n  });\n  takeFullSnapshot$1 = (isCheckout = false) => {\n    if (!recordDOM) {\n      return;\n    }\n    wrappedEmit(\n      {\n        type: EventType.Meta,\n        data: {\n          href: window.location.href,\n          width: getWindowWidth(),\n          height: getWindowHeight()\n        }\n      },\n      isCheckout\n    );\n    stylesheetManager.reset();\n    shadowDomManager.init();\n    mutationBuffers.forEach((buf) => buf.lock());\n    const node2 = snapshot(document, {\n      mirror,\n      blockClass,\n      blockSelector,\n      maskTextClass,\n      maskTextSelector,\n      inlineStylesheet,\n      maskAllInputs: maskInputOptions,\n      maskTextFn,\n      maskInputFn,\n      slimDOM: slimDOMOptions,\n      dataURLOptions,\n      recordCanvas,\n      inlineImages,\n      onSerialize: (n2) => {\n        if (isSerializedIframe(n2, mirror)) {\n          iframeManager.addIframe(n2);\n        }\n        if (isSerializedStylesheet(n2, mirror)) {\n          stylesheetManager.trackLinkElement(n2);\n        }\n        if (hasShadowRoot(n2)) {\n          shadowDomManager.addShadowRoot(index.shadowRoot(n2), document);\n        }\n      },\n      onIframeLoad: (iframe, childSn) => {\n        iframeManager.attachIframe(iframe, childSn);\n        shadowDomManager.observeAttachShadow(iframe);\n      },\n      onStylesheetLoad: (linkEl, childSn) => {\n        stylesheetManager.attachLinkElement(linkEl, childSn);\n      },\n      keepIframeSrcFn\n    });\n    if (!node2) {\n      return console.warn(\"Failed to snapshot the document\");\n    }\n    wrappedEmit(\n      {\n        type: EventType.FullSnapshot,\n        data: {\n          node: node2,\n          initialOffset: getWindowScroll(window)\n        }\n      },\n      isCheckout\n    );\n    mutationBuffers.forEach((buf) => buf.unlock());\n    if (document.adoptedStyleSheets && document.adoptedStyleSheets.length > 0)\n      stylesheetManager.adoptStyleSheets(\n        document.adoptedStyleSheets,\n        mirror.getId(document)\n      );\n  };\n  try {\n    const handlers = [];\n    const observe = (doc) => {\n      var _a2;\n      return callbackWrapper(initObservers)(\n        {\n          mutationCb: wrappedMutationEmit,\n          mousemoveCb: (positions, source) => wrappedEmit({\n            type: EventType.IncrementalSnapshot,\n            data: {\n              source,\n              positions\n            }\n          }),\n          mouseInteractionCb: (d) => wrappedEmit({\n            type: EventType.IncrementalSnapshot,\n            data: {\n              source: IncrementalSource.MouseInteraction,\n              ...d\n            }\n          }),\n          scrollCb: wrappedScrollEmit,\n          viewportResizeCb: (d) => wrappedEmit({\n            type: EventType.IncrementalSnapshot,\n            data: {\n              source: IncrementalSource.ViewportResize,\n              ...d\n            }\n          }),\n          inputCb: (v2) => wrappedEmit({\n            type: EventType.IncrementalSnapshot,\n            data: {\n              source: IncrementalSource.Input,\n              ...v2\n            }\n          }),\n          mediaInteractionCb: (p) => wrappedEmit({\n            type: EventType.IncrementalSnapshot,\n            data: {\n              source: IncrementalSource.MediaInteraction,\n              ...p\n            }\n          }),\n          styleSheetRuleCb: (r2) => wrappedEmit({\n            type: EventType.IncrementalSnapshot,\n            data: {\n              source: IncrementalSource.StyleSheetRule,\n              ...r2\n            }\n          }),\n          styleDeclarationCb: (r2) => wrappedEmit({\n            type: EventType.IncrementalSnapshot,\n            data: {\n              source: IncrementalSource.StyleDeclaration,\n              ...r2\n            }\n          }),\n          canvasMutationCb: wrappedCanvasMutationEmit,\n          fontCb: (p) => wrappedEmit({\n            type: EventType.IncrementalSnapshot,\n            data: {\n              source: IncrementalSource.Font,\n              ...p\n            }\n          }),\n          selectionCb: (p) => {\n            wrappedEmit({\n              type: EventType.IncrementalSnapshot,\n              data: {\n                source: IncrementalSource.Selection,\n                ...p\n              }\n            });\n          },\n          customElementCb: (c2) => {\n            wrappedEmit({\n              type: EventType.IncrementalSnapshot,\n              data: {\n                source: IncrementalSource.CustomElement,\n                ...c2\n              }\n            });\n          },\n          blockClass,\n          ignoreClass,\n          ignoreSelector,\n          maskTextClass,\n          maskTextSelector,\n          maskInputOptions,\n          inlineStylesheet,\n          sampling,\n          recordDOM,\n          recordCanvas,\n          inlineImages,\n          userTriggeredOnInput,\n          collectFonts,\n          doc,\n          maskInputFn,\n          maskTextFn,\n          keepIframeSrcFn,\n          blockSelector,\n          slimDOMOptions,\n          dataURLOptions,\n          mirror,\n          iframeManager,\n          stylesheetManager,\n          shadowDomManager,\n          processedNodeManager,\n          canvasManager,\n          ignoreCSSAttributes,\n          plugins: ((_a2 = plugins == null ? void 0 : plugins.filter((p) => p.observer)) == null ? void 0 : _a2.map((p) => ({\n            observer: p.observer,\n            options: p.options,\n            callback: (payload) => wrappedEmit({\n              type: EventType.Plugin,\n              data: {\n                plugin: p.name,\n                payload\n              }\n            })\n          }))) || []\n        },\n        hooks\n      );\n    };\n    iframeManager.addLoadListener((iframeEl) => {\n      try {\n        handlers.push(observe(iframeEl.contentDocument));\n      } catch (error) {\n        console.warn(error);\n      }\n    });\n    const init = () => {\n      takeFullSnapshot$1();\n      handlers.push(observe(document));\n      recording = true;\n    };\n    if (document.readyState === \"interactive\" || document.readyState === \"complete\") {\n      init();\n    } else {\n      handlers.push(\n        on(\"DOMContentLoaded\", () => {\n          wrappedEmit({\n            type: EventType.DomContentLoaded,\n            data: {}\n          });\n          if (recordAfter === \"DOMContentLoaded\") init();\n        })\n      );\n      handlers.push(\n        on(\n          \"load\",\n          () => {\n            wrappedEmit({\n              type: EventType.Load,\n              data: {}\n            });\n            if (recordAfter === \"load\") init();\n          },\n          window\n        )\n      );\n    }\n    return () => {\n      handlers.forEach((h) => h());\n      processedNodeManager.destroy();\n      recording = false;\n      unregisterErrorHandler();\n    };\n  } catch (error) {\n    console.warn(error);\n  }\n}\nrecord.addCustomEvent = (tag, payload) => {\n  if (!recording) {\n    throw new Error(\"please add custom event after start recording\");\n  }\n  wrappedEmit({\n    type: EventType.Custom,\n    data: {\n      tag,\n      payload\n    }\n  });\n};\nrecord.freezePage = () => {\n  mutationBuffers.forEach((buf) => buf.freeze());\n};\nrecord.takeFullSnapshot = (isCheckout) => {\n  if (!recording) {\n    throw new Error(\"please take full snapshot after start recording\");\n  }\n  takeFullSnapshot$1(isCheckout);\n};\nrecord.mirror = mirror;\nvar n;\n!function(t2) {\n  t2[t2.NotStarted = 0] = \"NotStarted\", t2[t2.Running = 1] = \"Running\", t2[t2.Stopped = 2] = \"Stopped\";\n}(n || (n = {}));\nexport {\n  record\n};\n//# sourceMappingURL=record.js.map\n", "var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\nvar __defProp2 = Object.defineProperty;\nvar __defNormalProp2 = (obj, key, value) => key in obj ? __defProp2(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField2 = (obj, key, value) => __defNormalProp2(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\nvar _a;\nvar __defProp$1 = Object.defineProperty;\nvar __defNormalProp$1 = (obj, key, value) => key in obj ? __defProp$1(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField$1 = (obj, key, value) => __defNormalProp$1(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\nconst testableAccessors$1 = {\n  Node: [\"childNodes\", \"parentNode\", \"parentElement\", \"textContent\"],\n  ShadowRoot: [\"host\", \"styleSheets\"],\n  Element: [\"shadowRoot\", \"querySelector\", \"querySelectorAll\"],\n  MutationObserver: []\n};\nconst testableMethods$1 = {\n  Node: [\"contains\", \"getRootNode\"],\n  ShadowRoot: [\"getSelection\"],\n  Element: [],\n  MutationObserver: [\"constructor\"]\n};\nconst untaintedBasePrototype$1 = {};\nfunction getUntaintedPrototype$1(key) {\n  if (untaintedBasePrototype$1[key])\n    return untaintedBasePrototype$1[key];\n  const defaultObj = globalThis[key];\n  const defaultPrototype = defaultObj.prototype;\n  const accessorNames = key in testableAccessors$1 ? testableAccessors$1[key] : void 0;\n  const isUntaintedAccessors = Boolean(\n    accessorNames && // @ts-expect-error 2345\n    accessorNames.every(\n      (accessor) => {\n        var _a2, _b;\n        return Boolean(\n          (_b = (_a2 = Object.getOwnPropertyDescriptor(defaultPrototype, accessor)) == null ? void 0 : _a2.get) == null ? void 0 : _b.toString().includes(\"[native code]\")\n        );\n      }\n    )\n  );\n  const methodNames = key in testableMethods$1 ? testableMethods$1[key] : void 0;\n  const isUntaintedMethods = Boolean(\n    methodNames && methodNames.every(\n      // @ts-expect-error 2345\n      (method) => {\n        var _a2;\n        return typeof defaultPrototype[method] === \"function\" && ((_a2 = defaultPrototype[method]) == null ? void 0 : _a2.toString().includes(\"[native code]\"));\n      }\n    )\n  );\n  if (isUntaintedAccessors && isUntaintedMethods) {\n    untaintedBasePrototype$1[key] = defaultObj.prototype;\n    return defaultObj.prototype;\n  }\n  try {\n    const iframeEl = document.createElement(\"iframe\");\n    document.body.appendChild(iframeEl);\n    const win = iframeEl.contentWindow;\n    if (!win) return defaultObj.prototype;\n    const untaintedObject = win[key].prototype;\n    document.body.removeChild(iframeEl);\n    if (!untaintedObject) return defaultPrototype;\n    return untaintedBasePrototype$1[key] = untaintedObject;\n  } catch {\n    return defaultPrototype;\n  }\n}\nconst untaintedAccessorCache$1 = {};\nfunction getUntaintedAccessor$1(key, instance, accessor) {\n  var _a2;\n  const cacheKey = `${key}.${String(accessor)}`;\n  if (untaintedAccessorCache$1[cacheKey])\n    return untaintedAccessorCache$1[cacheKey].call(\n      instance\n    );\n  const untaintedPrototype = getUntaintedPrototype$1(key);\n  const untaintedAccessor = (_a2 = Object.getOwnPropertyDescriptor(\n    untaintedPrototype,\n    accessor\n  )) == null ? void 0 : _a2.get;\n  if (!untaintedAccessor) return instance[accessor];\n  untaintedAccessorCache$1[cacheKey] = untaintedAccessor;\n  return untaintedAccessor.call(instance);\n}\nconst untaintedMethodCache$1 = {};\nfunction getUntaintedMethod$1(key, instance, method) {\n  const cacheKey = `${key}.${String(method)}`;\n  if (untaintedMethodCache$1[cacheKey])\n    return untaintedMethodCache$1[cacheKey].bind(\n      instance\n    );\n  const untaintedPrototype = getUntaintedPrototype$1(key);\n  const untaintedMethod = untaintedPrototype[method];\n  if (typeof untaintedMethod !== \"function\") return instance[method];\n  untaintedMethodCache$1[cacheKey] = untaintedMethod;\n  return untaintedMethod.bind(instance);\n}\nfunction childNodes$1(n2) {\n  return getUntaintedAccessor$1(\"Node\", n2, \"childNodes\");\n}\nfunction parentNode$1(n2) {\n  return getUntaintedAccessor$1(\"Node\", n2, \"parentNode\");\n}\nfunction parentElement$1(n2) {\n  return getUntaintedAccessor$1(\"Node\", n2, \"parentElement\");\n}\nfunction textContent$1(n2) {\n  return getUntaintedAccessor$1(\"Node\", n2, \"textContent\");\n}\nfunction contains$1(n2, other) {\n  return getUntaintedMethod$1(\"Node\", n2, \"contains\")(other);\n}\nfunction getRootNode$1(n2) {\n  return getUntaintedMethod$1(\"Node\", n2, \"getRootNode\")();\n}\nfunction host$1(n2) {\n  if (!n2 || !(\"host\" in n2)) return null;\n  return getUntaintedAccessor$1(\"ShadowRoot\", n2, \"host\");\n}\nfunction styleSheets$1(n2) {\n  return n2.styleSheets;\n}\nfunction shadowRoot$1(n2) {\n  if (!n2 || !(\"shadowRoot\" in n2)) return null;\n  return getUntaintedAccessor$1(\"Element\", n2, \"shadowRoot\");\n}\nfunction querySelector$1(n2, selectors) {\n  return getUntaintedAccessor$1(\"Element\", n2, \"querySelector\")(selectors);\n}\nfunction querySelectorAll$1(n2, selectors) {\n  return getUntaintedAccessor$1(\"Element\", n2, \"querySelectorAll\")(selectors);\n}\nfunction mutationObserverCtor$1() {\n  return getUntaintedPrototype$1(\"MutationObserver\").constructor;\n}\nconst index$1 = {\n  childNodes: childNodes$1,\n  parentNode: parentNode$1,\n  parentElement: parentElement$1,\n  textContent: textContent$1,\n  contains: contains$1,\n  getRootNode: getRootNode$1,\n  host: host$1,\n  styleSheets: styleSheets$1,\n  shadowRoot: shadowRoot$1,\n  querySelector: querySelector$1,\n  querySelectorAll: querySelectorAll$1,\n  mutationObserver: mutationObserverCtor$1\n};\nfunction isShadowRoot(n2) {\n  const hostEl = (\n    // anchor and textarea elements also have a `host` property\n    // but only shadow roots have a `mode` property\n    n2 && \"host\" in n2 && \"mode\" in n2 && index$1.host(n2) || null\n  );\n  return Boolean(\n    hostEl && \"shadowRoot\" in hostEl && index$1.shadowRoot(hostEl) === n2\n  );\n}\nclass Mirror {\n  constructor() {\n    __publicField$1(this, \"idNodeMap\", /* @__PURE__ */ new Map());\n    __publicField$1(this, \"nodeMetaMap\", /* @__PURE__ */ new WeakMap());\n  }\n  getId(n2) {\n    var _a2;\n    if (!n2) return -1;\n    const id = (_a2 = this.getMeta(n2)) == null ? void 0 : _a2.id;\n    return id ?? -1;\n  }\n  getNode(id) {\n    return this.idNodeMap.get(id) || null;\n  }\n  getIds() {\n    return Array.from(this.idNodeMap.keys());\n  }\n  getMeta(n2) {\n    return this.nodeMetaMap.get(n2) || null;\n  }\n  // removes the node from idNodeMap\n  // doesn't remove the node from nodeMetaMap\n  removeNodeFromMap(n2) {\n    const id = this.getId(n2);\n    this.idNodeMap.delete(id);\n    if (n2.childNodes) {\n      n2.childNodes.forEach(\n        (childNode) => this.removeNodeFromMap(childNode)\n      );\n    }\n  }\n  has(id) {\n    return this.idNodeMap.has(id);\n  }\n  hasNode(node2) {\n    return this.nodeMetaMap.has(node2);\n  }\n  add(n2, meta) {\n    const id = meta.id;\n    this.idNodeMap.set(id, n2);\n    this.nodeMetaMap.set(n2, meta);\n  }\n  replace(id, n2) {\n    const oldNode = this.getNode(id);\n    if (oldNode) {\n      const meta = this.nodeMetaMap.get(oldNode);\n      if (meta) this.nodeMetaMap.set(n2, meta);\n    }\n    this.idNodeMap.set(id, n2);\n  }\n  reset() {\n    this.idNodeMap = /* @__PURE__ */ new Map();\n    this.nodeMetaMap = /* @__PURE__ */ new WeakMap();\n  }\n}\nfunction createMirror$2() {\n  return new Mirror();\n}\nconst IGNORED_NODE = -2;\nfunction classMatchesRegex(node2, regex, checkAncestors) {\n  if (!node2) return false;\n  if (node2.nodeType !== node2.ELEMENT_NODE) {\n    if (!checkAncestors) return false;\n    return classMatchesRegex(index$1.parentNode(node2), regex, checkAncestors);\n  }\n  for (let eIndex = node2.classList.length; eIndex--; ) {\n    const className = node2.classList[eIndex];\n    if (regex.test(className)) {\n      return true;\n    }\n  }\n  if (!checkAncestors) return false;\n  return classMatchesRegex(index$1.parentNode(node2), regex, checkAncestors);\n}\nfunction getDefaultExportFromCjs$1(x2) {\n  return x2 && x2.__esModule && Object.prototype.hasOwnProperty.call(x2, \"default\") ? x2[\"default\"] : x2;\n}\nfunction getAugmentedNamespace$1(n2) {\n  if (n2.__esModule) return n2;\n  var f2 = n2.default;\n  if (typeof f2 == \"function\") {\n    var a2 = function a22() {\n      if (this instanceof a22) {\n        return Reflect.construct(f2, arguments, this.constructor);\n      }\n      return f2.apply(this, arguments);\n    };\n    a2.prototype = f2.prototype;\n  } else a2 = {};\n  Object.defineProperty(a2, \"__esModule\", { value: true });\n  Object.keys(n2).forEach(function(k) {\n    var d = Object.getOwnPropertyDescriptor(n2, k);\n    Object.defineProperty(a2, k, d.get ? d : {\n      enumerable: true,\n      get: function() {\n        return n2[k];\n      }\n    });\n  });\n  return a2;\n}\n// Removed postcss here\nclass BaseRRNode {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-explicit-any\n  constructor(..._args) {\n    __publicField22(this, \"parentElement\", null);\n    __publicField22(this, \"parentNode\", null);\n    __publicField22(this, \"ownerDocument\");\n    __publicField22(this, \"firstChild\", null);\n    __publicField22(this, \"lastChild\", null);\n    __publicField22(this, \"previousSibling\", null);\n    __publicField22(this, \"nextSibling\", null);\n    __publicField22(this, \"ELEMENT_NODE\", 1);\n    __publicField22(this, \"TEXT_NODE\", 3);\n    __publicField22(this, \"nodeType\");\n    __publicField22(this, \"nodeName\");\n    __publicField22(this, \"RRNodeType\");\n  }\n  get childNodes() {\n    const childNodes2 = [];\n    let childIterator = this.firstChild;\n    while (childIterator) {\n      childNodes2.push(childIterator);\n      childIterator = childIterator.nextSibling;\n    }\n    return childNodes2;\n  }\n  contains(node2) {\n    if (!(node2 instanceof BaseRRNode)) return false;\n    else if (node2.ownerDocument !== this.ownerDocument) return false;\n    else if (node2 === this) return true;\n    while (node2.parentNode) {\n      if (node2.parentNode === this) return true;\n      node2 = node2.parentNode;\n    }\n    return false;\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  appendChild(_newChild) {\n    throw new Error(\n      `RRDomException: Failed to execute 'appendChild' on 'RRNode': This RRNode type does not support this method.`\n    );\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  insertBefore(_newChild, _refChild) {\n    throw new Error(\n      `RRDomException: Failed to execute 'insertBefore' on 'RRNode': This RRNode type does not support this method.`\n    );\n  }\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  removeChild(_node) {\n    throw new Error(\n      `RRDomException: Failed to execute 'removeChild' on 'RRNode': This RRNode type does not support this method.`\n    );\n  }\n  toString() {\n    return \"RRNode\";\n  }\n}\nconst testableAccessors = {\n  Node: [\"childNodes\", \"parentNode\", \"parentElement\", \"textContent\"],\n  ShadowRoot: [\"host\", \"styleSheets\"],\n  Element: [\"shadowRoot\", \"querySelector\", \"querySelectorAll\"],\n  MutationObserver: []\n};\nconst testableMethods = {\n  Node: [\"contains\", \"getRootNode\"],\n  ShadowRoot: [\"getSelection\"],\n  Element: [],\n  MutationObserver: [\"constructor\"]\n};\nconst untaintedBasePrototype = {};\nfunction getUntaintedPrototype(key) {\n  if (untaintedBasePrototype[key])\n    return untaintedBasePrototype[key];\n  const defaultObj = globalThis[key];\n  const defaultPrototype = defaultObj.prototype;\n  const accessorNames = key in testableAccessors ? testableAccessors[key] : void 0;\n  const isUntaintedAccessors = Boolean(\n    accessorNames && // @ts-expect-error 2345\n    accessorNames.every(\n      (accessor) => {\n        var _a2, _b;\n        return Boolean(\n          (_b = (_a2 = Object.getOwnPropertyDescriptor(defaultPrototype, accessor)) == null ? void 0 : _a2.get) == null ? void 0 : _b.toString().includes(\"[native code]\")\n        );\n      }\n    )\n  );\n  const methodNames = key in testableMethods ? testableMethods[key] : void 0;\n  const isUntaintedMethods = Boolean(\n    methodNames && methodNames.every(\n      // @ts-expect-error 2345\n      (method) => {\n        var _a2;\n        return typeof defaultPrototype[method] === \"function\" && ((_a2 = defaultPrototype[method]) == null ? void 0 : _a2.toString().includes(\"[native code]\"));\n      }\n    )\n  );\n  if (isUntaintedAccessors && isUntaintedMethods) {\n    untaintedBasePrototype[key] = defaultObj.prototype;\n    return defaultObj.prototype;\n  }\n  try {\n    const iframeEl = document.createElement(\"iframe\");\n    document.body.appendChild(iframeEl);\n    const win = iframeEl.contentWindow;\n    if (!win) return defaultObj.prototype;\n    const untaintedObject = win[key].prototype;\n    document.body.removeChild(iframeEl);\n    if (!untaintedObject) return defaultPrototype;\n    return untaintedBasePrototype[key] = untaintedObject;\n  } catch {\n    return defaultPrototype;\n  }\n}\nconst untaintedAccessorCache = {};\nfunction getUntaintedAccessor(key, instance, accessor) {\n  var _a2;\n  const cacheKey = `${key}.${String(accessor)}`;\n  if (untaintedAccessorCache[cacheKey])\n    return untaintedAccessorCache[cacheKey].call(\n      instance\n    );\n  const untaintedPrototype = getUntaintedPrototype(key);\n  const untaintedAccessor = (_a2 = Object.getOwnPropertyDescriptor(\n    untaintedPrototype,\n    accessor\n  )) == null ? void 0 : _a2.get;\n  if (!untaintedAccessor) return instance[accessor];\n  untaintedAccessorCache[cacheKey] = untaintedAccessor;\n  return untaintedAccessor.call(instance);\n}\nconst untaintedMethodCache = {};\nfunction getUntaintedMethod(key, instance, method) {\n  const cacheKey = `${key}.${String(method)}`;\n  if (untaintedMethodCache[cacheKey])\n    return untaintedMethodCache[cacheKey].bind(\n      instance\n    );\n  const untaintedPrototype = getUntaintedPrototype(key);\n  const untaintedMethod = untaintedPrototype[method];\n  if (typeof untaintedMethod !== \"function\") return instance[method];\n  untaintedMethodCache[cacheKey] = untaintedMethod;\n  return untaintedMethod.bind(instance);\n}\nfunction childNodes(n2) {\n  return getUntaintedAccessor(\"Node\", n2, \"childNodes\");\n}\nfunction parentNode(n2) {\n  return getUntaintedAccessor(\"Node\", n2, \"parentNode\");\n}\nfunction parentElement(n2) {\n  return getUntaintedAccessor(\"Node\", n2, \"parentElement\");\n}\nfunction textContent(n2) {\n  return getUntaintedAccessor(\"Node\", n2, \"textContent\");\n}\nfunction contains(n2, other) {\n  return getUntaintedMethod(\"Node\", n2, \"contains\")(other);\n}\nfunction getRootNode(n2) {\n  return getUntaintedMethod(\"Node\", n2, \"getRootNode\")();\n}\nfunction host(n2) {\n  if (!n2 || !(\"host\" in n2)) return null;\n  return getUntaintedAccessor(\"ShadowRoot\", n2, \"host\");\n}\nfunction styleSheets(n2) {\n  return n2.styleSheets;\n}\nfunction shadowRoot(n2) {\n  if (!n2 || !(\"shadowRoot\" in n2)) return null;\n  return getUntaintedAccessor(\"Element\", n2, \"shadowRoot\");\n}\nfunction querySelector(n2, selectors) {\n  return getUntaintedAccessor(\"Element\", n2, \"querySelector\")(selectors);\n}\nfunction querySelectorAll(n2, selectors) {\n  return getUntaintedAccessor(\"Element\", n2, \"querySelectorAll\")(selectors);\n}\nfunction mutationObserverCtor() {\n  return getUntaintedPrototype(\"MutationObserver\").constructor;\n}\nconst index = {\n  childNodes,\n  parentNode,\n  parentElement,\n  textContent,\n  contains,\n  getRootNode,\n  host,\n  styleSheets,\n  shadowRoot,\n  querySelector,\n  querySelectorAll,\n  mutationObserver: mutationObserverCtor\n};\nfunction on(type, fn, target = document) {\n  const options = { capture: true, passive: true };\n  target.addEventListener(type, fn, options);\n  return () => target.removeEventListener(type, fn, options);\n}\nconst DEPARTED_MIRROR_ACCESS_WARNING = \"Please stop import mirror directly. Instead of that,\\r\\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\\r\\nor you can use record.mirror to access the mirror instance during recording.\";\nlet _mirror = {\n  map: {},\n  getId() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return -1;\n  },\n  getNode() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return null;\n  },\n  removeNodeFromMap() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  },\n  has() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n    return false;\n  },\n  reset() {\n    console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n  }\n};\nif (typeof window !== \"undefined\" && window.Proxy && window.Reflect) {\n  _mirror = new Proxy(_mirror, {\n    get(target, prop, receiver) {\n      if (prop === \"map\") {\n        console.error(DEPARTED_MIRROR_ACCESS_WARNING);\n      }\n      return Reflect.get(target, prop, receiver);\n    }\n  });\n}\nfunction throttle(func, wait, options = {}) {\n  let timeout = null;\n  let previous = 0;\n  return function(...args) {\n    const now = Date.now();\n    if (!previous && options.leading === false) {\n      previous = now;\n    }\n    const remaining = wait - (now - previous);\n    const context = this;\n    if (remaining <= 0 || remaining > wait) {\n      if (timeout) {\n        clearTimeout(timeout);\n        timeout = null;\n      }\n      previous = now;\n      func.apply(context, args);\n    } else if (!timeout && options.trailing !== false) {\n      timeout = setTimeout(() => {\n        previous = options.leading === false ? 0 : Date.now();\n        timeout = null;\n        func.apply(context, args);\n      }, remaining);\n    }\n  };\n}\nfunction hookSetter(target, key, d, isRevoked, win = window) {\n  const original = win.Object.getOwnPropertyDescriptor(target, key);\n  win.Object.defineProperty(\n    target,\n    key,\n    isRevoked ? d : {\n      set(value) {\n        setTimeout(() => {\n          d.set.call(this, value);\n        }, 0);\n        if (original && original.set) {\n          original.set.call(this, value);\n        }\n      }\n    }\n  );\n  return () => hookSetter(target, key, original || {}, true);\n}\nfunction patch(source, name, replacement) {\n  try {\n    if (!(name in source)) {\n      return () => {\n      };\n    }\n    const original = source[name];\n    const wrapped = replacement(original);\n    if (typeof wrapped === \"function\") {\n      wrapped.prototype = wrapped.prototype || {};\n      Object.defineProperties(wrapped, {\n        __rrweb_original__: {\n          enumerable: false,\n          value: original\n        }\n      });\n    }\n    source[name] = wrapped;\n    return () => {\n      source[name] = original;\n    };\n  } catch {\n    return () => {\n    };\n  }\n}\nlet nowTimestamp = Date.now;\nif (!/* @__PURE__ */ /[1-9][0-9]{12}/.test(Date.now().toString())) {\n  nowTimestamp = () => (/* @__PURE__ */ new Date()).getTime();\n}\nfunction getWindowScroll(win) {\n  var _a2, _b, _c, _d;\n  const doc = win.document;\n  return {\n    left: doc.scrollingElement ? doc.scrollingElement.scrollLeft : win.pageXOffset !== void 0 ? win.pageXOffset : doc.documentElement.scrollLeft || (doc == null ? void 0 : doc.body) && ((_a2 = index.parentElement(doc.body)) == null ? void 0 : _a2.scrollLeft) || ((_b = doc == null ? void 0 : doc.body) == null ? void 0 : _b.scrollLeft) || 0,\n    top: doc.scrollingElement ? doc.scrollingElement.scrollTop : win.pageYOffset !== void 0 ? win.pageYOffset : (doc == null ? void 0 : doc.documentElement.scrollTop) || (doc == null ? void 0 : doc.body) && ((_c = index.parentElement(doc.body)) == null ? void 0 : _c.scrollTop) || ((_d = doc == null ? void 0 : doc.body) == null ? void 0 : _d.scrollTop) || 0\n  };\n}\nfunction getWindowHeight() {\n  return window.innerHeight || document.documentElement && document.documentElement.clientHeight || document.body && document.body.clientHeight;\n}\nfunction getWindowWidth() {\n  return window.innerWidth || document.documentElement && document.documentElement.clientWidth || document.body && document.body.clientWidth;\n}\nfunction closestElementOfNode(node2) {\n  if (!node2) {\n    return null;\n  }\n  const el = node2.nodeType === node2.ELEMENT_NODE ? node2 : index.parentElement(node2);\n  return el;\n}\nfunction isBlocked(node2, blockClass, blockSelector, checkAncestors) {\n  if (!node2) {\n    return false;\n  }\n  const el = closestElementOfNode(node2);\n  if (!el) {\n    return false;\n  }\n  try {\n    if (typeof blockClass === \"string\") {\n      if (el.classList.contains(blockClass)) return true;\n      if (checkAncestors && el.closest(\".\" + blockClass) !== null) return true;\n    } else {\n      if (classMatchesRegex(el, blockClass, checkAncestors)) return true;\n    }\n  } catch (e2) {\n  }\n  if (blockSelector) {\n    if (el.matches(blockSelector)) return true;\n    if (checkAncestors && el.closest(blockSelector) !== null) return true;\n  }\n  return false;\n}\nfunction isSerialized(n2, mirror2) {\n  return mirror2.getId(n2) !== -1;\n}\nfunction isIgnored(n2, mirror2, slimDOMOptions) {\n  if (n2.tagName === \"TITLE\" && slimDOMOptions.headTitleMutations) {\n    return true;\n  }\n  return mirror2.getId(n2) === IGNORED_NODE;\n}\nfunction isAncestorRemoved(target, mirror2) {\n  if (isShadowRoot(target)) {\n    return false;\n  }\n  const id = mirror2.getId(target);\n  if (!mirror2.has(id)) {\n    return true;\n  }\n  const parent = index.parentNode(target);\n  if (parent && parent.nodeType === target.DOCUMENT_NODE) {\n    return false;\n  }\n  if (!parent) {\n    return true;\n  }\n  return isAncestorRemoved(parent, mirror2);\n}\nfunction legacy_isTouchEvent(event) {\n  return Boolean(event.changedTouches);\n}\nfunction polyfill$1(win = window) {\n  if (\"NodeList\" in win && !win.NodeList.prototype.forEach) {\n    win.NodeList.prototype.forEach = Array.prototype.forEach;\n  }\n  if (\"DOMTokenList\" in win && !win.DOMTokenList.prototype.forEach) {\n    win.DOMTokenList.prototype.forEach = Array.prototype.forEach;\n  }\n}\nfunction queueToResolveTrees(queue) {\n  const queueNodeMap = {};\n  const putIntoMap = (m, parent) => {\n    const nodeInTree = {\n      value: m,\n      parent,\n      children: []\n    };\n    queueNodeMap[m.node.id] = nodeInTree;\n    return nodeInTree;\n  };\n  const queueNodeTrees = [];\n  for (const mutation of queue) {\n    const { nextId, parentId } = mutation;\n    if (nextId && nextId in queueNodeMap) {\n      const nextInTree = queueNodeMap[nextId];\n      if (nextInTree.parent) {\n        const idx = nextInTree.parent.children.indexOf(nextInTree);\n        nextInTree.parent.children.splice(\n          idx,\n          0,\n          putIntoMap(mutation, nextInTree.parent)\n        );\n      } else {\n        const idx = queueNodeTrees.indexOf(nextInTree);\n        queueNodeTrees.splice(idx, 0, putIntoMap(mutation, null));\n      }\n      continue;\n    }\n    if (parentId in queueNodeMap) {\n      const parentInTree = queueNodeMap[parentId];\n      parentInTree.children.push(putIntoMap(mutation, parentInTree));\n      continue;\n    }\n    queueNodeTrees.push(putIntoMap(mutation, null));\n  }\n  return queueNodeTrees;\n}\nfunction iterateResolveTree(tree, cb) {\n  cb(tree.value);\n  for (let i2 = tree.children.length - 1; i2 >= 0; i2--) {\n    iterateResolveTree(tree.children[i2], cb);\n  }\n}\nfunction isSerializedIframe(n2, mirror2) {\n  return Boolean(n2.nodeName === \"IFRAME\" && mirror2.getMeta(n2));\n}\nfunction isSerializedStylesheet(n2, mirror2) {\n  return Boolean(\n    n2.nodeName === \"LINK\" && n2.nodeType === n2.ELEMENT_NODE && n2.getAttribute && n2.getAttribute(\"rel\") === \"stylesheet\" && mirror2.getMeta(n2)\n  );\n}\nfunction getBaseDimension(node2, rootIframe) {\n  var _a2, _b;\n  const frameElement = (_b = (_a2 = node2.ownerDocument) == null ? void 0 : _a2.defaultView) == null ? void 0 : _b.frameElement;\n  if (!frameElement || frameElement === rootIframe) {\n    return {\n      x: 0,\n      y: 0,\n      relativeScale: 1,\n      absoluteScale: 1\n    };\n  }\n  const frameDimension = frameElement.getBoundingClientRect();\n  const frameBaseDimension = getBaseDimension(frameElement, rootIframe);\n  const relativeScale = frameDimension.height / frameElement.clientHeight;\n  return {\n    x: frameDimension.x * frameBaseDimension.relativeScale + frameBaseDimension.x,\n    y: frameDimension.y * frameBaseDimension.relativeScale + frameBaseDimension.y,\n    relativeScale,\n    absoluteScale: frameBaseDimension.absoluteScale * relativeScale\n  };\n}\nfunction hasShadowRoot(n2) {\n  if (!n2) return false;\n  if (n2 instanceof BaseRRNode && \"shadowRoot\" in n2) {\n    return Boolean(n2.shadowRoot);\n  }\n  return Boolean(index.shadowRoot(n2));\n}\nfunction getNestedRule(rules2, position) {\n  const rule2 = rules2[position[0]];\n  if (position.length === 1) {\n    return rule2;\n  } else {\n    return getNestedRule(\n      rule2.cssRules[position[1]].cssRules,\n      position.slice(2)\n    );\n  }\n}\nfunction getPositionsAndIndex(nestedIndex) {\n  const positions = [...nestedIndex];\n  const index2 = positions.pop();\n  return { positions, index: index2 };\n}\nfunction uniqueTextMutations(mutations) {\n  const idSet = /* @__PURE__ */ new Set();\n  const uniqueMutations = [];\n  for (let i2 = mutations.length; i2--; ) {\n    const mutation = mutations[i2];\n    if (!idSet.has(mutation.id)) {\n      uniqueMutations.push(mutation);\n      idSet.add(mutation.id);\n    }\n  }\n  return uniqueMutations;\n}\nclass StyleSheetMirror {\n  constructor() {\n    __publicField2(this, \"id\", 1);\n    __publicField2(this, \"styleIDMap\", /* @__PURE__ */ new WeakMap());\n    __publicField2(this, \"idStyleMap\", /* @__PURE__ */ new Map());\n  }\n  getId(stylesheet) {\n    return this.styleIDMap.get(stylesheet) ?? -1;\n  }\n  has(stylesheet) {\n    return this.styleIDMap.has(stylesheet);\n  }\n  /**\n   * @returns If the stylesheet is in the mirror, returns the id of the stylesheet. If not, return the new assigned id.\n   */\n  add(stylesheet, id) {\n    if (this.has(stylesheet)) return this.getId(stylesheet);\n    let newId;\n    if (id === void 0) {\n      newId = this.id++;\n    } else newId = id;\n    this.styleIDMap.set(stylesheet, newId);\n    this.idStyleMap.set(newId, stylesheet);\n    return newId;\n  }\n  getStyle(id) {\n    return this.idStyleMap.get(id) || null;\n  }\n  reset() {\n    this.styleIDMap = /* @__PURE__ */ new WeakMap();\n    this.idStyleMap = /* @__PURE__ */ new Map();\n    this.id = 1;\n  }\n  generateId() {\n    return this.id++;\n  }\n}\nfunction getShadowHost(n2) {\n  var _a2;\n  let shadowHost = null;\n  if (\"getRootNode\" in n2 && ((_a2 = index.getRootNode(n2)) == null ? void 0 : _a2.nodeType) === Node.DOCUMENT_FRAGMENT_NODE && index.host(index.getRootNode(n2)))\n    shadowHost = index.host(index.getRootNode(n2));\n  return shadowHost;\n}\nfunction getRootShadowHost(n2) {\n  let rootShadowHost = n2;\n  let shadowHost;\n  while (shadowHost = getShadowHost(rootShadowHost))\n    rootShadowHost = shadowHost;\n  return rootShadowHost;\n}\nfunction shadowHostInDom(n2) {\n  const doc = n2.ownerDocument;\n  if (!doc) return false;\n  const shadowHost = getRootShadowHost(n2);\n  return index.contains(doc, shadowHost);\n}\nfunction inDom(n2) {\n  const doc = n2.ownerDocument;\n  if (!doc) return false;\n  return index.contains(doc, n2) || shadowHostInDom(n2);\n}\nconst utils = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({\n  __proto__: null,\n  StyleSheetMirror,\n  get _mirror() {\n    return _mirror;\n  },\n  closestElementOfNode,\n  getBaseDimension,\n  getNestedRule,\n  getPositionsAndIndex,\n  getRootShadowHost,\n  getShadowHost,\n  getWindowHeight,\n  getWindowScroll,\n  getWindowWidth,\n  hasShadowRoot,\n  hookSetter,\n  inDom,\n  isAncestorRemoved,\n  isBlocked,\n  isIgnored,\n  isSerialized,\n  isSerializedIframe,\n  isSerializedStylesheet,\n  iterateResolveTree,\n  legacy_isTouchEvent,\n  get nowTimestamp() {\n    return nowTimestamp;\n  },\n  on,\n  patch,\n  polyfill: polyfill$1,\n  queueToResolveTrees,\n  shadowHostInDom,\n  throttle,\n  uniqueTextMutations\n}, Symbol.toStringTag, { value: \"Module\" }));\nvar chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nvar lookup = typeof Uint8Array === \"undefined\" ? [] : new Uint8Array(256);\nfor (var i$1 = 0; i$1 < chars.length; i$1++) {\n  lookup[chars.charCodeAt(i$1)] = i$1;\n}\nconst encodedJs = \"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\";\nconst decodeBase64 = (base64) => Uint8Array.from(atob(base64), (c2) => c2.charCodeAt(0));\ntypeof window !== \"undefined\" && window.Blob && new Blob([decodeBase64(encodedJs)], { type: \"text/javascript;charset=utf-8\" });\ntry {\n  if (Array.from([1], (x2) => x2 * 2)[0] !== 2) {\n    const cleanFrame = document.createElement(\"iframe\");\n    document.body.appendChild(cleanFrame);\n    Array.from = ((_a = cleanFrame.contentWindow) == null ? void 0 : _a.Array.from) || Array.from;\n    document.body.removeChild(cleanFrame);\n  }\n} catch (err) {\n  console.debug(\"Unable to override Array.from\", err);\n}\ncreateMirror$2();\nvar n;\n!function(t2) {\n  t2[t2.NotStarted = 0] = \"NotStarted\", t2[t2.Running = 1] = \"Running\", t2[t2.Stopped = 2] = \"Stopped\";\n}(n || (n = {}));\nclass StackFrame {\n  constructor(obj) {\n    __publicField(this, \"fileName\");\n    __publicField(this, \"functionName\");\n    __publicField(this, \"lineNumber\");\n    __publicField(this, \"columnNumber\");\n    this.fileName = obj.fileName || \"\";\n    this.functionName = obj.functionName || \"\";\n    this.lineNumber = obj.lineNumber;\n    this.columnNumber = obj.columnNumber;\n  }\n  toString() {\n    const lineNumber = this.lineNumber || \"\";\n    const columnNumber = this.columnNumber || \"\";\n    if (this.functionName)\n      return `${this.functionName} (${this.fileName}:${lineNumber}:${columnNumber})`;\n    return `${this.fileName}:${lineNumber}:${columnNumber}`;\n  }\n}\nconst FIREFOX_SAFARI_STACK_REGEXP = /(^|@)\\S+:\\d+/;\nconst CHROME_IE_STACK_REGEXP = /^\\s*at .*(\\S+:\\d+|\\(native\\))/m;\nconst SAFARI_NATIVE_CODE_REGEXP = /^(eval@)?(\\[native code])?$/;\nconst ErrorStackParser = {\n  /**\n   * Given an Error object, extract the most information from it.\n   */\n  parse: function(error) {\n    if (!error) {\n      return [];\n    }\n    if (\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      typeof error.stacktrace !== \"undefined\" || // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n      // @ts-ignore\n      typeof error[\"opera#sourceloc\"] !== \"undefined\"\n    ) {\n      return this.parseOpera(\n        error\n      );\n    } else if (error.stack && error.stack.match(CHROME_IE_STACK_REGEXP)) {\n      return this.parseV8OrIE(error);\n    } else if (error.stack) {\n      return this.parseFFOrSafari(error);\n    } else {\n      console.warn(\n        \"[console-record-plugin]: Failed to parse error object:\",\n        error\n      );\n      return [];\n    }\n  },\n  // Separate line and column numbers from a string of the form: (URI:Line:Column)\n  extractLocation: function(urlLike) {\n    if (urlLike.indexOf(\":\") === -1) {\n      return [urlLike];\n    }\n    const regExp = /(.+?)(?::(\\d+))?(?::(\\d+))?$/;\n    const parts = regExp.exec(urlLike.replace(/[()]/g, \"\"));\n    if (!parts) throw new Error(`Cannot parse given url: ${urlLike}`);\n    return [parts[1], parts[2] || void 0, parts[3] || void 0];\n  },\n  parseV8OrIE: function(error) {\n    const filtered = error.stack.split(\"\\n\").filter(function(line) {\n      return !!line.match(CHROME_IE_STACK_REGEXP);\n    }, this);\n    return filtered.map(function(line) {\n      if (line.indexOf(\"(eval \") > -1) {\n        line = line.replace(/eval code/g, \"eval\").replace(/(\\(eval at [^()]*)|(\\),.*$)/g, \"\");\n      }\n      let sanitizedLine = line.replace(/^\\s+/, \"\").replace(/\\(eval code/g, \"(\");\n      const location = sanitizedLine.match(/ (\\((.+):(\\d+):(\\d+)\\)$)/);\n      sanitizedLine = location ? sanitizedLine.replace(location[0], \"\") : sanitizedLine;\n      const tokens = sanitizedLine.split(/\\s+/).slice(1);\n      const locationParts = this.extractLocation(\n        location ? location[1] : tokens.pop()\n      );\n      const functionName = tokens.join(\" \") || void 0;\n      const fileName = [\"eval\", \"<anonymous>\"].indexOf(locationParts[0]) > -1 ? void 0 : locationParts[0];\n      return new StackFrame({\n        functionName,\n        fileName,\n        lineNumber: locationParts[1],\n        columnNumber: locationParts[2]\n      });\n    }, this);\n  },\n  parseFFOrSafari: function(error) {\n    const filtered = error.stack.split(\"\\n\").filter(function(line) {\n      return !line.match(SAFARI_NATIVE_CODE_REGEXP);\n    }, this);\n    return filtered.map(function(line) {\n      if (line.indexOf(\" > eval\") > -1) {\n        line = line.replace(\n          / line (\\d+)(?: > eval line \\d+)* > eval:\\d+:\\d+/g,\n          \":$1\"\n        );\n      }\n      if (line.indexOf(\"@\") === -1 && line.indexOf(\":\") === -1) {\n        return new StackFrame({\n          functionName: line\n        });\n      } else {\n        const functionNameRegex = /((.*\".+\"[^@]*)?[^@]*)(?:@)/;\n        const matches = line.match(functionNameRegex);\n        const functionName = matches && matches[1] ? matches[1] : void 0;\n        const locationParts = this.extractLocation(\n          line.replace(functionNameRegex, \"\")\n        );\n        return new StackFrame({\n          functionName,\n          fileName: locationParts[0],\n          lineNumber: locationParts[1],\n          columnNumber: locationParts[2]\n        });\n      }\n    }, this);\n  },\n  parseOpera: function(e) {\n    if (!e.stacktrace || e.message.indexOf(\"\\n\") > -1 && e.message.split(\"\\n\").length > e.stacktrace.split(\"\\n\").length) {\n      return this.parseOpera9(e);\n    } else if (!e.stack) {\n      return this.parseOpera10(e);\n    } else {\n      return this.parseOpera11(e);\n    }\n  },\n  parseOpera9: function(e) {\n    const lineRE = /Line (\\d+).*script (?:in )?(\\S+)/i;\n    const lines = e.message.split(\"\\n\");\n    const result2 = [];\n    for (let i = 2, len = lines.length; i < len; i += 2) {\n      const match = lineRE.exec(lines[i]);\n      if (match) {\n        result2.push(\n          new StackFrame({\n            fileName: match[2],\n            lineNumber: parseFloat(match[1])\n          })\n        );\n      }\n    }\n    return result2;\n  },\n  parseOpera10: function(e) {\n    const lineRE = /Line (\\d+).*script (?:in )?(\\S+)(?:: In function (\\S+))?$/i;\n    const lines = e.stacktrace.split(\"\\n\");\n    const result2 = [];\n    for (let i = 0, len = lines.length; i < len; i += 2) {\n      const match = lineRE.exec(lines[i]);\n      if (match) {\n        result2.push(\n          new StackFrame({\n            functionName: match[3] || void 0,\n            fileName: match[2],\n            lineNumber: parseFloat(match[1])\n          })\n        );\n      }\n    }\n    return result2;\n  },\n  // Opera 10.65+ Error.stack very similar to FF/Safari\n  parseOpera11: function(error) {\n    const filtered = error.stack.split(\"\\n\").filter(function(line) {\n      return !!line.match(FIREFOX_SAFARI_STACK_REGEXP) && !line.match(/^Error created at/);\n    }, this);\n    return filtered.map(function(line) {\n      const tokens = line.split(\"@\");\n      const locationParts = this.extractLocation(tokens.pop());\n      const functionCall = tokens.shift() || \"\";\n      const functionName = functionCall.replace(/<anonymous function(: (\\w+))?>/, \"$2\").replace(/\\([^)]*\\)/g, \"\") || void 0;\n      return new StackFrame({\n        functionName,\n        fileName: locationParts[0],\n        lineNumber: locationParts[1],\n        columnNumber: locationParts[2]\n      });\n    }, this);\n  }\n};\nfunction pathToSelector(node2) {\n  if (!node2 || !node2.outerHTML) {\n    return \"\";\n  }\n  let path = \"\";\n  while (node2.parentElement) {\n    let name = node2.localName;\n    if (!name) {\n      break;\n    }\n    name = name.toLowerCase();\n    const parent = node2.parentElement;\n    const domSiblings = [];\n    if (parent.children && parent.children.length > 0) {\n      for (let i = 0; i < parent.children.length; i++) {\n        const sibling = parent.children[i];\n        if (sibling.localName && sibling.localName.toLowerCase) {\n          if (sibling.localName.toLowerCase() === name) {\n            domSiblings.push(sibling);\n          }\n        }\n      }\n    }\n    if (domSiblings.length > 1) {\n      name += `:eq(${domSiblings.indexOf(node2)})`;\n    }\n    path = name + (path ? \">\" + path : \"\");\n    node2 = parent;\n  }\n  return path;\n}\nfunction isObject(obj) {\n  return Object.prototype.toString.call(obj) === \"[object Object]\";\n}\nfunction isObjTooDeep(obj, limit) {\n  if (limit === 0) {\n    return true;\n  }\n  const keys = Object.keys(obj);\n  for (const key of keys) {\n    if (isObject(obj[key]) && isObjTooDeep(obj[key], limit - 1)) {\n      return true;\n    }\n  }\n  return false;\n}\nfunction stringify(obj, stringifyOptions) {\n  const options = {\n    numOfKeysLimit: 50,\n    depthOfLimit: 4\n  };\n  Object.assign(options, stringifyOptions);\n  const stack = [];\n  const keys = [];\n  return JSON.stringify(\n    obj,\n    function(key, value) {\n      if (stack.length > 0) {\n        const thisPos = stack.indexOf(this);\n        ~thisPos ? stack.splice(thisPos + 1) : stack.push(this);\n        ~thisPos ? keys.splice(thisPos, Infinity, key) : keys.push(key);\n        if (~stack.indexOf(value)) {\n          if (stack[0] === value) {\n            value = \"[Circular ~]\";\n          } else {\n            value = \"[Circular ~.\" + keys.slice(0, stack.indexOf(value)).join(\".\") + \"]\";\n          }\n        }\n      } else {\n        stack.push(value);\n      }\n      if (value === null) return value;\n      if (value === void 0) return \"undefined\";\n      if (shouldIgnore(value)) {\n        return toString(value);\n      }\n      if (typeof value === \"bigint\") {\n        return value.toString() + \"n\";\n      }\n      if (value instanceof Event) {\n        const eventResult = {};\n        for (const eventKey in value) {\n          const eventValue = value[eventKey];\n          if (Array.isArray(eventValue)) {\n            eventResult[eventKey] = pathToSelector(\n              eventValue.length ? eventValue[0] : null\n            );\n          } else {\n            eventResult[eventKey] = eventValue;\n          }\n        }\n        return eventResult;\n      } else if (value instanceof Node) {\n        if (value instanceof HTMLElement) {\n          return value ? value.outerHTML : \"\";\n        }\n        return value.nodeName;\n      } else if (value instanceof Error) {\n        return value.stack ? value.stack + \"\\nEnd of stack for Error object\" : value.name + \": \" + value.message;\n      }\n      return value;\n    }\n  );\n  function shouldIgnore(_obj) {\n    if (isObject(_obj) && Object.keys(_obj).length > options.numOfKeysLimit) {\n      return true;\n    }\n    if (typeof _obj === \"function\") {\n      return true;\n    }\n    if (isObject(_obj) && isObjTooDeep(_obj, options.depthOfLimit)) {\n      return true;\n    }\n    return false;\n  }\n  function toString(_obj) {\n    let str = _obj.toString();\n    if (options.stringLengthLimit && str.length > options.stringLengthLimit) {\n      str = `${str.slice(0, options.stringLengthLimit)}...`;\n    }\n    return str;\n  }\n}\nconst defaultLogOptions = {\n  level: [\n    \"assert\",\n    \"clear\",\n    \"count\",\n    \"countReset\",\n    \"debug\",\n    \"dir\",\n    \"dirxml\",\n    \"error\",\n    \"group\",\n    \"groupCollapsed\",\n    \"groupEnd\",\n    \"info\",\n    \"log\",\n    \"table\",\n    \"time\",\n    \"timeEnd\",\n    \"timeLog\",\n    \"trace\",\n    \"warn\"\n  ],\n  lengthThreshold: 1e3,\n  logger: \"console\"\n};\nfunction initLogObserver(cb, win, options) {\n  const logOptions = options ? Object.assign({}, defaultLogOptions, options) : defaultLogOptions;\n  const loggerType = logOptions.logger;\n  if (!loggerType) {\n    return () => {\n    };\n  }\n  let logger;\n  if (typeof loggerType === \"string\") {\n    logger = win[loggerType];\n  } else {\n    logger = loggerType;\n  }\n  let logCount = 0;\n  let inStack = false;\n  const cancelHandlers = [];\n  if (logOptions.level.includes(\"error\")) {\n    const errorHandler = (event) => {\n      const message = event.message, error = event.error;\n      const trace = ErrorStackParser.parse(error).map(\n        (stackFrame) => stackFrame.toString()\n      );\n      const payload = [stringify(message, logOptions.stringifyOptions)];\n      cb({\n        level: \"error\",\n        trace,\n        payload\n      });\n    };\n    win.addEventListener(\"error\", errorHandler);\n    cancelHandlers.push(() => {\n      win.removeEventListener(\"error\", errorHandler);\n    });\n    const unhandledrejectionHandler = (event) => {\n      let error;\n      let payload;\n      if (event.reason instanceof Error) {\n        error = event.reason;\n        payload = [\n          stringify(\n            `Uncaught (in promise) ${error.name}: ${error.message}`,\n            logOptions.stringifyOptions\n          )\n        ];\n      } else {\n        error = new Error();\n        payload = [\n          stringify(\"Uncaught (in promise)\", logOptions.stringifyOptions),\n          stringify(event.reason, logOptions.stringifyOptions)\n        ];\n      }\n      const trace = ErrorStackParser.parse(error).map(\n        (stackFrame) => stackFrame.toString()\n      );\n      cb({\n        level: \"error\",\n        trace,\n        payload\n      });\n    };\n    win.addEventListener(\"unhandledrejection\", unhandledrejectionHandler);\n    cancelHandlers.push(() => {\n      win.removeEventListener(\"unhandledrejection\", unhandledrejectionHandler);\n    });\n  }\n  for (const levelType of logOptions.level) {\n    cancelHandlers.push(replace(logger, levelType));\n  }\n  return () => {\n    cancelHandlers.forEach((h) => h());\n  };\n  function replace(_logger, level) {\n    if (!_logger[level]) {\n      return () => {\n      };\n    }\n    return utils.patch(\n      _logger,\n      level,\n      (original) => {\n        return (...args) => {\n          original.apply(this, args);\n          if (level === \"assert\" && !!args[0]) {\n            return;\n          }\n          if (inStack) {\n            return;\n          }\n          inStack = true;\n          try {\n            const trace = ErrorStackParser.parse(new Error()).map((stackFrame) => stackFrame.toString()).splice(1);\n            const argsForPayload = level === \"assert\" ? args.slice(1) : args;\n            const payload = argsForPayload.map(\n              (s) => stringify(s, logOptions.stringifyOptions)\n            );\n            logCount++;\n            if (logCount < logOptions.lengthThreshold) {\n              cb({\n                level,\n                trace,\n                payload\n              });\n            } else if (logCount === logOptions.lengthThreshold) {\n              cb({\n                level: \"warn\",\n                trace: [],\n                payload: [\n                  stringify(\"The number of log records reached the threshold.\")\n                ]\n              });\n            }\n          } catch (error) {\n            original(\"rrweb logger error:\", error, ...args);\n          } finally {\n            inStack = false;\n          }\n        };\n      }\n    );\n  }\n}\nconst PLUGIN_NAME = \"rrweb/console@1\";\nconst getRecordConsolePlugin = (options) => ({\n  name: PLUGIN_NAME,\n  observer: initLogObserver,\n  options\n});\nexport {\n  PLUGIN_NAME,\n  getRecordConsolePlugin\n};\n//# sourceMappingURL=rrweb-plugin-console-record.js.map\n", "import { ErrorProperties } from '../extensions/exception-autocapture/error-conversion'\nimport type { PostHog } from '../posthog-core'\nimport { SessionIdManager } from '../sessionid'\nimport { DeadClicksAutoCaptureConfig, RemoteConfig, SiteAppLoader } from '../types'\n\n/*\n * Global helpers to protect access to browser globals in a way that is safer for different targets\n * like DOM, SSR, Web workers etc.\n *\n * NOTE: Typically we want the \"window\" but globalThis works for both the typical browser context as\n * well as other contexts such as the web worker context. Window is still exported for any bits that explicitly require it.\n * If in doubt - export the global you need from this file and use that as an optional value. This way the code path is forced\n * to handle the case where the global is not available.\n */\n\n// eslint-disable-next-line no-restricted-globals\nconst win: (Window & typeof globalThis) | undefined = typeof window !== 'undefined' ? window : undefined\n\nexport type AssignableWindow = Window &\n    typeof globalThis & {\n        __PosthogExtensions__?: PostHogExtensions\n\n        _POSTHOG_REMOTE_CONFIG?: Record<\n            string,\n            {\n                config: RemoteConfig\n                siteApps: SiteAppLoader[]\n            }\n        >\n\n        doNotTrack: any\n        posthogCustomizations: any\n        posthogErrorWrappingFunctions: any\n        rrweb: any\n        rrwebConsoleRecord: any\n        getRecordNetworkPlugin: any\n        POSTHOG_DEBUG: any\n        posthog: any\n        ph_load_toolbar: any\n        ph_load_editor: any\n        ph_toolbar_state: any\n        postHogWebVitalsCallbacks: any\n        postHogTracingHeadersPatchFns: any\n        extendPostHogWithSurveys: any\n    } & Record<`__$$ph_site_app_${string}`, any>\n\n/**\n * This is our contract between (potentially) lazily loaded extensions and the SDK\n * changes to this interface can be breaking changes for users of the SDK\n */\n\nexport type PostHogExtensionKind =\n    | 'toolbar'\n    | 'exception-autocapture'\n    | 'web-vitals'\n    | 'recorder'\n    | 'tracing-headers'\n    | 'surveys'\n    | 'dead-clicks-autocapture'\n    | 'remote-config'\n\nexport interface LazyLoadedDeadClicksAutocaptureInterface {\n    start: (observerTarget: Node) => void\n    stop: () => void\n}\n\ninterface PostHogExtensions {\n    loadExternalDependency?: (\n        posthog: PostHog,\n        kind: PostHogExtensionKind,\n        callback: (error?: string | Event, event?: Event) => void\n    ) => void\n\n    loadSiteApp?: (posthog: PostHog, appUrl: string, callback: (error?: string | Event, event?: Event) => void) => void\n\n    errorWrappingFunctions?: {\n        wrapOnError: (captureFn: (props: ErrorProperties) => void) => () => void\n        wrapUnhandledRejection: (captureFn: (props: ErrorProperties) => void) => () => void\n        wrapConsoleError: (captureFn: (props: ErrorProperties) => void) => () => void\n    }\n    rrweb?: { record: any; version: string }\n    rrwebPlugins?: { getRecordConsolePlugin: any; getRecordNetworkPlugin?: any }\n    generateSurveys?: (posthog: PostHog) => any | undefined\n    postHogWebVitalsCallbacks?: {\n        onLCP: (metric: any) => void\n        onCLS: (metric: any) => void\n        onFCP: (metric: any) => void\n        onINP: (metric: any) => void\n    }\n    tracingHeadersPatchFns?: {\n        _patchFetch: (sessionManager?: SessionIdManager) => () => void\n        _patchXHR: (sessionManager?: SessionIdManager) => () => void\n    }\n    initDeadClicksAutocapture?: (\n        ph: PostHog,\n        config: DeadClicksAutoCaptureConfig\n    ) => LazyLoadedDeadClicksAutocaptureInterface\n}\n\nconst global: typeof globalThis | undefined = typeof globalThis !== 'undefined' ? globalThis : win\n\nexport const ArrayProto = Array.prototype\nexport const nativeForEach = ArrayProto.forEach\nexport const nativeIndexOf = ArrayProto.indexOf\n\nexport const navigator = global?.navigator\nexport const document = global?.document\nexport const location = global?.location\nexport const fetch = global?.fetch\nexport const XMLHttpRequest =\n    global?.XMLHttpRequest && 'withCredentials' in new global.XMLHttpRequest() ? global.XMLHttpRequest : undefined\nexport const AbortController = global?.AbortController\nexport const userAgent = navigator?.userAgent\nexport const assignableWindow: AssignableWindow = win ?? ({} as any)\n\nexport { win as window }\n", "import { window } from './globals'\nimport { knownUnsafeEditableEvent, KnownUnsafeEditableEvent } from '../types'\nimport { includes } from './string-utils'\n\n// eslint-disable-next-line posthog-js/no-direct-array-check\nconst nativeIsArray = Array.isArray\nconst ObjProto = Object.prototype\nexport const hasOwnProperty = ObjProto.hasOwnProperty\nconst toString = ObjProto.toString\n\nexport const isArray =\n    nativeIsArray ||\n    function (obj: any): obj is any[] {\n        return toString.call(obj) === '[object Array]'\n    }\n\n// from a comment on http://dbj.org/dbj/?p=286\n// fails on only one very rare and deliberate custom object:\n// let bomb = { toString : undefined, valueOf: function(o) { return \"function BOMBA!\"; }};\nexport const isFunction = (x: unknown): x is (...args: any[]) => any => {\n    // eslint-disable-next-line posthog-js/no-direct-function-check\n    return typeof x === 'function'\n}\n\nexport const isNativeFunction = (x: unknown): x is (...args: any[]) => any =>\n    isFunction(x) && x.toString().indexOf('[native code]') !== -1\n\n// When angular patches functions they pass the above `isNativeFunction` check (at least the MutationObserver)\nexport const isAngularZonePresent = (): boolean => {\n    return !!(window as any).Zone\n}\n\n// Underscore Addons\nexport const isObject = (x: unknown): x is Record<string, any> => {\n    // eslint-disable-next-line posthog-js/no-direct-object-check\n    return x === Object(x) && !isArray(x)\n}\nexport const isEmptyObject = (x: unknown) => {\n    if (isObject(x)) {\n        for (const key in x) {\n            if (hasOwnProperty.call(x, key)) {\n                return false\n            }\n        }\n        return true\n    }\n    return false\n}\nexport const isUndefined = (x: unknown): x is undefined => x === void 0\n\nexport const isString = (x: unknown): x is string => {\n    // eslint-disable-next-line posthog-js/no-direct-string-check\n    return toString.call(x) == '[object String]'\n}\n\nexport const isEmptyString = (x: unknown): boolean => isString(x) && x.trim().length === 0\n\nexport const isNull = (x: unknown): x is null => {\n    // eslint-disable-next-line posthog-js/no-direct-null-check\n    return x === null\n}\n\n/*\n    sometimes you want to check if something is null or undefined\n    that's what this is for\n */\nexport const isNullish = (x: unknown): x is null | undefined => isUndefined(x) || isNull(x)\n\nexport const isNumber = (x: unknown): x is number => {\n    // eslint-disable-next-line posthog-js/no-direct-number-check\n    return toString.call(x) == '[object Number]'\n}\nexport const isBoolean = (x: unknown): x is boolean => {\n    // eslint-disable-next-line posthog-js/no-direct-boolean-check\n    return toString.call(x) === '[object Boolean]'\n}\n\nexport const isDocument = (x: unknown): x is Document => {\n    // eslint-disable-next-line posthog-js/no-direct-document-check\n    return x instanceof Document\n}\n\nexport const isFormData = (x: unknown): x is FormData => {\n    // eslint-disable-next-line posthog-js/no-direct-form-data-check\n    return x instanceof FormData\n}\n\nexport const isFile = (x: unknown): x is File => {\n    // eslint-disable-next-line posthog-js/no-direct-file-check\n    return x instanceof File\n}\n\nexport const isError = (x: unknown): x is Error => {\n    return x instanceof Error\n}\n\nexport const isKnownUnsafeEditableEvent = (x: unknown): x is KnownUnsafeEditableEvent => {\n    return includes(knownUnsafeEditableEvent as unknown as string[], x)\n}\n", "import Config from '../config'\nimport { isUndefined } from './type-utils'\nimport { assignableWindow, window } from './globals'\n\nexport type Logger = {\n    _log: (level: 'log' | 'warn' | 'error', ...args: any[]) => void\n    info: (...args: any[]) => void\n    warn: (...args: any[]) => void\n    error: (...args: any[]) => void\n    critical: (...args: any[]) => void\n    uninitializedWarning: (methodName: string) => void\n    createLogger: (prefix: string) => Logger\n}\n\nconst _createLogger = (prefix: string): Logger => {\n    const logger: Logger = {\n        _log: (level: 'log' | 'warn' | 'error', ...args: any[]) => {\n            if (\n                window &&\n                (Config.DEBUG || assignableWindow.POSTHOG_DEBUG) &&\n                !isUndefined(window.console) &&\n                window.console\n            ) {\n                const consoleLog =\n                    '__rrweb_original__' in window.console[level]\n                        ? (window.console[level] as any)['__rrweb_original__']\n                        : window.console[level]\n\n                // eslint-disable-next-line no-console\n                consoleLog(prefix, ...args)\n            }\n        },\n\n        info: (...args: any[]) => {\n            logger._log('log', ...args)\n        },\n\n        warn: (...args: any[]) => {\n            logger._log('warn', ...args)\n        },\n\n        error: (...args: any[]) => {\n            logger._log('error', ...args)\n        },\n\n        critical: (...args: any[]) => {\n            // Critical errors are always logged to the console\n            // eslint-disable-next-line no-console\n            console.error(prefix, ...args)\n        },\n\n        uninitializedWarning: (methodName: string) => {\n            logger.error(`You must initialize PostHog before calling ${methodName}`)\n        },\n\n        createLogger: (additionalPrefix: string) => _createLogger(`${prefix} ${additionalPrefix}`),\n    }\n    return logger\n}\n\nexport const logger = _createLogger('[PostHog.js]')\n\nexport const createLogger = logger.createLogger\n", "import { Breaker, Properties } from '../types'\nimport { nativeForEach, nativeIndexOf } from './globals'\nimport { logger } from './logger'\nimport { hasOwnProperty, isArray, isFormData, isNull, isNullish, isNumber, isString, isUndefined } from './type-utils'\n\nconst breaker: Breaker = {}\n\nexport function eachArray<E = any>(\n    obj: E[] | null | undefined,\n    iterator: (value: E, key: number) => void | Breaker,\n    thisArg?: any\n): void {\n    if (isArray(obj)) {\n        if (nativeForEach && obj.forEach === nativeForEach) {\n            obj.forEach(iterator, thisArg)\n        } else if ('length' in obj && obj.length === +obj.length) {\n            for (let i = 0, l = obj.length; i < l; i++) {\n                if (i in obj && iterator.call(thisArg, obj[i], i) === breaker) {\n                    return\n                }\n            }\n        }\n    }\n}\n\n/**\n * @param {*=} obj\n * @param {function(...*)=} iterator\n * @param {Object=} thisArg\n */\nexport function each(obj: any, iterator: (value: any, key: any) => void | Breaker, thisArg?: any): void {\n    if (isNullish(obj)) {\n        return\n    }\n    if (isArray(obj)) {\n        return eachArray(obj, iterator, thisArg)\n    }\n    if (isFormData(obj)) {\n        for (const pair of obj.entries()) {\n            if (iterator.call(thisArg, pair[1], pair[0]) === breaker) {\n                return\n            }\n        }\n        return\n    }\n    for (const key in obj) {\n        if (hasOwnProperty.call(obj, key)) {\n            if (iterator.call(thisArg, obj[key], key) === breaker) {\n                return\n            }\n        }\n    }\n}\n\nexport const extend = function (obj: Record<string, any>, ...args: Record<string, any>[]): Record<string, any> {\n    eachArray(args, function (source) {\n        for (const prop in source) {\n            if (source[prop] !== void 0) {\n                obj[prop] = source[prop]\n            }\n        }\n    })\n    return obj\n}\n\nexport const extendArray = function <T>(obj: T[], ...args: T[][]): T[] {\n    eachArray(args, function (source) {\n        eachArray(source, function (item) {\n            obj.push(item)\n        })\n    })\n    return obj\n}\n\nexport const include = function (\n    obj: null | string | Array<any> | Record<string, any>,\n    target: any\n): boolean | Breaker {\n    let found = false\n    if (isNull(obj)) {\n        return found\n    }\n    if (nativeIndexOf && obj.indexOf === nativeIndexOf) {\n        return obj.indexOf(target) != -1\n    }\n    each(obj, function (value) {\n        if (found || (found = value === target)) {\n            return breaker\n        }\n        return\n    })\n    return found\n}\n\n/**\n * Object.entries() polyfill\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/entries\n */\nexport function entries<T = any>(obj: Record<string, T>): [string, T][] {\n    const ownProps = Object.keys(obj)\n    let i = ownProps.length\n    const resArray = new Array(i) // preallocate the Array\n\n    while (i--) {\n        resArray[i] = [ownProps[i], obj[ownProps[i]]]\n    }\n    return resArray\n}\n\nexport const trySafe = function <T>(fn: () => T): T | undefined {\n    try {\n        return fn()\n    } catch {\n        return undefined\n    }\n}\n\nexport const safewrap = function <F extends (...args: any[]) => any = (...args: any[]) => any>(f: F): F {\n    return function (...args) {\n        try {\n            // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n            // @ts-ignore\n            return f.apply(this, args)\n        } catch (e) {\n            logger.critical(\n                'Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A.'\n            )\n            logger.critical(e)\n        }\n    } as F\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport const safewrapClass = function (klass: Function, functions: string[]): void {\n    for (let i = 0; i < functions.length; i++) {\n        klass.prototype[functions[i]] = safewrap(klass.prototype[functions[i]])\n    }\n}\n\nexport const stripEmptyProperties = function (p: Properties): Properties {\n    const ret: Properties = {}\n    each(p, function (v, k) {\n        if ((isString(v) && v.length > 0) || isNumber(v)) {\n            ret[k] = v\n        }\n    })\n    return ret\n}\n\n/**\n * Deep copies an object.\n * It handles cycles by replacing all references to them with `undefined`\n * Also supports customizing native values\n *\n * @param value\n * @param customizer\n * @returns {{}|undefined|*}\n */\nfunction deepCircularCopy<T extends Record<string, any> = Record<string, any>>(\n    value: T,\n    customizer?: <K extends keyof T = keyof T>(value: T[K], key?: K) => T[K]\n): T | undefined {\n    const COPY_IN_PROGRESS_SET = new Set()\n\n    function internalDeepCircularCopy(value: T, key?: string): T | undefined {\n        if (value !== Object(value)) return customizer ? customizer(value as any, key) : value // primitive value\n\n        if (COPY_IN_PROGRESS_SET.has(value)) return undefined\n        COPY_IN_PROGRESS_SET.add(value)\n        let result: T\n\n        if (isArray(value)) {\n            result = [] as any as T\n            eachArray(value, (it) => {\n                result.push(internalDeepCircularCopy(it))\n            })\n        } else {\n            result = {} as T\n            each(value, (val, key) => {\n                if (!COPY_IN_PROGRESS_SET.has(val)) {\n                    ;(result as any)[key] = internalDeepCircularCopy(val, key)\n                }\n            })\n        }\n        return result\n    }\n    return internalDeepCircularCopy(value)\n}\n\nexport function _copyAndTruncateStrings<T extends Record<string, any> = Record<string, any>>(\n    object: T,\n    maxStringLength: number | null\n): T {\n    return deepCircularCopy(object, (value: any) => {\n        if (isString(value) && !isNull(maxStringLength)) {\n            return (value as string).slice(0, maxStringLength)\n        }\n        return value\n    }) as T\n}\n\n// NOTE: Update PostHogConfig docs if you change this list\n// We will not try to catch all bullets here, but we should make an effort to catch the most common ones\n// You should be highly against adding more to this list, because ultimately customers can configure\n// their `cross_subdomain_cookie` setting to anything they want.\nconst EXCLUDED_FROM_CROSS_SUBDOMAIN_COOKIE = ['herokuapp.com', 'vercel.app', 'netlify.app']\nexport function isCrossDomainCookie(documentLocation: Location | undefined) {\n    const hostname = documentLocation?.hostname\n\n    if (!isString(hostname)) {\n        return false\n    }\n    // split and slice isn't a great way to match arbitrary domains,\n    // but it's good enough for ensuring we only match herokuapp.com when it is the TLD\n    // for the hostname\n    const lastTwoParts = hostname.split('.').slice(-2).join('.')\n\n    for (const excluded of EXCLUDED_FROM_CROSS_SUBDOMAIN_COOKIE) {\n        if (lastTwoParts === excluded) {\n            return false\n        }\n    }\n\n    return true\n}\n\nexport function find<T>(value: T[], predicate: (value: T) => boolean): T | undefined {\n    for (let i = 0; i < value.length; i++) {\n        if (predicate(value[i])) {\n            return value[i]\n        }\n    }\n    return undefined\n}\n\n// Use this instead of element.addEventListener to avoid eslint errors\n// this properly implements the default options for passive event listeners\nexport function addEventListener(\n    element: Window | Document | Element | undefined,\n    event: string,\n    callback: EventListener,\n    options?: AddEventListenerOptions\n): void {\n    const { capture = false, passive = true } = options ?? {}\n\n    // This is the only place where we are allowed to call this function\n    // because the whole idea is that we should be calling this instead of the built-in one\n    // eslint-disable-next-line posthog-js/no-add-event-listener\n    element?.addEventListener(event, callback, { capture, passive })\n}\n\n/**\n * Helper to migrate deprecated config fields to new field names with appropriate warnings\n * @param config - The config object to check\n * @param newField - The new field name to use\n * @param oldField - The deprecated field name to check for\n * @param defaultValue - The default value if neither field is set\n * @param loggerInstance - Optional logger instance for deprecation warnings\n * @returns The value to use (new field takes precedence over old field)\n */\nexport function migrateConfigField<T>(\n    config: Record<string, any>,\n    newField: string,\n    oldField: string,\n    defaultValue: T,\n    loggerInstance?: { warn: (message: string) => void }\n): T {\n    const hasNewField = newField in config && !isUndefined(config[newField])\n    const hasOldField = oldField in config && !isUndefined(config[oldField])\n\n    if (hasNewField) {\n        return config[newField]\n    }\n\n    if (hasOldField) {\n        if (loggerInstance) {\n            loggerInstance.warn(\n                `Config field '${oldField}' is deprecated. Please use '${newField}' instead. ` +\n                    `The old field will be removed in a future major version.`\n            )\n        }\n        return config[oldField]\n    }\n\n    return defaultValue\n}\n", "import { each } from './'\n\nimport { isArray, isFile, isUndefined } from './type-utils'\nimport { logger } from './logger'\nimport { document } from './globals'\n\nconst localDomains = ['localhost', '127.0.0.1']\n\n/**\n * IE11 doesn't support `new URL`\n * so we can create an anchor element and use that to parse the URL\n * there's a lot of overlap between HTMLHyperlinkElementUtils and URL\n * meaning useful properties like `pathname` are available on both\n */\nexport const convertToURL = (url: string): HTMLAnchorElement | null => {\n    const location = document?.createElement('a')\n    if (isUndefined(location)) {\n        return null\n    }\n\n    location.href = url\n    return location\n}\n\nexport const formDataToQuery = function (formdata: Record<string, any> | FormData, arg_separator = '&'): string {\n    let use_val: string\n    let use_key: string\n    const tph_arr: string[] = []\n\n    each(formdata, function (val: File | string | undefined, key: string | undefined) {\n        // the key might be literally the string undefined for e.g. if {undefined: 'something'}\n        if (isUndefined(val) || isUndefined(key) || key === 'undefined') {\n            return\n        }\n\n        use_val = encodeURIComponent(isFile(val) ? val.name : val.toString())\n        use_key = encodeURIComponent(key)\n        tph_arr[tph_arr.length] = use_key + '=' + use_val\n    })\n\n    return tph_arr.join(arg_separator)\n}\n\n// NOTE: Once we get rid of IE11/op_mini we can start using URLSearchParams\nexport const getQueryParam = function (url: string, param: string): string {\n    const withoutHash: string = url.split('#')[0] || ''\n\n    // Split only on the first ? to sort problem out for those with multiple ?s\n    // and then remove them\n    const queryParams: string = withoutHash.split(/\\?(.*)/)[1] || ''\n    const cleanedQueryParams = queryParams.replace(/^\\?+/g, '')\n\n    const queryParts = cleanedQueryParams.split('&')\n    let keyValuePair\n\n    for (let i = 0; i < queryParts.length; i++) {\n        const parts = queryParts[i].split('=')\n        if (parts[0] === param) {\n            keyValuePair = parts\n            break\n        }\n    }\n\n    if (!isArray(keyValuePair) || keyValuePair.length < 2) {\n        return ''\n    } else {\n        let result = keyValuePair[1]\n        try {\n            result = decodeURIComponent(result)\n        } catch {\n            logger.error('Skipping decoding for malformed query param: ' + result)\n        }\n        return result.replace(/\\+/g, ' ')\n    }\n}\n\n// replace any query params in the url with the provided mask value. Tries to keep the URL as instant as possible,\n// including preserving malformed text in most cases\nexport const maskQueryParams = function <T extends string | undefined>(\n    url: T,\n    maskedParams: string[] | undefined,\n    mask: string\n): T extends string ? string : undefined {\n    if (!url || !maskedParams || !maskedParams.length) {\n        return url as any\n    }\n\n    const splitHash = url.split('#')\n    const withoutHash: string = splitHash[0] || ''\n    const hash = splitHash[1]\n\n    const splitQuery: string[] = withoutHash.split('?')\n    const queryString: string = splitQuery[1]\n    const urlWithoutQueryAndHash: string = splitQuery[0]\n    const queryParts = (queryString || '').split('&')\n\n    // use an array of strings rather than an object to preserve ordering and duplicates\n    const paramStrings: string[] = []\n\n    for (let i = 0; i < queryParts.length; i++) {\n        const keyValuePair = queryParts[i].split('=')\n        if (!isArray(keyValuePair)) {\n            continue\n        } else if (maskedParams.includes(keyValuePair[0])) {\n            paramStrings.push(keyValuePair[0] + '=' + mask)\n        } else {\n            paramStrings.push(queryParts[i])\n        }\n    }\n\n    let result = urlWithoutQueryAndHash\n    if (queryString != null) {\n        result += '?' + paramStrings.join('&')\n    }\n    if (hash != null) {\n        result += '#' + hash\n    }\n\n    return result as any\n}\n\nexport const _getHashParam = function (hash: string, param: string): string | null {\n    const matches = hash.match(new RegExp(param + '=([^&]*)'))\n    return matches ? matches[1] : null\n}\n\nexport const isLocalhost = (): boolean => {\n    return localDomains.includes(location.hostname)\n}\n", "// import { patch } from 'rrweb/typings/utils'\n// copied from https://github.com/rrweb-io/rrweb/blob/8aea5b00a4dfe5a6f59bd2ae72bb624f45e51e81/packages/rrweb/src/utils.ts#L129\n// which was copied from https://github.com/getsentry/sentry-javascript/blob/b2109071975af8bf0316d3b5b38f519bdaf5dc15/packages/utils/src/object.ts\nimport { isFunction } from '../../../utils/type-utils'\n\nexport function patch(\n    source: { [key: string]: any },\n    name: string,\n    replacement: (...args: unknown[]) => unknown\n): () => void {\n    try {\n        if (!(name in source)) {\n            return () => {\n                //\n            }\n        }\n\n        const original = source[name] as () => unknown\n        const wrapped = replacement(original)\n\n        // Make sure it's a function first, as we need to attach an empty prototype for `defineProperties` to work\n        // otherwise it'll throw \"TypeError: Object.defineProperties called on non-object\"\n        if (isFunction(wrapped)) {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            wrapped.prototype = wrapped.prototype || {}\n            Object.defineProperties(wrapped, {\n                __posthog_wrapped__: {\n                    enumerable: false,\n                    value: true,\n                },\n            })\n        }\n\n        source[name] = wrapped\n\n        return () => {\n            source[name] = original\n        }\n    } catch {\n        return () => {\n            //\n        }\n        // This can throw if multiple fill happens on a global object like XMLHttpRequest\n        // Fixes https://github.com/getsentry/sentry-javascript/issues/2043\n    }\n}\n", "import { NetworkRecordOptions } from '../../../types'\n\nfunction hostnameFromURL(url: string | URL | RequestInfo): string | null {\n    try {\n        if (typeof url === 'string') {\n            return new URL(url).hostname\n        }\n        if ('url' in url) {\n            return new URL(url.url).hostname\n        }\n        return url.hostname\n    } catch {\n        return null\n    }\n}\n\nexport function isHostOnDenyList(url: string | URL | Request, options: NetworkRecordOptions) {\n    const hostname = hostnameFromURL(url)\n    const defaultNotDenied = { hostname, isHostDenied: false }\n\n    if (!options.payloadHostDenyList?.length || !hostname?.trim().length) {\n        return defaultNotDenied\n    }\n\n    for (const deny of options.payloadHostDenyList) {\n        if (hostname.endsWith(deny)) {\n            return { hostname, isHostDenied: true }\n        }\n    }\n\n    return defaultNotDenied\n}\n", "import { CapturedNetworkRequest, NetworkRecordOptions, PostHogConfig } from '../../types'\nimport { isFunction, isNullish, isString, isUndefined } from '../../utils/type-utils'\nimport { convertToURL } from '../../utils/request-utils'\nimport { logger } from '../../utils/logger'\nimport { shouldCaptureValue } from '../../autocapture-utils'\nimport { each } from '../../utils'\n\nconst LOGGER_PREFIX = '[SessionRecording]'\n\nconst REDACTED = 'redacted'\n\nexport const defaultNetworkOptions: Required<NetworkRecordOptions> = {\n    initiatorTypes: [\n        'audio',\n        'beacon',\n        'body',\n        'css',\n        'early-hint',\n        'embed',\n        'fetch',\n        'frame',\n        'iframe',\n        'icon',\n        'image',\n        'img',\n        'input',\n        'link',\n        'navigation',\n        'object',\n        'ping',\n        'script',\n        'track',\n        'video',\n        'xmlhttprequest',\n    ],\n    maskRequestFn: (data: CapturedNetworkRequest) => data,\n    recordHeaders: false,\n    recordBody: false,\n    recordInitialRequests: false,\n    recordPerformance: false,\n    performanceEntryTypeToObserve: [\n        // 'event', // This is too noisy as it covers all browser events\n        'first-input',\n        // 'mark', // Mark is used too liberally. We would need to filter for specific marks\n        // 'measure', // Measure is used too liberally. We would need to filter for specific measures\n        'navigation',\n        'paint',\n        'resource',\n    ],\n    payloadSizeLimitBytes: 1000000,\n    payloadHostDenyList: [\n        '.lr-ingest.io',\n        '.ingest.sentry.io',\n        '.clarity.ms',\n        // NB no leading dot here\n        'analytics.google.com',\n        'bam.nr-data.net',\n    ],\n}\n\nconst HEADER_DENY_LIST = [\n    'authorization',\n    'x-forwarded-for',\n    'authorization',\n    'cookie',\n    'set-cookie',\n    'x-api-key',\n    'x-real-ip',\n    'remote-addr',\n    'forwarded',\n    'proxy-authorization',\n    'x-csrf-token',\n    'x-csrftoken',\n    'x-xsrf-token',\n]\n\nconst PAYLOAD_CONTENT_DENY_LIST = [\n    'password',\n    'secret',\n    'passwd',\n    'api_key',\n    'apikey',\n    'auth',\n    'credentials',\n    'mysql_pwd',\n    'privatekey',\n    'private_key',\n    'token',\n]\n\n// we always remove headers on the deny list because we never want to capture this sensitive data\nconst removeAuthorizationHeader = (data: CapturedNetworkRequest): CapturedNetworkRequest => {\n    const headers = data.requestHeaders\n    if (!isNullish(headers)) {\n        each(Object.keys(headers ?? {}), (header) => {\n            if (HEADER_DENY_LIST.includes(header.toLowerCase())) {\n                headers[header] = REDACTED\n            }\n        })\n    }\n    return data\n}\n\nconst POSTHOG_PATHS_TO_IGNORE = ['/s/', '/e/', '/i/']\n// want to ignore posthog paths when capturing requests, or we can get trapped in a loop\n// because calls to PostHog would be reported using a call to PostHog which would be reported....\nconst ignorePostHogPaths = (\n    data: CapturedNetworkRequest,\n    apiHostConfig: PostHogConfig['api_host']\n): CapturedNetworkRequest | undefined => {\n    const url = convertToURL(data.name)\n\n    // we need to account for api host config as e.g. pathname could be /ingest/s/ and we want to ignore that\n    let replaceValue = apiHostConfig.indexOf('http') === 0 ? convertToURL(apiHostConfig)?.pathname : apiHostConfig\n    if (replaceValue === '/') {\n        replaceValue = ''\n    }\n    const pathname = url?.pathname.replace(replaceValue || '', '')\n\n    if (url && pathname && POSTHOG_PATHS_TO_IGNORE.some((path) => pathname.indexOf(path) === 0)) {\n        return undefined\n    }\n    return data\n}\n\nfunction estimateBytes(payload: string): number {\n    return new Blob([payload]).size\n}\n\nfunction enforcePayloadSizeLimit(\n    payload: string | null | undefined,\n    headers: Record<string, any> | undefined,\n    limit: number,\n    description: string\n): string | null | undefined {\n    if (isNullish(payload)) {\n        return payload\n    }\n\n    let requestContentLength: string | number = headers?.['content-length'] || estimateBytes(payload)\n    if (isString(requestContentLength)) {\n        requestContentLength = parseInt(requestContentLength)\n    }\n\n    if (requestContentLength > limit) {\n        return LOGGER_PREFIX + ` ${description} body too large to record (${requestContentLength} bytes)`\n    }\n\n    return payload\n}\n\n// people can have arbitrarily large payloads on their site, but we don't want to ingest them\nconst limitPayloadSize = (\n    options: NetworkRecordOptions\n): ((data: CapturedNetworkRequest | undefined) => CapturedNetworkRequest | undefined) => {\n    // the smallest of 1MB or the specified limit if there is one\n    const limit = Math.min(1000000, options.payloadSizeLimitBytes ?? 1000000)\n\n    return (data) => {\n        if (data?.requestBody) {\n            data.requestBody = enforcePayloadSizeLimit(data.requestBody, data.requestHeaders, limit, 'Request')\n        }\n\n        if (data?.responseBody) {\n            data.responseBody = enforcePayloadSizeLimit(data.responseBody, data.responseHeaders, limit, 'Response')\n        }\n\n        return data\n    }\n}\n\nfunction scrubPayload(payload: string | null | undefined, label: 'Request' | 'Response'): string | null | undefined {\n    if (isNullish(payload)) {\n        return payload\n    }\n    let scrubbed = payload\n\n    if (!shouldCaptureValue(scrubbed, false)) {\n        scrubbed = LOGGER_PREFIX + ' ' + label + ' body ' + REDACTED\n    }\n    each(PAYLOAD_CONTENT_DENY_LIST, (text) => {\n        if (scrubbed?.length && scrubbed?.indexOf(text) !== -1) {\n            scrubbed = LOGGER_PREFIX + ' ' + label + ' body ' + REDACTED + ' as might contain: ' + text\n        }\n    })\n\n    return scrubbed\n}\n\nfunction scrubPayloads(capturedRequest: CapturedNetworkRequest | undefined): CapturedNetworkRequest | undefined {\n    if (isUndefined(capturedRequest)) {\n        return undefined\n    }\n\n    capturedRequest.requestBody = scrubPayload(capturedRequest.requestBody, 'Request')\n    capturedRequest.responseBody = scrubPayload(capturedRequest.responseBody, 'Response')\n\n    return capturedRequest\n}\n\n/**\n *  whether a maskRequestFn is provided or not,\n *  we ensure that we remove the denied header from requests\n *  we _never_ want to record that header by accident\n *  if someone complains then we'll add an opt-in to let them override it\n */\nexport const buildNetworkRequestOptions = (\n    instanceConfig: PostHogConfig,\n    remoteNetworkOptions: Pick<\n        NetworkRecordOptions,\n        'recordHeaders' | 'recordBody' | 'recordPerformance' | 'payloadHostDenyList'\n    >\n): NetworkRecordOptions => {\n    const config: NetworkRecordOptions = {\n        payloadSizeLimitBytes: defaultNetworkOptions.payloadSizeLimitBytes,\n        performanceEntryTypeToObserve: [...defaultNetworkOptions.performanceEntryTypeToObserve],\n        payloadHostDenyList: [\n            ...(remoteNetworkOptions.payloadHostDenyList || []),\n            ...defaultNetworkOptions.payloadHostDenyList,\n        ],\n    }\n    // client can always disable despite remote options\n    const canRecordHeaders =\n        instanceConfig.session_recording.recordHeaders === false ? false : remoteNetworkOptions.recordHeaders\n    const canRecordBody =\n        instanceConfig.session_recording.recordBody === false ? false : remoteNetworkOptions.recordBody\n    const canRecordPerformance =\n        instanceConfig.capture_performance === false ? false : remoteNetworkOptions.recordPerformance\n\n    const payloadLimiter = limitPayloadSize(config)\n\n    const enforcedCleaningFn: NetworkRecordOptions['maskRequestFn'] = (d: CapturedNetworkRequest) =>\n        payloadLimiter(ignorePostHogPaths(removeAuthorizationHeader(d), instanceConfig.api_host))\n\n    const hasDeprecatedMaskFunction = isFunction(instanceConfig.session_recording.maskNetworkRequestFn)\n\n    if (hasDeprecatedMaskFunction && isFunction(instanceConfig.session_recording.maskCapturedNetworkRequestFn)) {\n        logger.warn(\n            'Both `maskNetworkRequestFn` and `maskCapturedNetworkRequestFn` are defined. `maskNetworkRequestFn` will be ignored.'\n        )\n    }\n\n    if (hasDeprecatedMaskFunction) {\n        instanceConfig.session_recording.maskCapturedNetworkRequestFn = (data: CapturedNetworkRequest) => {\n            const cleanedURL = instanceConfig.session_recording.maskNetworkRequestFn!({ url: data.name })\n            return {\n                ...data,\n                name: cleanedURL?.url,\n            } as CapturedNetworkRequest\n        }\n    }\n\n    config.maskRequestFn = isFunction(instanceConfig.session_recording.maskCapturedNetworkRequestFn)\n        ? (data) => {\n              const cleanedRequest = enforcedCleaningFn(data)\n              return cleanedRequest\n                  ? (instanceConfig.session_recording.maskCapturedNetworkRequestFn?.(cleanedRequest) ?? undefined)\n                  : undefined\n          }\n        : (data) => scrubPayloads(enforcedCleaningFn(data))\n\n    return {\n        ...defaultNetworkOptions,\n        ...config,\n        recordHeaders: canRecordHeaders,\n        recordBody: canRecordBody,\n        recordPerformance: canRecordPerformance,\n        recordInitialRequests: canRecordPerformance,\n    }\n}\n", "/// <reference lib=\"dom\" />\n\n// rrweb/network@1 code starts\n// most of what is below here will be removed when rrweb release their code for this\n// see https://github.com/rrweb-io/rrweb/pull/1105\n\n// NB adopted from https://github.com/rrweb-io/rrweb/pull/1105 which looks like it will be accepted into rrweb\n// however, in the PR, it throws when the performance observer data is not available\n// and assumes it is running in a browser with the Request API (i.e. not IE11)\n// copying here so that we can use it before rrweb adopt it\nimport type { IWindow, listenerHandler, RecordPlugin } from '@rrweb/types'\nimport { CapturedNetworkRequest, Headers, InitiatorType, NetworkRecordOptions } from '../../../types'\nimport {\n    isArray,\n    isBoolean,\n    isDocument,\n    isFormData,\n    isNull,\n    isNullish,\n    isObject,\n    isString,\n    isUndefined,\n} from '../../../utils/type-utils'\nimport { createLogger } from '../../../utils/logger'\nimport { formDataToQuery } from '../../../utils/request-utils'\nimport { patch } from '../rrweb-plugins/patch'\nimport { isHostOnDenyList } from '../../../extensions/replay/external/denylist'\nimport { defaultNetworkOptions } from '../config'\n\nconst logger = createLogger('[Recorder]')\n\nexport type NetworkData = {\n    requests: CapturedNetworkRequest[]\n    isInitial?: boolean\n}\n\ntype networkCallback = (data: NetworkData) => void\n\nconst isNavigationTiming = (entry: PerformanceEntry): entry is PerformanceNavigationTiming =>\n    entry.entryType === 'navigation'\nconst isResourceTiming = (entry: PerformanceEntry): entry is PerformanceResourceTiming => entry.entryType === 'resource'\n\ntype ObservedPerformanceEntry = (PerformanceNavigationTiming | PerformanceResourceTiming) & {\n    responseStatus?: number\n}\n\nexport function findLast<T>(array: Array<T>, predicate: (value: T) => boolean): T | undefined {\n    const length = array.length\n    for (let i = length - 1; i >= 0; i -= 1) {\n        if (predicate(array[i])) {\n            return array[i]\n        }\n    }\n    return undefined\n}\n\nfunction initPerformanceObserver(cb: networkCallback, win: IWindow, options: Required<NetworkRecordOptions>) {\n    // if we are only observing timings then we could have a single observer for all types, with buffer true,\n    // but we are going to filter by initiatorType _if we are wrapping fetch and xhr as the wrapped functions\n    // will deal with those.\n    // so we have a block which captures requests from before fetch/xhr is wrapped\n    // these are marked `isInitial` so playback can display them differently if needed\n    // they will never have method/status/headers/body because they are pre-wrapping that provides that\n    if (options.recordInitialRequests) {\n        const initialPerformanceEntries = win.performance\n            .getEntries()\n            .filter(\n                (entry): entry is ObservedPerformanceEntry =>\n                    isNavigationTiming(entry) ||\n                    (isResourceTiming(entry) && options.initiatorTypes.includes(entry.initiatorType as InitiatorType))\n            )\n        cb({\n            requests: initialPerformanceEntries.flatMap((entry) =>\n                prepareRequest({ entry, method: undefined, status: undefined, networkRequest: {}, isInitial: true })\n            ),\n            isInitial: true,\n        })\n    }\n    const observer = new win.PerformanceObserver((entries) => {\n        // if recordBody or recordHeaders is true then we don't want to record fetch or xhr here\n        // as the wrapped functions will do that. Otherwise, this filter becomes a noop\n        // because we do want to record them here\n        const wrappedInitiatorFilter = (entry: ObservedPerformanceEntry) =>\n            options.recordBody || options.recordHeaders\n                ? entry.initiatorType !== 'xmlhttprequest' && entry.initiatorType !== 'fetch'\n                : true\n\n        const performanceEntries = entries.getEntries().filter(\n            (entry): entry is ObservedPerformanceEntry =>\n                isNavigationTiming(entry) ||\n                (isResourceTiming(entry) &&\n                    options.initiatorTypes.includes(entry.initiatorType as InitiatorType) &&\n                    // TODO if we are _only_ capturing timing we don't want to filter initiator here\n                    wrappedInitiatorFilter(entry))\n        )\n\n        cb({\n            requests: performanceEntries.flatMap((entry) =>\n                prepareRequest({ entry, method: undefined, status: undefined, networkRequest: {} })\n            ),\n        })\n    })\n    // compat checked earlier\n    // eslint-disable-next-line compat/compat\n    const entryTypes = PerformanceObserver.supportedEntryTypes.filter((x) =>\n        options.performanceEntryTypeToObserve.includes(x)\n    )\n    // initial records are gathered above, so we don't need to observe and buffer each type separately\n    observer.observe({ entryTypes })\n    return () => {\n        observer.disconnect()\n    }\n}\n\nfunction shouldRecordHeaders(type: 'request' | 'response', recordHeaders: NetworkRecordOptions['recordHeaders']) {\n    return !!recordHeaders && (isBoolean(recordHeaders) || recordHeaders[type])\n}\n\nexport function shouldRecordBody({\n    type,\n    recordBody,\n    headers,\n    url,\n}: {\n    type: 'request' | 'response'\n    headers: Headers\n    url: string | URL | RequestInfo\n    recordBody: NetworkRecordOptions['recordBody']\n}) {\n    function matchesContentType(contentTypes: string[]) {\n        const contentTypeHeader = Object.keys(headers).find((key) => key.toLowerCase() === 'content-type')\n        const contentType = contentTypeHeader && headers[contentTypeHeader]\n        return contentTypes.some((ct) => contentType?.includes(ct))\n    }\n\n    /**\n     * particularly in canvas applications we see many requests to blob URLs\n     * e.g. blob:https://video_url\n     * these blob/object URLs are local to the browser, we can never capture that body\n     * so we can just return false here\n     */\n    function isBlobURL(url: string | URL | RequestInfo) {\n        try {\n            if (typeof url === 'string') {\n                return url.startsWith('blob:')\n            }\n            if (url instanceof URL) {\n                return url.protocol === 'blob:'\n            }\n            if (url instanceof Request) {\n                return isBlobURL(url.url)\n            }\n            return false\n        } catch {\n            return false\n        }\n    }\n    if (!recordBody) return false\n    if (isBlobURL(url)) return false\n    if (isBoolean(recordBody)) return true\n    if (isArray(recordBody)) return matchesContentType(recordBody)\n    const recordBodyType = recordBody[type]\n    if (isBoolean(recordBodyType)) return recordBodyType\n    return matchesContentType(recordBodyType)\n}\n\nasync function getRequestPerformanceEntry(\n    win: IWindow,\n    initiatorType: string,\n    url: string,\n    start?: number,\n    end?: number,\n    attempt = 0\n): Promise<PerformanceResourceTiming | null> {\n    if (attempt > 10) {\n        logger.warn('Failed to get performance entry for request', { url, initiatorType })\n        return null\n    }\n    const urlPerformanceEntries = win.performance.getEntriesByName(url) as PerformanceResourceTiming[]\n    const performanceEntry = findLast(\n        urlPerformanceEntries,\n        (entry) =>\n            isResourceTiming(entry) &&\n            entry.initiatorType === initiatorType &&\n            (isUndefined(start) || entry.startTime >= start) &&\n            (isUndefined(end) || entry.startTime <= end)\n    )\n    if (!performanceEntry) {\n        await new Promise((resolve) => setTimeout(resolve, 50 * attempt))\n        return getRequestPerformanceEntry(win, initiatorType, url, start, end, attempt + 1)\n    }\n    return performanceEntry\n}\n\n/**\n * According to MDN https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest/response\n * xhr response is typed as any but can be an ArrayBuffer, a Blob, a Document, a JavaScript object,\n * or a string, depending on the value of XMLHttpRequest.responseType, that contains the response entity body.\n *\n * XHR request body is Document | XMLHttpRequestBodyInit | null | undefined\n */\nfunction _tryReadXHRBody({\n    body,\n    options,\n    url,\n}: {\n    body: Document | XMLHttpRequestBodyInit | any | null | undefined\n    options: NetworkRecordOptions\n    url: string | URL | RequestInfo\n}): string | null {\n    if (isNullish(body)) {\n        return null\n    }\n\n    const { hostname, isHostDenied } = isHostOnDenyList(url, options)\n    if (isHostDenied) {\n        return hostname + ' is in deny list'\n    }\n\n    if (isString(body)) {\n        return body\n    }\n\n    if (isDocument(body)) {\n        return body.textContent\n    }\n\n    if (isFormData(body)) {\n        return formDataToQuery(body)\n    }\n\n    if (isObject(body)) {\n        try {\n            return JSON.stringify(body)\n        } catch {\n            return '[SessionReplay] Failed to stringify response object'\n        }\n    }\n\n    return '[SessionReplay] Cannot read body of type ' + toString.call(body)\n}\n\nfunction initXhrObserver(cb: networkCallback, win: IWindow, options: Required<NetworkRecordOptions>): listenerHandler {\n    if (!options.initiatorTypes.includes('xmlhttprequest')) {\n        return () => {\n            //\n        }\n    }\n    const recordRequestHeaders = shouldRecordHeaders('request', options.recordHeaders)\n    const recordResponseHeaders = shouldRecordHeaders('response', options.recordHeaders)\n\n    const restorePatch = patch(\n        win.XMLHttpRequest.prototype,\n        'open',\n        // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n        // @ts-ignore\n        (originalOpen: typeof XMLHttpRequest.prototype.open) => {\n            return function (\n                method: string,\n                url: string | URL,\n                async = true,\n                username?: string | null,\n                password?: string | null\n            ) {\n                // because this function is returned in its actual context `this` _is_ an XMLHttpRequest\n                // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n                // @ts-ignore\n                const xhr = this as XMLHttpRequest\n\n                // check IE earlier than this, we only initialize if Request is present\n                // eslint-disable-next-line compat/compat\n                const req = new Request(url)\n                const networkRequest: Partial<CapturedNetworkRequest> = {}\n                let start: number | undefined\n                let end: number | undefined\n\n                const requestHeaders: Headers = {}\n                const originalSetRequestHeader = xhr.setRequestHeader.bind(xhr)\n                xhr.setRequestHeader = (header: string, value: string) => {\n                    requestHeaders[header] = value\n                    return originalSetRequestHeader(header, value)\n                }\n                if (recordRequestHeaders) {\n                    networkRequest.requestHeaders = requestHeaders\n                }\n\n                const originalSend = xhr.send.bind(xhr)\n                xhr.send = (body) => {\n                    if (\n                        shouldRecordBody({\n                            type: 'request',\n                            headers: requestHeaders,\n                            url,\n                            recordBody: options.recordBody,\n                        })\n                    ) {\n                        networkRequest.requestBody = _tryReadXHRBody({ body, options, url })\n                    }\n                    start = win.performance.now()\n                    return originalSend(body)\n                }\n\n                // This is very tricky code, and making it passive won't bring many performance benefits,\n                // so let's ignore the rule here.\n                // eslint-disable-next-line posthog-js/no-add-event-listener\n                xhr.addEventListener('readystatechange', () => {\n                    if (xhr.readyState !== xhr.DONE) {\n                        return\n                    }\n                    end = win.performance.now()\n                    const responseHeaders: Headers = {}\n                    const rawHeaders = xhr.getAllResponseHeaders()\n                    const headers = rawHeaders.trim().split(/[\\r\\n]+/)\n                    headers.forEach((line) => {\n                        const parts = line.split(': ')\n                        const header = parts.shift()\n                        const value = parts.join(': ')\n                        if (header) {\n                            responseHeaders[header] = value\n                        }\n                    })\n                    if (recordResponseHeaders) {\n                        networkRequest.responseHeaders = responseHeaders\n                    }\n                    if (\n                        shouldRecordBody({\n                            type: 'response',\n                            headers: responseHeaders,\n                            url,\n                            recordBody: options.recordBody,\n                        })\n                    ) {\n                        networkRequest.responseBody = _tryReadXHRBody({ body: xhr.response, options, url })\n                    }\n                    getRequestPerformanceEntry(win, 'xmlhttprequest', req.url, start, end)\n                        .then((entry) => {\n                            const requests = prepareRequest({\n                                entry,\n                                method: method,\n                                status: xhr?.status,\n                                networkRequest,\n                                start,\n                                end,\n                                url: url.toString(),\n                                initiatorType: 'xmlhttprequest',\n                            })\n                            cb({ requests })\n                        })\n                        .catch(() => {\n                            //\n                        })\n                })\n\n                originalOpen.call(xhr, method, url, async, username, password)\n            }\n        }\n    )\n    return () => {\n        restorePatch()\n    }\n}\n\n/**\n *  Check if this PerformanceEntry is either a PerformanceResourceTiming or a PerformanceNavigationTiming\n *  NB PerformanceNavigationTiming extends PerformanceResourceTiming\n *  Here we don't care which interface it implements as both expose `serverTimings`\n */\nconst exposesServerTiming = (event: PerformanceEntry | null): event is PerformanceResourceTiming =>\n    !isNull(event) && (event.entryType === 'navigation' || event.entryType === 'resource')\n\nfunction prepareRequest({\n    entry,\n    method,\n    status,\n    networkRequest,\n    isInitial,\n    start,\n    end,\n    url,\n    initiatorType,\n}: {\n    entry: PerformanceResourceTiming | null\n    method: string | undefined\n    status: number | undefined\n    networkRequest: Partial<CapturedNetworkRequest>\n    isInitial?: boolean\n    start?: number\n    end?: number\n    // if there is no performance observer entry, we still need to know the url\n    url?: string\n    // if there is no performance observer entry, we can provide the initiatorType\n    initiatorType?: string\n}): CapturedNetworkRequest[] {\n    start = entry ? entry.startTime : start\n    end = entry ? entry.responseEnd : end\n\n    // kudos to sentry javascript sdk for excellent background on why to use Date.now() here\n    // https://github.com/getsentry/sentry-javascript/blob/e856e40b6e71a73252e788cd42b5260f81c9c88e/packages/utils/src/time.ts#L70\n    // can't start observer if performance.now() is not available\n    // eslint-disable-next-line compat/compat\n    const timeOrigin = Math.floor(Date.now() - performance.now())\n    // clickhouse can't ingest timestamps that are floats\n    // (in this case representing fractions of a millisecond we don't care about anyway)\n    // use timeOrigin if we really can't gather a start time\n    const timestamp = Math.floor(timeOrigin + (start || 0))\n\n    const entryJSON = entry ? entry.toJSON() : { name: url }\n\n    const requests: CapturedNetworkRequest[] = [\n        {\n            ...entryJSON,\n            startTime: isUndefined(start) ? undefined : Math.round(start),\n            endTime: isUndefined(end) ? undefined : Math.round(end),\n            timeOrigin,\n            timestamp,\n            method: method,\n            initiatorType: initiatorType ? initiatorType : entry ? (entry.initiatorType as InitiatorType) : undefined,\n            status,\n            requestHeaders: networkRequest.requestHeaders,\n            requestBody: networkRequest.requestBody,\n            responseHeaders: networkRequest.responseHeaders,\n            responseBody: networkRequest.responseBody,\n            isInitial,\n        },\n    ]\n\n    if (exposesServerTiming(entry)) {\n        for (const timing of entry.serverTiming || []) {\n            requests.push({\n                timeOrigin,\n                timestamp,\n                startTime: Math.round(entry.startTime),\n                name: timing.name,\n                duration: timing.duration,\n                // the spec has a closed list of possible types\n                // https://developer.mozilla.org/en-US/docs/Web/API/PerformanceEntry/entryType\n                // but, we need to know this was a server timing so that we know to\n                // match it to the appropriate navigation or resource timing\n                // that matching will have to be on timestamp and $current_url\n                entryType: 'serverTiming',\n            })\n        }\n    }\n\n    return requests\n}\n\nconst contentTypePrefixDenyList = ['video/', 'audio/']\n\nfunction _checkForCannotReadResponseBody({\n    r,\n    options,\n    url,\n}: {\n    r: Response\n    options: NetworkRecordOptions\n    url: string | URL | RequestInfo\n}): string | null {\n    if (r.headers.get('Transfer-Encoding') === 'chunked') {\n        return 'Chunked Transfer-Encoding is not supported'\n    }\n\n    // `get` and `has` are case-insensitive\n    // but return the header value with the casing that was supplied\n    const contentType = r.headers.get('Content-Type')?.toLowerCase()\n    const contentTypeIsDenied = contentTypePrefixDenyList.some((prefix) => contentType?.startsWith(prefix))\n    if (contentType && contentTypeIsDenied) {\n        return `Content-Type ${contentType} is not supported`\n    }\n\n    const { hostname, isHostDenied } = isHostOnDenyList(url, options)\n    if (isHostDenied) {\n        return hostname + ' is in deny list'\n    }\n\n    return null\n}\n\nfunction _tryReadBody(r: Request | Response): Promise<string> {\n    // there are now already multiple places where we're using Promise...\n    // eslint-disable-next-line compat/compat\n    return new Promise((resolve, reject) => {\n        const timeout = setTimeout(() => resolve('[SessionReplay] Timeout while trying to read body'), 500)\n        try {\n            r.clone()\n                .text()\n                .then(\n                    (txt) => resolve(txt),\n                    (reason) => reject(reason)\n                )\n                .finally(() => clearTimeout(timeout))\n        } catch {\n            clearTimeout(timeout)\n            resolve('[SessionReplay] Failed to read body')\n        }\n    })\n}\n\nasync function _tryReadRequestBody({\n    r,\n    options,\n    url,\n}: {\n    r: Request\n    options: NetworkRecordOptions\n    url: string | URL | RequestInfo\n}): Promise<string> {\n    const { hostname, isHostDenied } = isHostOnDenyList(url, options)\n    if (isHostDenied) {\n        return Promise.resolve(hostname + ' is in deny list')\n    }\n\n    return _tryReadBody(r)\n}\n\nasync function _tryReadResponseBody({\n    r,\n    options,\n    url,\n}: {\n    r: Response\n    options: NetworkRecordOptions\n    url: string | URL | RequestInfo\n}): Promise<string> {\n    const cannotReadBodyReason: string | null = _checkForCannotReadResponseBody({ r, options, url })\n    if (!isNull(cannotReadBodyReason)) {\n        return Promise.resolve(cannotReadBodyReason)\n    }\n\n    return _tryReadBody(r)\n}\n\nfunction initFetchObserver(\n    cb: networkCallback,\n    win: IWindow,\n    options: Required<NetworkRecordOptions>\n): listenerHandler {\n    if (!options.initiatorTypes.includes('fetch')) {\n        return () => {\n            //\n        }\n    }\n    const recordRequestHeaders = shouldRecordHeaders('request', options.recordHeaders)\n    const recordResponseHeaders = shouldRecordHeaders('response', options.recordHeaders)\n\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n    // @ts-ignore\n    const restorePatch = patch(win, 'fetch', (originalFetch: typeof fetch) => {\n        return async function (url: URL | RequestInfo, init?: RequestInit | undefined) {\n            // check IE earlier than this, we only initialize if Request is present\n            // eslint-disable-next-line compat/compat\n            const req = new Request(url, init)\n            let res: Response | undefined\n            const networkRequest: Partial<CapturedNetworkRequest> = {}\n            let start: number | undefined\n            let end: number | undefined\n\n            try {\n                const requestHeaders: Headers = {}\n                req.headers.forEach((value, header) => {\n                    requestHeaders[header] = value\n                })\n                if (recordRequestHeaders) {\n                    networkRequest.requestHeaders = requestHeaders\n                }\n                if (\n                    shouldRecordBody({\n                        type: 'request',\n                        headers: requestHeaders,\n                        url,\n                        recordBody: options.recordBody,\n                    })\n                ) {\n                    networkRequest.requestBody = await _tryReadRequestBody({ r: req, options, url })\n                }\n\n                start = win.performance.now()\n                res = await originalFetch(req)\n                end = win.performance.now()\n\n                const responseHeaders: Headers = {}\n                res.headers.forEach((value, header) => {\n                    responseHeaders[header] = value\n                })\n                if (recordResponseHeaders) {\n                    networkRequest.responseHeaders = responseHeaders\n                }\n                if (\n                    shouldRecordBody({\n                        type: 'response',\n                        headers: responseHeaders,\n                        url,\n                        recordBody: options.recordBody,\n                    })\n                ) {\n                    networkRequest.responseBody = await _tryReadResponseBody({ r: res, options, url })\n                }\n\n                return res\n            } finally {\n                getRequestPerformanceEntry(win, 'fetch', req.url, start, end)\n                    .then((entry) => {\n                        const requests = prepareRequest({\n                            entry,\n                            method: req.method,\n                            status: res?.status,\n                            networkRequest,\n                            start,\n                            end,\n                            url: req.url,\n                            initiatorType: 'fetch',\n                        })\n                        cb({ requests })\n                    })\n                    .catch(() => {\n                        //\n                    })\n            }\n        }\n    })\n    return () => {\n        restorePatch()\n    }\n}\n\nlet initialisedHandler: listenerHandler | null = null\n\nfunction initNetworkObserver(\n    callback: networkCallback,\n    win: IWindow, // top window or in an iframe\n    options: NetworkRecordOptions\n): listenerHandler {\n    if (!('performance' in win)) {\n        return () => {\n            //\n        }\n    }\n\n    if (initialisedHandler) {\n        logger.warn('Network observer already initialised, doing nothing')\n        return () => {\n            // the first caller should already have this handler and will be responsible for teardown\n        }\n    }\n\n    const networkOptions = (\n        options ? Object.assign({}, defaultNetworkOptions, options) : defaultNetworkOptions\n    ) as Required<NetworkRecordOptions>\n\n    const cb: networkCallback = (data) => {\n        const requests: CapturedNetworkRequest[] = []\n        data.requests.forEach((request) => {\n            const maskedRequest = networkOptions.maskRequestFn(request)\n            if (maskedRequest) {\n                requests.push(maskedRequest)\n            }\n        })\n\n        if (requests.length > 0) {\n            callback({ ...data, requests })\n        }\n    }\n    const performanceObserver = initPerformanceObserver(cb, win, networkOptions)\n\n    // only wrap fetch and xhr if headers or body are being recorded\n    let xhrObserver: listenerHandler = () => {}\n    let fetchObserver: listenerHandler = () => {}\n    if (networkOptions.recordHeaders || networkOptions.recordBody) {\n        xhrObserver = initXhrObserver(cb, win, networkOptions)\n        fetchObserver = initFetchObserver(cb, win, networkOptions)\n    }\n\n    initialisedHandler = () => {\n        performanceObserver()\n        xhrObserver()\n        fetchObserver()\n    }\n    return initialisedHandler\n}\n\n// use the plugin name so that when this functionality is adopted into rrweb\n// we can remove this plugin and use the core functionality with the same data\nexport const NETWORK_PLUGIN_NAME = 'rrweb/network@1'\n\n// TODO how should this be typed?\n// eslint-disable-next-line @typescript-eslint/ban-ts-comment\n// @ts-ignore\nexport const getRecordNetworkPlugin: (options?: NetworkRecordOptions) => RecordPlugin = (options) => {\n    return {\n        name: NETWORK_PLUGIN_NAME,\n        observer: initNetworkObserver,\n        options: options,\n    }\n}\n\n// rrweb/networ@1 ends\n", "import { record as rrwebRecord } from '@rrweb/record'\nimport { getRecordConsolePlugin } from '@rrweb/rrweb-plugin-console-record'\nimport { getRecordNetworkPlugin } from '../extensions/replay/external/network-plugin'\nimport { assignableWindow } from '../utils/globals'\n\nassignableWindow.__PosthogExtensions__ = assignableWindow.__PosthogExtensions__ || {}\nassignableWindow.__PosthogExtensions__.rrwebPlugins = { getRecordConsolePlugin, getRecordNetworkPlugin }\nassignableWindow.__PosthogExtensions__.rrweb = { record: rrwebRecord, version: 'v2' }\n\n// we used to put all of these items directly on window, and now we put it on __PosthogExtensions__\n// but that means that old clients which lazily load this extension are looking in the wrong place\n// yuck,\n// so we also put them directly on the window\n// when 1.161.1 is the oldest version seen in production we can remove this\nassignableWindow.rrweb = { record: rrwebRecord, version: 'v2' }\nassignableWindow.rrwebConsoleRecord = { getRecordConsolePlugin }\nassignableWindow.getRecordNetworkPlugin = getRecordNetworkPlugin\n\nexport default rrwebRecord\n"], "names": ["_a", "__defProp", "Object", "defineProperty", "__publicField", "obj", "key", "value", "__defNormalProp", "enumerable", "configurable", "writable", "__defProp$1", "__publicField$1", "__defNormalProp$1", "NodeType$2", "NodeType2", "testableAccessors$1", "Node", "ShadowRoot", "Element", "MutationObserver", "testableMethods$1", "untaintedBasePrototype$1", "getUntaintedPrototype$1", "defaultObj", "_globalThis$Zone", "angularUnpatchedVersionSymbol", "globalThis", "Zone", "__symbol__", "angularZoneUnpatchedAlternative", "defaultPrototype", "prototype", "accessorNames", "isUntaintedAccessors", "Boolean", "every", "accessor", "_a2", "_b", "getOwnPropertyDescriptor", "get", "toString", "includes", "methodNames", "isUntaintedMethods", "method", "iframeEl", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "win", "contentWindow", "untaintedObject", "<PERSON><PERSON><PERSON><PERSON>", "_unused", "untaintedAccessorCache$1", "getUntaintedAccessor$1", "instance", "cache<PERSON>ey", "String", "call", "untaintedPrototype", "untaintedAccessor", "untaintedMethodCache$1", "getUntaintedMethod$1", "bind", "untainted<PERSON><PERSON><PERSON>", "index$1", "childNodes", "n2", "parentNode", "parentElement", "textContent", "contains", "other", "getRootNode", "host", "styleSheets", "shadowRoot", "querySelector", "selectors", "querySelectorAll", "mutationObserver", "constructor", "isElement", "nodeType", "ELEMENT_NODE", "isShadowRoot", "hostEl", "isNativeShadowDom", "shadowRoot2", "stringifyStylesheet", "s2", "rules2", "rules", "cssRules", "stringifiedRules", "Array", "from", "rule2", "stringifyRule", "href", "join", "cssText", "replace", "error", "sheetHref", "isCSSImportRule", "importStringified", "styleSheet", "split", "length", "statement", "JSON", "stringify", "layerName", "push", "supportsText", "media", "mediaText", "escapeImportStatement", "absolutifyURLs", "regex", "ruleStringified", "isCSSStyleRule", "selectorText", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "doc", "find", "s", "Mirror$1", "Map", "WeakMap", "getId", "id", "getMeta", "getNode", "this", "idNodeMap", "getIds", "keys", "nodeMetaMap", "removeNodeFromMap", "delete", "for<PERSON>ach", "childNode", "has", "hasNode", "node2", "add", "meta", "set", "oldNode", "reset", "maskInputValue", "_ref", "element", "maskInputOptions", "tagName", "type", "maskInputFn", "text", "actualType", "toLowerCase", "repeat", "str", "ORIGINAL_ATTRIBUTE_NAME", "getInputType", "hasAttribute", "extractFileExtension", "path", "baseURL", "_ref2", "url", "URL", "window", "location", "err", "match", "pathname", "URL_IN_CSS_REF", "URL_PROTOCOL_MATCH", "URL_WWW_MATCH", "DATA_URI", "origin", "quote1", "path1", "quote2", "path2", "path3", "filePath", "maybeQuote", "test", "indexOf", "slice", "stack", "parts", "part", "pop", "canvasService", "canvasCtx", "_id", "tagNameRegex", "RegExp", "IGNORED_NODE", "genId", "SRCSET_NOT_SPACES", "SRCSET_COMMAS_OR_SPACES", "cachedDocument", "absoluteToDoc", "attributeValue", "trim", "getHref", "isSVGElement", "el", "ownerSVGElement", "customHref", "a2", "startsWith", "setAttribute", "transformAttribute", "name", "pos", "collectCharacters", "regEx", "chars2", "exec", "substring", "output", "descriptorsStr", "inParens", "c2", "char<PERSON>t", "getAbsoluteSrcsetString", "ignoreAttribute", "_value", "classMatchesRegex", "checkAncestors", "eIndex", "classList", "className", "needMaskingText", "maskTextClass", "maskTextSelector", "closest", "matches", "e2", "serializeNode", "options", "mirror", "mirror2", "blockClass", "blockSelector", "needsMask", "inlineStylesheet", "maskTextFn", "dataURLOptions", "inlineImages", "recordCanvas", "keepIframeSrcFn", "newlyAddedElement", "rootId", "docId", "getRootId", "DOCUMENT_NODE", "compatMode", "Document", "DOCUMENT_TYPE_NODE", "DocumentType", "publicId", "systemId", "isCustomElement", "needBlock", "_isBlockedElement", "HTMLFormElement", "processedTagName", "getValidTagName$1", "attributes", "len", "i2", "attr", "n", "hrefFrom", "stylesheet", "rel", "_cssText", "sheet", "innerText", "checked", "selected", "open", "rr_open_mode", "_unused2", "ph_rr_could_not_detect_modal", "__context", "canvas", "ctx", "getContext", "x2", "width", "y", "height", "getImageData", "originalGetImageData", "Uint32Array", "Math", "min", "data", "buffer", "some", "pixel", "is2DCanvasBlank", "rr_dataURL", "toDataURL", "quality", "canvasDataURL", "blankCanvas", "image", "imageSrc", "currentSrc", "getAttribute", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "crossOrigin", "recordInlineImage", "removeEventListener", "naturalWidth", "naturalHeight", "drawImage", "complete", "addEventListener", "console", "warn", "removeAttribute", "mediaAttributes", "rr_mediaState", "paused", "rr_mediaCurrentTime", "currentTime", "rr_mediaPlaybackRate", "playbackRate", "rr_mediaMuted", "muted", "rr_mediaLoop", "loop", "rr_mediaVolume", "volume", "scrollLeft", "rr_scrollLeft", "scrollTop", "rr_scrollTop", "getBoundingClientRect", "class", "rr_width", "rr_height", "src", "contentDocument", "rr_src", "customElements", "isSVG", "isCustom", "serializeElementNode", "TEXT_NODE", "parent", "parentTagName", "isStyle", "isScript", "nextS<PERSON>ling", "previousSibling", "Text", "serializeTextNode", "CDATA_SECTION_NODE", "CDATA", "COMMENT_NODE", "Comment", "lowerIfExists", "maybeAttr", "serializeNodeWithId", "<PERSON><PERSON><PERSON><PERSON>", "slimDOMOptions", "onSerialize", "onIframeLoad", "iframeLoadTimeout", "onStylesheetLoad", "stylesheetLoadTimeout", "preserveWhiteSpace", "_serializedNode", "sn", "comment", "script", "as", "headFavi<PERSON>", "headMetaDescKeywords", "headMetaSocial", "property", "headMetaRobots", "headMetaHttpEquiv", "headMetaAuthorship", "headMetaVerification", "slimDOMExcluded", "serializedNode", "assign", "<PERSON><PERSON><PERSON><PERSON>", "shadowRootEl", "isShadowHost", "headWhitespace", "bypassOptions", "childN", "serializedChildNode", "is<PERSON><PERSON>ow", "listener", "readyState", "fired", "blankUrl", "setTimeout", "timer", "clearTimeout", "onceIframeLoaded", "iframeDoc", "serializedIframeNode", "link", "styleSheetLoadTimeout", "styleSheetLoaded", "onceStylesheetLoaded", "serializedLinkNode", "BaseRRNode$1", "BaseRRNode", "__publicField2", "childNodes2", "childIterator", "<PERSON><PERSON><PERSON><PERSON>", "ownerDocument", "_new<PERSON><PERSON>d", "Error", "insertBefore", "_refChild", "_node", "testableAccessors", "testableMethods", "untaintedBasePrototype", "getUntaintedPrototype", "_globalThis$Zone2", "angularZoneUnpatchedAlternative$1", "_unused3", "untaintedAccessorCache", "getUntaintedAccessor", "untainted<PERSON>ethod<PERSON><PERSON>", "getUntaintedMethod", "mutationObserverCtor", "index", "on", "fn", "target", "capture", "passive", "DEPARTED_MIRROR_ACCESS_WARNING", "_mirror", "map", "throttle", "func", "wait", "timeout", "previous", "_len", "arguments", "args", "_key", "now", "Date", "leading", "remaining", "context", "apply", "trailing", "hookSetter", "d", "isRevoked", "original", "patch", "source", "replacement", "wrapped", "defineProperties", "__rrweb_original__", "_unused4", "Proxy", "Reflect", "prop", "receiver", "nowTimestamp", "getWindowScroll", "_c", "_d", "left", "scrollingElement", "pageXOffset", "documentElement", "top", "pageYOffset", "getWindowHeight", "innerHeight", "clientHeight", "getWindowWidth", "innerWidth", "clientWidth", "closestElementOfNode", "isBlocked", "isIgnored", "headTitleMutations", "isAncestorRemoved", "legacy_isTouchEvent", "event", "changedTouches", "isSerializedIframe", "nodeName", "isSerializedStylesheet", "hasShadowRoot", "getTime", "StyleSheetMirror$1", "_this$styleIDMap$get", "styleIDMap", "newId", "idStyleMap", "getStyle", "generateId", "getShadowHost", "shadowHost", "DOCUMENT_FRAGMENT_NODE", "shadowHostInDom", "rootShadowHost", "getRootShadowHost", "inDom", "EventType", "EventType2", "IncrementalSource", "IncrementalSource2", "MouseInteractions", "MouseInteractions2", "PointerTypes", "PointerTypes2", "CanvasContext", "CanvasContext2", "MediaInteractions", "MediaInteractions2", "isNodeInLinkedList", "DoubleLinkedList", "position", "current", "head", "index2", "next", "addNode", "__ln", "tail", "removeNode", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "parentId", "MutationBuffer", "Set", "mutations", "processMutation", "emit", "frozen", "locked", "adds", "addedIds", "addList", "getNextId", "ns", "nextId", "pushAdd", "currentN", "iframeManager", "addIframe", "stylesheetManager", "trackLinkElement", "shadowDomManager", "addShadowRoot", "iframe", "childSn", "attachIframe", "observe<PERSON>ttach<PERSON><PERSON>ow", "attachLinkElement", "node", "mapRemoves", "shift", "movedSet", "isParentRemoved", "removes", "addedSet", "isAncestorInSet", "droppedSet", "candidate", "tailNode", "unhandledNode", "payload", "texts", "genTextAreaValueMutation", "filter", "attribute", "style", "diffAsStr", "styleDiff", "unchangedAsStr", "_unchangedStyles", "attributeMap", "movedMap", "mutationCb", "textarea", "item", "cn", "m", "oldValue", "attributeName", "unattachedDoc", "implementation", "createHTMLDocument", "old", "pname", "newValue", "getPropertyValue", "newPriority", "getPropertyPriority", "addedNodes", "gen<PERSON><PERSON>s", "removedNodes", "nodeId", "isSerialized", "deepDelete", "processedNodeManager", "in<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetId", "init", "freeze", "canvasManager", "unfreeze", "isFrozen", "lock", "unlock", "addsSet", "_ret", "_loop", "r2", "v", "_isParentRemoved", "size", "_isAncestorInSet", "callbackWrapper", "cb", "rrwebWrapped", "mutationBuffers", "getEventTarget", "<PERSON><PERSON><PERSON>", "_unused5", "initMutationObserver", "rootEl", "<PERSON><PERSON><PERSON>er", "observer", "processMutations", "observe", "attributeOldValue", "characterData", "characterDataOldValue", "childList", "subtree", "initMouseInteractionObserver", "_ref4", "mouseInteractionCb", "sampling", "mouseInteraction", "disableMap", "handlers", "currentPointerType", "Number", "isNaN", "endsWith", "eventKey", "eventName", "handler", "pointerType", "thisEventKey", "Mouse", "Touch", "Pen", "MouseDown", "MouseUp", "Click", "clientX", "clientY", "_extends", "x", "<PERSON><PERSON><PERSON><PERSON>", "PointerEvent", "TouchStart", "TouchEnd", "h", "initScrollObserver", "_ref5", "scrollCb", "evt", "defaultView", "scrollLeftTop", "scroll", "INPUT_TAGS", "lastInputValueMap", "getNestedCSSRulePositions", "childRule", "hasNestedCSSRule", "parentRule", "CSSGroupingRule", "CSSMediaRule", "CSSSupportsRule", "CSSConditionRule", "unshift", "parentStyleSheet", "recurse", "getIdAndStyleId", "styleMirror", "styleId", "ownerNode", "initAdoptedStyleSheetObserver", "_ref11", "host2", "hostId", "patch<PERSON>arget", "originalPropertyDescriptor", "_a3", "sheets", "result2", "adoptStyleSheets", "initObservers", "o2", "hooks", "currentWindow", "mousemoveCb", "viewportResizeCb", "inputCb", "mediaInteractionCb", "styleSheetRuleCb", "styleDeclarationCb", "canvasMutationCb", "fontCb", "selectionCb", "customElementCb", "mutation", "mousemove", "viewportResize", "input", "mediaInteaction", "styleSheetRule", "styleDeclaration", "canvasMutation", "font", "selection", "customElement", "mergeHooks", "recordDOM", "mousemoveHandler", "_ref3", "timeBaseline", "threshold", "callback<PERSON><PERSON><PERSON><PERSON>", "mousemoveCallback", "positions", "wrappedCb", "totalOffset", "p", "timeOffset", "updatePosition", "DragEvent", "Drag", "MouseEvent", "MouseMove", "TouchMove", "initMoveObserver", "mouseInteractionHandler", "<PERSON><PERSON><PERSON><PERSON>", "viewportResizeHandler", "_ref6", "_ref7", "lastH", "lastW", "initViewportResizeObserver", "inputHandler", "_ref8", "ignoreClass", "ignoreSelector", "userTriggeredOnInput", "<PERSON><PERSON><PERSON><PERSON>", "userTriggered", "isTrusted", "isChecked", "cbWithDedup", "text2", "v2", "lastInputValue", "propertyDescriptor", "HTMLInputElement", "hookProperties", "HTMLSelectElement", "HTMLTextAreaElement", "HTMLOptionElement", "initInputObserver", "mediaInteractionHandler", "_ref14", "Play", "Pause", "Seeked", "VolumeChange", "RateChange", "initMediaInteractionObserver", "styleSheetObserver", "adoptedStyleSheetObserver", "styleDeclarationObserver", "fontObserver", "_ref9", "_ref0", "CSSStyleSheet", "insertRule", "thisArg", "argumentsList", "rule", "addRule", "selector", "styleBlock", "replaceSync", "deleteRule", "removeRule", "supportedNestedCSSRuleTypes", "canMonkeyPatchNestedCSSRule", "unmodifiedFunctions", "entries", "_ref1", "typeKey", "_ref10", "initStyleSheetObserver", "_ref12", "_ref13", "ignoreCSSAttributes", "setProperty", "CSSStyleDeclaration", "priority", "removeProperty", "remove", "initStyleDeclarationObserver", "collectFonts", "_ref15", "fontMap", "originalFontFace", "FontFace", "family", "descriptors", "fontFace", "fontSource", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON>", "fonts", "initFontObserver", "selectionObserver", "param", "collapsed", "updateSelection", "getSelection", "isCollapsed", "ranges", "count", "rangeCount", "range", "getRangeAt", "startContainer", "startOffset", "endContainer", "endOffset", "start", "end", "initSelectionObserver", "customElementObserver", "_ref16", "define", "initCustomElementObserver", "pluginHandlers", "plugin3", "plugins", "callback", "b", "disconnect", "CrossOriginIframeMirror", "generateIdFn", "remoteId", "idToRemoteMap", "remoteToIdMap", "idToRemoteIdMap", "getIdToRemoteIdMap", "remoteIdToIdMap", "getRemoteIdToIdMap", "getRemoteId", "getRemoteIds", "ids", "iframeIdToRemoteIdMap", "iframeRemoteIdToIdMap", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "wrappedEmit", "recordCrossOriginIframes", "crossOriginIframeStyleMirror", "handleMessage", "iframes", "crossOriginIframeMap", "addLoadListener", "loadListener", "isAttachIframe", "adoptedStyleSheets", "message", "crossOriginMessageEvent", "transformedEvent", "transformCrossOriginEvent", "isCheckout", "FullSnapshot", "crossOriginIframeMirror", "replaceIdOnNode", "crossOriginIframeRootIdMap", "patchRootIdOnNode", "timestamp", "IncrementalSnapshot", "Mutation", "Meta", "Load", "DomContentLoaded", "Plugin", "Custom", "replaceIds", "ViewportResize", "MediaInteraction", "MouseInteraction", "<PERSON><PERSON>", "CanvasMutation", "Input", "StyleSheetRule", "StyleDeclaration", "replaceStyleIds", "Font", "Selection", "AdoptedStyleSheet", "styles", "iframeM<PERSON><PERSON>r", "isArray", "child", "ShadowDomManager", "WeakSet", "patchAttachShadow", "shadowDoms", "restoreHandlers", "iframeElement", "manager", "option", "sRoot", "chars", "lookup", "i$1", "charCodeAt", "canvasVarMap", "saveWebGLVar", "isInstanceOfWebGLObject", "list2", "ctor", "contextMap", "variableListFor$1", "serializeArg", "arg", "Float32Array", "Float64Array", "Int32Array", "Uint16Array", "Int16Array", "Int8Array", "Uint8ClampedArray", "rr_type", "values", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "base64", "encode", "arraybuffer", "bytes", "DataView", "byteOffset", "byteLength", "HTMLImageElement", "HTMLCanvasElement", "ImageData", "serializeArgs", "supportedWebGLConstructorNames", "initCanvasContextObserver", "setPreserveDrawingBufferToTrue", "contextType", "_len3", "_key3", "ctxName", "getNormalizedContextName", "contextAttributes", "preserveDrawingBuffer", "splice", "_unused7", "patchGLPrototype", "props", "getOwnPropertyNames", "_loop3", "_len4", "_key4", "recordArgs", "_unused8", "<PERSON><PERSON><PERSON><PERSON>", "setter", "takeFullSnapshot$1", "encodedJs", "blob", "Blob", "decodeBase64", "atob", "WorkerWrapper", "objURL", "webkitURL", "createObjectURL", "worker", "Worker", "revokeObjectURL", "CanvasManager", "latestId", "invokeId", "rafStamps", "pendingCanvasMutations", "initCanvasMutationObserver", "initCanvasFPSObserver", "clear", "resetObservers", "fps", "_this", "canvasContextReset", "snapshotInProgressMap", "onmessage", "commands", "rafId", "timeBetweenSnapshots", "lastSnapshotTime", "takeCanvasSnapshots", "get<PERSON>anvas", "<PERSON><PERSON><PERSON><PERSON>", "searchCanvas", "requestAnimationFrame", "root", "elem", "_ref17", "_asyncToGenerator", "getContextAttributes", "COLOR_BUFFER_BIT", "bitmap", "createImageBitmap", "resizeWidth", "resizeHeight", "postMessage", "_x", "cancelAnimationFrame", "startRAFTimestamping", "startPendingCanvasMutationFlusher", "canvas2DReset", "props2D", "CanvasRenderingContext2D", "_loop2", "_len2", "_key2", "_unused6", "initCanvas2DMutationObserver", "canvasWebGL1and2Reset", "WebGLRenderingContext", "WebGL", "WebGL2RenderingContext", "WebGL2", "initCanvasWebGLMutationObserver", "flushPendingCanvasMutations", "setLatestRAFTimestamp", "_values", "flushPendingCanvasMutationFor", "valuesWithType", "rest", "_objectWithoutPropertiesLoose", "_excluded", "StylesheetManager", "StyleSheetMirror", "adoptedStyleSheetCb", "linkEl", "trackedLinkElements", "trackStylesheetInLinkElement", "_this2", "adoptedStyleSheetData", "styleIds", "_loop4", "CSSRule", "_linkEl", "ProcessedNodeManager", "thisBuffer", "buffers", "nodeMap", "active", "destroy", "recording", "cleanFrame", "debug", "t2", "Mirror", "record", "checkoutEveryNms", "checkoutEveryNth", "maskAllInputs", "_maskInputOptions", "_slimDOMOptions", "packFn", "mousemoveWait", "recordAfter", "errorHandler2", "inEmittingFrame", "passEmitsToParent", "lastFullSnapshotEvent", "color", "date", "email", "month", "number", "search", "tel", "time", "week", "select", "password", "NodeList", "DOMTokenList", "polyfill$1", "incrementalSnapshotCount", "eventProcessor", "buf", "exceedCount", "exceedTime", "wrappedMutationEmit", "wrappedScrollEmit", "wrappedCanvasMutationEmit", "getMirror", "nodeMirror", "slimDOM", "snapshot", "initialOffset", "CustomElement", "plugin", "addCustomEvent", "tag", "freezePage", "takeFullSnapshot", "NotStarted", "Running", "Stopped", "__defProp2", "__defNormalProp2", "__publicField22", "utils", "__proto__", "getBaseDimension", "rootIframe", "frameElement", "relativeScale", "absoluteScale", "frameDimension", "frameBaseDimension", "getNestedRule", "getPositionsAndIndex", "nestedIndex", "iterateResolveTree", "tree", "children", "polyfill", "queueToResolveTrees", "queue", "queueNodeMap", "putIntoMap", "nodeInTree", "queueNodeTrees", "nextInTree", "idx", "parentInTree", "uniqueTextMutations", "idSet", "uniqueMutations", "Symbol", "toStringTag", "StackFrame", "fileName", "functionName", "lineNumber", "columnNumber", "FIREFOX_SAFARI_STACK_REGEXP", "CHROME_IE_STACK_REGEXP", "SAFARI_NATIVE_CODE_REGEXP", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parse", "stacktrace", "parseOpera", "parseV8OrIE", "parseFFOr<PERSON><PERSON><PERSON>", "extractLocation", "urlLike", "line", "sanitizedLine", "tokens", "locationParts", "functionNameRegex", "e", "parseOpera9", "parseOpera11", "parseOpera10", "lineRE", "lines", "i", "parseFloat", "pathToSelector", "outerHTML", "localName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sibling", "isObject", "isObjTooDeep", "limit", "stringifyOptions", "numOfKeysLimit", "depthOfLimit", "thisPos", "Infinity", "_obj", "shouldIgnore", "stringLengthLimit", "Event", "eventResult", "eventValue", "HTMLElement", "defaultLogOptions", "level", "lengthThreshold", "logger", "initLogObserver", "logOptions", "loggerType", "logCount", "inStack", "cancelHandlers", "trace", "stackFrame", "unhandledre<PERSON><PERSON><PERSON><PERSON>", "reason", "levelType", "_logger", "getRecordConsolePlugin", "undefined", "global", "nativeForEach", "navigator", "fetch", "XMLHttpRequest", "AbortController", "userAgent", "assignableWindow", "nativeIsArray", "Obj<PERSON><PERSON><PERSON>", "hasOwnProperty", "isFunction", "isUndefined", "isString", "isNull", "<PERSON><PERSON><PERSON><PERSON>", "isBoolean", "isDocument", "isFormData", "FormData", "_createLogger", "prefix", "_log", "consoleLog", "info", "critical", "_len5", "_key5", "uninitializedWarning", "methodName", "createLogger", "additionalPrefix", "breaker", "each", "iterator", "l", "eachArray", "pair", "formDataToQuery", "formdata", "arg_separator", "use_val", "use_key", "tph_arr", "val", "encodeURIComponent", "File", "__posthog_wrapped__", "isHostOnDenyList", "_options$payloadHostD", "hostname", "hostnameFromURL", "defaultNotDenied", "isHostDenied", "payloadHostDenyList", "deny", "defaultNetworkOptions", "initiatorTypes", "maskRequestFn", "recordHeaders", "recordBody", "recordInitialRequests", "recordPerformance", "performanceEntryTypeToObserve", "payloadSizeLimitBytes", "isNavigationTiming", "entry", "entryType", "isResourceTiming", "initPerformanceObserver", "initialPerformanceEntries", "performance", "getEntries", "initiatorType", "requests", "flatMap", "prepareRequest", "status", "networkRequest", "isInitial", "PerformanceObserver", "performanceEntries", "wrappedInitiatorFilter", "entryTypes", "supportedEntryTypes", "shouldRecordHeaders", "shouldRecordBody", "headers", "matchesContentType", "contentTypes", "contentTypeHeader", "contentType", "ct", "isBlobURL", "protocol", "Request", "recordBodyType", "getRequestPerformanceEntry", "_x2", "_x3", "_x4", "_x5", "_x6", "_getRequestPerformanceEntry", "attempt", "performanceEntry", "array", "predicate", "findLast", "getEntriesByName", "startTime", "Promise", "resolve", "_tryReadXHRBody", "exposesServerTiming", "responseEnd", "<PERSON><PERSON><PERSON><PERSON>", "floor", "toJSON", "round", "endTime", "requestHeaders", "requestBody", "responseHeaders", "responseBody", "timing", "serverTiming", "duration", "contentTypePrefixDenyList", "_tryReadBody", "r", "reject", "clone", "then", "txt", "finally", "_tryReadRequestBody2", "_tryReadResponseBody2", "cannotReadBodyReason", "_r$headers$get", "contentTypeIsDenied", "_checkForCannotReadResponseBody", "initFetchObserver", "recordRequestHeaders", "recordResponseHeaders", "restorePatch", "originalFetch", "res", "req", "header", "_x7", "_tryReadRequestBody", "_x8", "_tryReadResponseBody", "_res", "catch", "_x9", "_x0", "initialised<PERSON><PERSON><PERSON>", "initNetworkObserver", "networkOptions", "request", "maskedRequest", "performanceObserver", "xhrObserver", "fetchObserver", "originalOpen", "async", "username", "xhr", "originalSetRequestHeader", "setRequestHeader", "originalSend", "send", "DONE", "getAllResponseHeaders", "response", "initXhrObserver", "getRecordNetworkPlugin", "__PosthogExtensions__", "rrwebPlugins", "rrweb", "rrwebRecord", "version", "rrwebConsoleRecord"], "mappings": "ijBAGIA,aAHAC,EAAYC,OAAOC,eAEnBC,EAAgBA,CAACC,EAAKC,EAAKC,IADTC,EAACH,EAAKC,EAAKC,IAAUD,KAAOD,EAAMJ,EAAUI,EAAKC,EAAK,CAAEG,YAAY,EAAMC,cAAc,EAAMC,UAAU,EAAMJ,UAAWF,EAAIC,GAAOC,EACjHC,CAAgBH,EAAoB,iBAARC,EAAmBA,EAAM,GAAKA,EAAKC,GAEpGK,EAAcV,OAAOC,eAErBU,EAAkBA,CAACR,EAAKC,EAAKC,IADTO,EAACT,EAAKC,EAAKC,IAAUD,KAAOD,EAAMO,EAAYP,EAAKC,EAAK,CAAEG,YAAY,EAAMC,cAAc,EAAMC,UAAU,EAAMJ,UAAWF,EAAIC,GAAOC,EACnHO,CAAkBT,EAAoB,iBAARC,EAAmBA,EAAM,GAAKA,EAAKC,GACxGQ,EAA+BC,CAAAA,IACjCA,EAAUA,EAAoB,SAAI,GAAK,WACvCA,EAAUA,EAAwB,aAAI,GAAK,eAC3CA,EAAUA,EAAmB,QAAI,GAAK,UACtCA,EAAUA,EAAgB,KAAI,GAAK,OACnCA,EAAUA,EAAiB,MAAI,GAAK,QACpCA,EAAUA,EAAmB,QAAI,GAAK,UAC/BA,GAP0BA,CAQhCD,GAAc,CAAA,GACXE,EAAsB,CAC1BC,KAAM,CAAC,aAAc,aAAc,gBAAiB,eACpDC,WAAY,CAAC,OAAQ,eACrBC,QAAS,CAAC,aAAc,gBAAiB,oBACzCC,iBAAkB,IAEdC,EAAoB,CACxBJ,KAAM,CAAC,WAAY,eACnBC,WAAY,CAAC,gBACbC,QAAS,GACTC,iBAAkB,CAAC,gBA0Bb,IAAAE,EAAc,CAAA,EAAuI,SACtJC,EAAAlB,GAAA,GACFiB,EAAAjB,GACL,OAAAiB,EAAAjB,GACE,IAAImB,EArByB,SAC3BnB,GAAA,IAAAoB,EACAC,QAAcC,YAAAF,OACXA,EADWE,WAEVC,OAAS,MAFCH,EAEVI,gBAAS,EAFCJ,EAEVI,WAASxB,GACT,OAAOqB,GAC0JC,WACzKD,GAEKC,WACLD,QAGI,CAA2B,CAQzBI,CAA4CzB,IAAAsB,WAAAtB,GAC9C0B,EAAAP,EAAgCQ,UAChCC,EAAkB5B,KAAAW,EAAAA,EAAAX,QAAA,EACnB6B,EAAAC,QACDF,GACEA,EAAMG,OACNC,IACA,IAAMC,EAAMC,EACR,OAAMJ,QACuB,OAA3BI,EAA2B,OAA3BD,EAAArC,OAAkBuC,yBAAST,EAAAM,SAAA,EAAAC,EAAAG,UAAA,EAAAF,EAAAG,WAAAC,SACjC,iBAAA,KAIAC,EAAOvC,KAAAgB,EAAAA,EAAAhB,QAAA,EACRwC,EAAAV,QACHS,GAAAA,EAAAR,OAEAU,IACM,IAAAR,EACE,MAAqC,mBAAvBP,EAAce,KAASR,OAAAA,EAAAP,EAAAe,SAAAR,EAAAA,EAAAI,WAAAC,SAAA,iBAAA,KAGvC,GACNT,GAAAW,EAEE,OADAvB,EAA2BjB,GAAAmB,EAAAQ,UACrBR,EAAAQ,UACJ,IAED,IAAKe,EAAOC,SAAaC,cAAA,UAC1BD,SAAKE,KAAAC,YAAmBJ,GACxB,IAAAK,EAAAL,EAAAM,cACA,IAAOD,EAAA,OAAA5B,EAAuBQ,UAChC,IAAAsB,EAAAF,EAAA/C,GAAA2B,UAEA,OADAgB,SAAME,KAAAK,YAAAR,GACGO,EACDhC,EAA4BjB,GAAOiD,EADb9B,EAAKQ,SAEjC,CAAA,MAAAwB,GACE,OAAOhC,EAAAQ,SAAiC,CACtC,CAEJ,IAAAyB,EAA2B,CAAA,EAC3B,SAAMC,EAAkBrD,EAAAsD,EAAmBtB,GAC3C,IAAIC,EACJsB,EAAAvD,EAAuBwD,IAAAA,OAAQxB,GAC/B,GAAAoB,EAA4BG,GAC9B,OAAAH,EAAAG,GAAAE,KACAH,GAEA,IAAAI,EAAAxC,EAAAlB,GACA2D,EAGA,OAH0B1B,EAAArC,OAAAuC,yBACxBuB,EACF1B,SACSC,EAAAA,EAAgBG,IACvB,OAAAuB,GACFP,EAAAG,GAAAI,EACSA,EAAkBF,KAAAH,IAFKA,EAAQtB,EAGtC,CACF,IAAA4B,EAAA,CAAA,EACA,SAASC,EAAe7D,EAAOsD,EAAAb,GAC7B,IAAAc,EAAOvD,EAAAwD,IAAAA,OAAqBf,GAC9B,GAAAmB,EAAAL,GACA,OAASK,EAAkBL,GAAAO,KACzBR,GAEF,IACOS,EADa7C,EAAAlB,GACiByC,GACnC,MAA8B,mBAAvBsB,EAAyCT,EAAMb,IACxDmB,EAAAL,GAAAQ,EACSA,EAAcD,KAAIR,GACzB,CAsCO,IACLU,EAAU,CACdC,WAvCA,SAAAC,GACA,OAASb,SAAiBa,EAAA,aACxB,EAsCFC,WArCE,SAAOD,GACT,OAAAb,SAAAa,EAAA,aACA,EAoCAE,cAnCE,SAAOF,GACT,OAAAb,SAAAa,EAAA,gBACA,EAkCEG,YAjCA,SAAOH,GACT,OAAAb,SAAAa,EAAA,cACA,EAgCAI,SA/BE,SAAOJ,EAAAK,GACT,OAAAV,EAAA,OAAAK,EAAA,WAAAL,CAAAU,EACA,EA8BAC,YA9BgB,SACFN,GACZ,OAAAL,SAAYK,EAAA,cAAZL,EAAY,EA6BZY,KA5Be,SACFP,GACb,OAAAA,GAAU,SAAAA,EACVb,eAAaa,EAAA,QADH,IACG,EA0BXQ,YAzBI,SACOR,GACb,OAAAA,EAAAQ,WAAY,EAuBQC,WAtBL,SACGT,GAClB,OAAAA,GAAkB,eAAAA,EACpBb,YAAAa,EAAA,cADoB,IAEpB,EAmBMU,cAlBJ,SAAuBV,EAAGW,GAC5B,OAAAxB,EAAA,UAAAa,EAAA,gBAAAb,CAAAwB,EACA,EAiBMC,iBAhBJ,SAAMZ,EAAAW,GAAA,OAAAxB,EAAA,UAAAa,EAAA,mBAAAb,CAAAwB,EAAA,EAkBLE,iBAlBK,WAGsD,OAAA7D,sBAAA8D,WAE5D,GAeF,SAAAC,EAAAf,GACA,OAASA,EAAAgB,WAAAhB,EAAAiB,YACP,CACA,SAAIC,EAAclB,GAClB,IAAMmB,EAGRnB,GAAa,SAAMA,GAAA,SAAWA,GAAAF,EAAAS,KAAAP,IAC1B,KACD,OAAApC,QACDuD,GAAU,eAAcA,GAAArB,EAAAW,WAAAU,KAAAnB,EAC0B,CAElD,SAAIoB,EAAoBC,GACtB,MAAoC,wBAApC3F,OAAU+B,UAAWU,SAAMoB,KAAS8B,EAAA,CA4BlC,SAAAC,EAAoBC,GAAM,IAE5B,IAAIC,EAAMD,EAAAE,OAAiBF,EAAAG,SACzB,IAAAF,EACD,OAAA,KAEL,IAASG,EAAAC,MAAAC,KACLL,GACAM,GAAIC,EAAoBD,EAAKP,EAAMS,QACjCC,KAAA,IACD,OApC0BC,EAoC1BP,GAnCLvD,SAAA8D,6BAAAA,EAAA9D,SAAA,qCACA8D,EAASA,EAAAC,QACH,8BACF,2DAGCD,CA8BD,CAAA,MAAIE,GACF,OAAO,IAAyC,CAtCpD,IAA6BF,CAwC3B,CAAO,SACRH,EAAAD,EAAAO,GACH,GA6BI,SAAYP,GACb,MAAA,eAAAA,CAAA,CA9BHQ,CAAAR,GAAA,CACA,IAAAS,EACE,IACAA,EAEFjB,EAAyBQ,EAAOU,aAvCG,SAC7BV,GAAA,IACCI,QAAAA,GAAUJ,EAA4B,GACvCI,EAAOO,MAAAC,KAAAA,gBAAAR,EACT,IAAAS,oBAAOC,KAAAC,UAAmCf,EAAAE,MAAgB,KAWpB,MAVxB,KAAfF,EAAQgB,UACPH,EAAOI,KAAA,SACRjB,EAAAgB,WACHH,EAAAI,KAAAjB,SAAAA,EAAAgB,UAAA,KAEMhB,EAAAkB,cACFL,EAAII,KAAAjB,YAAAA,EAAAkB,aAAA,KAEFlB,EAAAmB,MAAAP,QAAAC,EAAAI,KAAAjB,EAAAmB,MAAAC,WAGAP,EAAAV,KAAA,KAA2B,GAAA,CAwB/BkB,CAAuBrB,EACzB,CAAA,MAAAM,GACAG,EAAwBT,EAAOI,OAC7B,CACF,OAAAJ,EAAAU,WAAAR,KACaoB,EAAAb,EAAAT,EAAAU,WAAAR,MAETO,CACA,CACD,IAWCc,EAXDC,EAAAxB,EAAAI,QAIC,OAWD,SAAAJ,GAGD,MAAA,iBAAsBA,CACpB,CAlBIyB,CAAIzB,IAAAA,EAAA0B,aAAApF,SAAA,OAURiF,EAAuC,uCATvCC,EAAIA,EAULnB,QAAAkB,EAAA,WARChB,EACOe,EAAME,EAAAjB,GAEPiB,CAC2B,CAYjC,SAAKG,EAAUC,EAAS1B,GACxB,OAAIJ,MAAGC,KAAA6B,EAAYlD,aAAAmD,MAAAC,GAAAA,EAAA5B,OAAAA,GACjB,CAAc,IAAA6B,EAAA,MACmC/C,WAAAA,GAElDzE,uBAAAyH,KACFzH,yBAAA0H,QACD,CACEC,KAAAA,CAAAhE,GACD,IAAAjC,EACD,IAAAiC,EAAe,OAAA,EACb,IAAAiE,EAAiC,OAArBlG,EAAAmG,KAAAA,QAAgBlE,SAAK,EAAAjC,EAAAkG,GAClC,OAAAA,QAAAA,GAAA,CACD,CACEE,OAAAA,CAAMF,GACN,OAAKG,KAAAC,UAAcnG,IAAM+F,IAAA,IACzB,CACDK,MAAAA,GACD,OAAQ1C,MAAIC,KAAIwC,KAAAA,UAAAE,OACd,CACAL,OAAAA,CAAIlE,GACF,OAAAoE,KAAMI,YAAYtG,IAAA8B,IAAY,IAC9B,CAGHyE,iBAAAA,CAAAzE,GACD,IAAQiE,EAAAG,KAAAJ,MAAAhE,GACNoE,KAAKC,UAAAK,OAA4BT,GACjCjE,EAAKD,YACNC,EAAAD,WAAA4E,SACHC,GAAAH,KAAAA,kBAAAG,IAGA,CACAC,GAAAA,CAAAZ,GACE,OAAAG,KAAAC,UAAAQ,IAAAZ,EACA,CACAa,OAAAA,CAAAC,GACA,OAAAX,KAAAI,YAAAK,IAAAE,EACA,CACAC,GAAAA,CAAAhF,EAAAiF,GACC,IAAAhB,EAAAgB,EAAAhB,GACDG,KAAIC,UAAOa,IAASjB,EAAAjE,GACpBoE,KAAMI,YAAaU,IAAAlF,EAAQiF,EAC3B,CACE9C,OAAAA,CAAI8B,EAAAjE,GACF,IAAAmF,EAAOf,KAAYD,QAAMF,GAC/B,GAAAkB,EAAW,CACL,IAAAF,EAAWb,KAAOI,YAAWtG,IAAAiH,GAC9BF,QAAAT,YAAAU,IAAAlF,EAAAiF,EACF,CACDb,KAAOC,UAAAa,IAAAjB,EAAAjE,EACT,CACAoF,KAAAA,GACEhB,KAAOC,cAAIP,IACbM,KAAAI,gBAAAT,OACA,GAKE,SAAAsB,EAAiBC,GAMU,IANLC,QACpBA,EAAAC,iBACEA,EAAMC,QACNA,EAAAC,KACAA,EAAA3J,MAAwBA,EAAA4J,YAAAA,GAEDL,EACnBM,EACA7J,GAAA,GAAA8J,EACAH,GAAAI,EAAAJ,GAQR,OARQF,EACSC,EAAWK,gBAAiBD,GAAAL,EAAAK,MACAD,EADAD,EAErCA,EAAKC,EAAAL,GAELQ,IAAAA,OAAYH,EAAMlD,SAGnBkD,CACT,CAaA,SAASE,EAAAE,GACP,OAAMA,EAAAF,aACN,CAAkE,IAAAG,EAAA,qBAyBpE,SAAAC,EAAAX,GACA,IAAMG,EAAAH,EAAiBG,KACvB,OAAMH,EAAAY,aAAqB,uBAAA,WAAAT,EAE3BI,EAAMJ,GACN,IACE,CAAuB,SACrBU,EAAAC,EAAAC,GAAA,IAAAC,EACCC,EACC,IACAA,EAAMC,IAAAA,IAAAJ,EAAAC,QAAAA,EAAuBI,OAAAC,SAAU3E,KACvC,CAAA,MAAK4E,GACH,OAAO,IAAA,CAET,IACEC,EAAOL,EAAAM,SAAOD,MADY,uBACsB,OACjDN,QADiDA,EACjD,MAAAM,OAAA,EAAAA,EAAAN,cAAAA,EAAAA,EAAA,IACD,CAWI,IAAAQ,EAAA,6CAAAC,EACwB,sBACxBC,EAAS,YAAAC,EACJ,wBACL,SAAA9D,EAAWlB,EAAIF,GAAA,OAChBE,GAAA,IAAAC,QAAA4E,GAEH,CAAAI,EAAOC,EAAOC,EAAAC,EAAaC,EAAMC,KAClC,IAlBiBhB,EAkBjBiB,EAAAJ,GAAAE,GAAAC,EACLE,EAAAN,GAAAE,GAAA,GACA,IAAAG,EACI,OAAMN,EAEJ,GAAAH,EAAeW,KAAAF,IAAAR,EAAAU,KAAAF,GACrB,MAAA,OAAiBC,EAAAD,EAAAC,EAAA,IAEjB,GAAAR,EAAAS,KAAAF,GACA,MAAA,OAASC,EAAkBD,EAASC,EAAA,IAEhC,GAAO,MAAPD,EAAO,GACR,MAAAC,OAAAA,KA9BmBlB,EA8BnBxE,GA5BO4F,QAAA,OAAgB,EAClBpB,EAAO/D,WAAOoF,MAAU,EAAG5F,GAAAA,KAAc,KAErCuE,EAAA/D,MAAa,KAAA,IAEVA,MAAA,KAAA,GAuBZgF,GAAAC,EAAA,IAEG,IAAAI,EAAa9F,EAAKS,MAAA,KACbsF,EAAAN,EAAAhF,MAAA,KAET,IAAO,IAAAuF,KADNF,EAAAG,MACMF,GACT,MAAAC,IAEI,OAAAA,EACEF,EAAAG,MAEGH,EAAA/E,KAAAiF,IAGN,MAAA,OAAAN,EAAAI,EAAA7F,UAAAyF,EAAA,GAAA,GAGC,CACA,IAgBEQ,EACAC,EAjBFC,EAAM,EACNC,MAAWC,OAAA,gBACTC,GAAgB,EAChB,SAAAC,IACA,OAAAJ,GAAO,CAaQ,IACrBK,EAAW,qBACLC,EAAqB,qBAsDhB,IACRC,MAAA5E,QACD,SAAI6E,EAASlF,EAASmF,GACpB,OAAAA,GAA+B,KAAVA,EAAUC,OAGnCC,EAAarF,EAASmF,GAFtBA,CAGI,CAA+B,SACnCG,EAAsBC,GAClB,OAAArL,QAA+B,QAAxBqL,EAAAxD,SAA6BwD,EAAAC,gBAAK,CAEzC,SAAAH,EAAOrF,EAAAyF,GACR,IAAAC,EAAAT,EAAsBzK,IAAAwF,GAKzB,GAJI0F,IACDA,EAAA1F,EAAAhF,cAAA,KACDiK,EAAOzD,IAAAxB,EAAA0F,IAETD,GAEA,GAAAA,EAAAE,WAAAF,UAAAA,EAAAE,WAAA,SACA,OAASF,OAFPA,EAAQ,GAKJ,OADFC,EAAAE,aAAW,OAAAH,GACTC,EAAIpH,IACF,CAAO,SACRuH,EAAA7F,EAAA+B,EAAA+D,EAAAzN,GAAA,OACPA,EAGY,QAAJyN,YAAeA,IAAiB,QAAA/D,GAAA,MAAA1J,EAAA,KAE/B,eAAAyN,GAAA,MAAAzN,EAAA,GADC6M,EAAOlF,EAAA3H,GAGZ,eAAAyN,GAAA/D,UAAAA,UAAAA,GAAA,OAAAA,EAEgB,WAAR+D,EAxFP,SAAyB9F,EAAGmF,GAC5B,GAAe,KAAfA,EAAeC,OACf,OAAAD,EAEE,IAAAY,EAAI,EACF,SAAAC,EAAmBC,GACnB,IAAAC,EACV/C,EAAA8C,EAAoBE,KAAAhB,EAAUiB,UAAAL,IACpB,OAAA5C,GACE+C,EAAA/C,EAAO,GACP4C,GAAAG,EAAOlH,OACPkH,GAEA,EAAW,CAGb,IAFC,IACXG,EAAe,GAEHL,EAAAhB,KAAWe,GACZZ,EAAAnG,SAFG,CAKN,IAAA8D,EAAAkD,EAAOjB,GAAA,GACR,MAAAjC,EAAAqB,OAAA,GACFrB,EAAAoC,EAAAlF,EAAA8C,EAAAsD,UAAA,EAAAtD,EAAA9D,OAAA,IACFqH,EAAAhH,KAAAyD,OACM,CACT,IAAAwD,EAAA,GACMxD,EAAAoC,EAAiClF,EAAA8C,GAEjC,IADN,IAASyD,GAAc,IAChB,CACH,IAAOC,EAAArB,EAAAsB,OAAAV,GACR,GAAA,KAAAS,EAAA,CACMH,EAAQhH,MAAKyD,EAAAwD,GAAclB,QACpC,KACA,IAASmB,EASU,MAAZC,IACHD,GAAa,OAVS,CACjB,GAAW,MAAXC,EAAW,CACpBT,GAAA,EACSM,EAAQhH,MAAKyD,EAAAwD,GAAYlB,QACvB,KACJ,CAAI,MAAAoB,IACFD,GAAkB,EAExB,CAKAD,GAAAE,EACET,GAAA,CACH,CACF,CACA,CACE,OAAKM,EAAO9H,KAAA,KACV,CAoCCmI,CAAA1G,EAAA3H,GACU,UAAJyN,EACRpG,EAAArH,EAAAgN,EAAArF,IACM,WAAA+B,GAAA,SAAA+D,EACTZ,EAAAlF,EAAA3H,GAEOA,EARC6M,EAAelF,EAAA3H,GAPjBA,CAgBJ,CACE,SAAKsO,EAAgB5E,EAAO+D,EAAAc,GAC5B,OAAyB,UAAzB7E,GAA4C,UAAnBA,IAAkC,aAAP+D,CAAqB,CAuB3E,SAAIe,EAAAxF,EAAA1B,EAAAmH,GACF,IAAAzF,EAAW,OAAA,EACT,GAAAA,EAAI/D,WAAA+D,EAAgB9D,aAClB,QAAAuJ,GACRD,EAAazK,EAAAG,WAAA8E,GAAA1B,EAAAmH,GAC4C,IAClD,IAAAC,EAAA1F,EAAA2F,UAAAhI,OAAA+H,KAAA,CACP,IAAAE,EAAW5F,EAAA2F,UAAAD,GACL,GAAApH,EAAIsE,KAAAgD,GACL,OAAA,CAEC,CACE,QAAAH,GACRD,EAAazK,EAAAG,WAAA8E,GAAA1B,EAAAmH,EACL,CAAyC,SAC1CI,EAAA7F,EAAA8F,EAAAC,EAAAN,GAAA,IACFvB,EACF,GAAAlI,EAAYgE,IAEb,GADCkE,EAAAlE,GACMjF,EAAAC,WAAAkJ,GAAAvG,OACT,OAAA,MAEQ,IAAe,OAAf5C,EAAMI,cAAS6E,GACrB,OAAK,EAEJkE,EAAAnJ,EAAAI,cAAA6E,EACD,CACA,IACA,GAAI,iBAAA8F,GACF,GAAAL,GACD,GAAQvB,EAAA8B,QAAO,IAAAF,GAAA,OAAA,OAEf,GAAA5B,EAAAyB,UAAAtK,SAAAyK,GAAA,OAAA,OAGG,GAAIN,EAAQtB,EAAA4B,EAAAL,GAAA,OAAA,EAEV,GAAAM,EACD,GAAAN,GACA,GAAAvB,EAAA8B,QAAAD,GAAiB,OAAA,OAElB,GAAA7B,EAAA+B,QAAaF,GAAK,OAAA,CAGxB,CAAA,MAAKG,GACD,CACD,OAAA,CACD,CAsDF,SACAC,EAAalL,EAAAmL,GACL,IAAAzH,IAAOA,EACY0H,OACjBC,EAAAC,WAAcA,EACxBC,cAAAA,EACOC,UACHA,EAAQC,iBACNA,EAAOjG,iBAAAA,EACY,CAAA,EAAAkG,WAAAA,EACX/F,YAAGA,EACTgG,eAAaA,EACA,CAAA,EAAAC,aAAAA,EACbC,aAAAA,EACRC,gBACIA,EAAQC,kBACNA,GAAO,GAAyBZ,EAE9Ba,EA8DN,SAActI,EAAA2H,GACZ,IAAAA,EAAOvG,QAAApB,GAAA,OACR,IAAAuI,EAAAZ,EAAArH,MAAAN,GACD,OAAiB,IAAZuI,OAAY,EAAYA,CAC3B,CAlEIC,CAAAxI,EAAA2H,GAAA,OACArL,EAAAgB,UAAA,KACAhB,EAAAmM,cAAA,MACA,eAAAnM,EAAAoM,WACA,CACA1G,KAAAnJ,EAAA8P,SACAtM,WAAA,GACAqM,WAAApM,EAAAoM,YAID,CACE1G,KAAGnJ,EAAA8P,SACNtM,WAAO,IAEL,KACAC,EAAAsM,mBAAA,MACA,CACD5G,KAAAnJ,EAAAgQ,aACH/C,KAAQxJ,EAAAwJ,KACNgD,SAAOxM,EAAAwM,SACLC,SAAMzM,EAAAyM,SACNT,UACA,KACRhM,EAAAiB,aACI,OA0FE,SAAkBjB,EAAAmL,GAkBP,IAjBX,IA2LNuB,GA3LMhJ,IACDA,EAAA4H,WACFA,EAAAC,cACDA,EAAIE,iBACFA,EAAgBjG,iBAChBA,EAAgB,CAAA,EAAAG,YAAAA,EACXgG,eACTA,EAAA,CAAA,EAAAC,aACIA,EAAIC,aACFA,EAAAC,gBACDA,EAAAC,kBACFA,GAAA,EAAAC,OACDA,GACEb,EACAwB,EA9QF,SAAwBpH,EAAU+F,EAAQC,GACxC,IACA,GAAwB,iBAAdD,GACR,GAAA/F,EAAOmF,UAAAtK,SAAAkL,GACR,OAAA,OAGH,IAAO,IAAAb,EAAAlF,EAAkBmF,UAAQhI,OAAW+H,KAAe,CAC7D,IAAAE,EAAApF,EAAAmF,UAAAD,GACA,GAASa,EAAA3D,KAAgBgD,GACnB,OAAA,CAEF,CAEE,GAAAY,EACD,OAAAhG,EAAAyF,QAAAO,EAED,CAAA,MAAON,GACX,CACI,OAAK,CAA2B,CA2PhB2B,CAAG5M,EAAAsL,EAAAC,GACf9F,EAjYJ,SAAOF,GACR,GAAAA,aAAAsH,gBACD,MAAM,OAEJ,IAAAC,EAAkBhH,EAAAP,EAAuBE,SACzC,OAAA4C,EAAWV,KAAAmF,GACT,MAEEA,CACJ,CAwXIC,CAAoB/M,GACtBgN,EAAW,CAAA,EACTC,EAAAjN,EAASgN,WAAAtK,OACTwK,EAAM,EAAAA,EAAAD,EAAeC,IAAA,CAAA,IACrBC,EAAAnN,EAAAgN,WAAAE,GACA7C,EAAA5E,EAAA0H,EAAA3D,KAAA2D,EAAApR,SAAAiR,EACAG,EAAA3D,MAAAD,EACA7F,EACD+B,EACFK,EAAUqH,EAAS3D,MAClB2D,EAAApR,OAGJ,CACE,GAAoB,SAApB0J,GAAoBgG,EAAiB,CACnC,IAAAzJ,EAnCA,SAAUoL,GAA8B,OACzC,MAAAA,OAAA,EAAAA,EAAApL,IACD,CAiCaqL,CAAArN,GACjB,GAAAgC,EAAW,CACL,IAAAsL,EAAO7J,EAAWC,EAAA1B,GACnB,IAAAsL,GAAAtL,EAAA5D,SAAA,QAIFkP,EAAA7J,EAAAC,EAHAgD,OAAAC,SAAAQ,OAEsC,IADXnF,EAAGG,QAAMuE,OAAAC,SAAA3E,KAAA,KAInC,IAAIE,EAAG,KACDoL,IACFpL,EAAAZ,EAA2BgM,IAEzBpL,WACV8K,EAAAO,WACOP,EAAAhL,KACPgL,EAAiBQ,SAAAtL,EACc,CACR,CACA,GACvB,UAAAuD,GAAAzF,EAAAyN,SACMzN,EAAA0N,WAAM5N,EAAcK,YAAIH,IAAc,IAAA8I,OAAQpG,OAAA,CAC9C,IAAAR,EAAYZ,EACZtB,EAAAyN,OAEEvL,IAAe8K,EACfQ,SAAetL,EAEjB,CACE,GAAA,UAAAuD,gBAAWA,GAAa,WAAAA,EAAA,CAAA,IACzB1J,EAAAiE,EAAAjE,MACF4R,EAAA3N,EAAA2N,QACFX,UAAAA,EAAAtH,MAAA,aAAAsH,EAAAtH,MAAAsH,WAAAA,EAAAtH,iBAAAsH,EAAAtH,MAAA3J,EACGiR,EAAAjR,MAAYsJ,EAAS,CACnBE,QAACvF,EACH0F,KAAAQ,EAAoBlG,GACpByF,UACD1J,QACDyJ,mBACAG,gBAEMgI,IACJX,EAAMW,QAAAA,EAEJ,CACc,WAAdlI,IACAzF,EAAA4N,WAAUpI,EAAqB,OAC/BwH,EAAAY,UAAwB,SAEtBZ,EAAAY,UAGF,GAAU,WAAVnI,GAAUzF,EAAA6N,KACR,IACAb,EAAIc,aAAkB9N,EAAAgL,QAAuB,gBAAA,QAAA,WAC3C,CAAA,MAAA+C,GAIFf,EAAAc,aAAQ,QAAAd,EACNgB,8BAAiC,CAAe,CAEnD,cACFvI,GAAAoG,EACD,GAAU,OAAV7L,EAAIiO,WAjjB0D,SAEhDC,GAAA,IACdC,EAAAD,EAAAE,WAAA,MACN,IAAAD,EAAA,OAAA,EAEE,IADF,IACME,EAAA,EAAAA,EAAAH,EAAAI,MAAAD,GADG,GAEP,IAAI,IAAAE,EAAA,EAAAA,EAAAL,EAAAM,OAAAD,GAFG,GAEH,CACF,IAAME,EAAcN,EAAAM,aACbC,EAAKzI,KAAAwI,EAAAA,EAAAxI,GAAAwI,EAWhB,OAVWE,YAEHD,EAAQnP,KACR4O,EACEE,EACVE,EACSK,KAAAC,IAXA,GAWmBX,EAAAI,MAAAD,GACtBO,KAASC,IAZN,GAYMX,EAAAM,OAAAD,IACLO,KAAAC,QAEDC,MAAAC,GAAA,IAAAA,IAAA,OAAA,CACL,CAEF,OAAS,CACT,EA0hBMC,CAAmBlP,KACpBgN,EAAAmC,WAAAnP,EAAAoP,UACPzD,EAAAjG,KACQiG,EAAM0D,eAGR,KAAA,cAAuBrP,GAAA,CACzB,IAAMsP,EAAkBtP,EAAAoP,UACxBzD,EAAgBjG,KAChBiG,EAAgB0D,SAEhBE,EAAgB7L,EAAAhF,cAAmB,UACnC6Q,EAAAjB,MAAgBtO,EAAAsO,MAChBiB,EAAAf,OAAgBxO,EAAAwO,OAKfc,IAJFC,EAAAH,UACIzD,EAAAjG,KACCiG,EAAe0D,WAGfrC,EAAGmC,WAAWG,EAEjB,CAEH,GAAe,QAAX7J,GAAWmG,EAAA,CACb1D,IACAA,EAAaxE,EAAAhF,cAAA,UACXyJ,EAAOD,EAAWkG,WAAA,OACA,IAClBoB,EAAWxP,EACjByP,EAAAD,EAAAE,YAAAF,EAAAG,aAAA,QAAA,gBACGC,EAAAJ,EAAAK,YACGC,EAAwBA,KAC1BN,EAAQO,oBAAiB,OAAAD,GACvB,IACD5H,EAAAoG,MAAAkB,EAAAQ,aACD9H,EAAkBsG,OAAAgB,EAAAS,cACnB9H,EAAA+H,UAAAV,EAAA,EAAA,GACGxC,EAAAmC,WAAAjH,EAAAkH,UACAzD,EAAAjG,KACEiG,EAAe0D,QAEpB,CAAA,MAAAzI,GACD,GAAO,cAAA4I,EAAAK,YAKL,OAJML,EAAAK,YAAW,iBACjBL,EAAAW,UACAL,IADAN,EAAAQ,aACAF,IACAN,EAAcY,wBAAAN,IAGdO,QAAAC,KACUb,yBAAAA,cAAA7I,EAGd,CAC8B,cAAxB4I,EAAAK,cACFD,EAAO5C,EAAA6C,YAAAD,EAAAJ,EAAAe,gBAAA,eACX,EAEGf,EAAAW,UAAAL,IAAAN,EAAAQ,aAAAF,IACHN,EAAAY,wBAAAN,EACA,CACE,GAAI,UAAArK,GAAsC,UAAvBA,EAAuB,CACxC,IAAA+K,EAAOxD,EACRwD,EAAaC,cAAoBzQ,EAAA0Q,OAAS,SAAA,SACzCF,EAAIG,oBAAe3Q,EAAA4Q,YAAAJ,EACHK,qBAAA7Q,EAAA8Q,aAChBN,EAAeO,cAAc/Q,EAAAgR,MAC7BR,EAAeS,aAAajR,EAAAkR,KAC1BV,EAAOW,eAAAnR,EAAAoR,MAAA,CAEPrF,IACD/L,EAAIqR,aACHrE,EAAOsE,cAAAtR,EAAAqR,YAEPrR,EAAIuR,YACFvE,EAAOwE,aAAAxR,EAAAuR,YAGP,GAAA5E,EAAO,CAAA,IACf2B,MAAAA,EAAiBE,OAAAA,GAAAxO,EAAeyR,wBACxBzE,EAAO,CACf0E,MAAA1E,EAAiB0E,MACTC,SAAOrD,EAAA,KACfsD,UAAiBpD,EAAA,KACF,CAEA,WAAP/I,GAAOqG,EAAAkB,EAAA6E,OACR7R,EAAA8R,kBACF9E,EAAA+E,OAAA/E,EAAA6E,YAEI7E,EAAA6E,KAGP,IACEG,eAAA9T,IAAAuH,KAAAiH,GAAA,EAAA,CACA,MAAAzB,GAAQ,CACR,MACA,CACAvF,KAAAnJ,EAAAK,QACA6I,UACAuH,aACAjN,WAAA,GACAkS,MAAAjJ,EAAqBhJ,SAAA,EACrB2M,YACAX,SACAkG,SAAAxF,EACmB,CApSXyF,CAAAnS,EAAA,CACN0D,MACE4H,aACAC,gBACAE,mBACRjG,mBACIG,cACEgG,iBACHC,eACHC,eACAC,kBACOC,oBACCC,WAER,KAAAhM,EAAAoS,UACA,OA0B+F,SAC5FpS,EAAAmL,GACD,IAAApN,GACEyN,UAAMA,EAAAE,WAAWA,EAAAM,OAAAA,GAAAb,EACjBkH,EAAavS,EAAQG,WAAAD,GACrBsS,EAAAD,GAAAA,EAAA5M,QACAG,EAAA9F,EAAAK,YAAAH,GACJuS,EAAA,UAAAD,QAAA,EACAE,EAAA,WAAAF,QAAA,EACA,GAAAC,GAAS3M,EAAA,CACP,IACE5F,EAAAyS,aAAAzS,EAAA0S,kBACA3U,OAAAA,EAAAsU,EAAA5E,YAAA1P,EAAAA,EAAA2D,YACAkE,EAAAtE,EAAA+Q,EAAA5E,OAEA,CAAA,MAAA7G,GACAyJ,QAAAC,KACmB1J,wDAAAA,EACnB5G,EAEA,CACA4F,EAAAxC,EAAoBwC,EAAAmD,EAAAoC,EAAAzH,KAAA,CAElB8O,IACJ5M,EAAkB,uBAEd2M,IAAaC,GAAA5M,GAAA4F,IACjB5F,EAAM8F,EAASA,EAAW9F,EAAA9F,EAAAI,cAAAF,IAAA4F,EAAAzD,QAAA,QAAA,MAExB,MAAA,CACAuD,KAAKnJ,EAAAoW,KACHxS,YAAWyF,GAAK,GAAQ2M,UACtBvG,SAEqB,CA5DpB4G,CAAsB5S,EAAA,CACzB0D,MACE8H,YACAE,aACAM,WAEN,KAAMhM,EAAA6S,mBACN,MAAM,CACFnN,KAAAnJ,EAAiBuW,MACf3S,YAAA,GACF6L,UAEE,KAAAhM,EAAA+S,aACD,MAAA,CACFrN,KAAQnJ,EAAKyW,QACZ7S,YAAQL,EAAAK,YAAAH,IAAA,GACNgM,UACA,QAEH,OAAA,EAC+C,CAkQjC,SACfiH,EAAeC,GAAA,OACfA,QACA,GAEAA,EAAApN,aACwB,CAiCjB,SACRqN,GAAAnT,EAAAmL,GACD,IAAIzH,IACJA,EACE0H,OAAKC,EAAQC,WACjBA,EAAaC,cACTA,EAAKV,cACTA,EAASC,iBACLA,EAAUsI,UACXA,GAAA,EAAA3H,iBACDA,GAAuB,EAAAjG,iBACvBA,EAAgB,CAAA,EAAAkG,WAChBA,EAAW/F,YACTA,EAAO0N,eACRA,EAAA1H,eACDA,EAAiB,CAAA,EAAAC,aACfA,GAAc,EAAAC,aACfA,GAAA,EAAAyH,YACDA,EAAIC,aACJA,EAAIC,kBACFA,EAAc,IAAAC,iBACdA,EAAOC,sBACPA,EAAqB,IAAA5H,gBACrBA,EAAIA,MAAgB,GAAAC,kBAClBA,GAAe,GAClBZ,GACDK,UAAKA,GAAeL,GAClBwI,mBAAIA,GAAe,GAAAxI,EACjBK,IAEFA,EAAMZ,EACJ5K,EACA6K,EACAC,OAJD,IAAAU,IAOC,IAqBAvH,EApBA2P,EAAA1I,EAAAlL,EAAA,CAAA0D,MACA0H,OACAC,EAAAC,aACAC,gBACAC,YACAC,mBACAjG,mBACAkG,aACA/F,cACAgG,iBACAC,eACAC,eACAC,kBACAC,sBAEA,IACA6H,EAEF,OADJvD,QAAAC,KAAAtQ,EAAA,kBACQ,KAIAiE,EADAoH,EAAMvG,QAAA9E,GACNqL,EAAIrH,MAAAhE,IA3FgB,SACJ6T,EAAAR,GACrB,GAAGA,EAAAS,SAAAD,EAAAnO,OAAAnJ,EAAAyW,QACJ,OAAM,EACA,GAAAa,EAAAnO,OAAAnJ,EAA2BK,QAAG,CACpC,GAAKyW,EAAWU,SACS,WAAvBF,EAAApO,SACY,SAAZoO,EAAApO,UAAY,YAAAoO,EAAA7G,WAAAO,KAAA,kBAAAsG,EAAA7G,WAAAO,MAAA,WAAAsG,EAAA7G,WAAAgH,IACVH,SADUA,EACVpO,SAAAoO,aAAAA,EAAA7G,WAAAO,KAAAnH,iBAAAyN,EAAA7G,WAAAhL,MAAA,OAAAoE,EAAAyN,EAAA7G,WAAAhL,OACA,OAAA,EACA,GAAAqR,EAAAY,cAAAJ,SAAAA,EAAApO,SAAAoO,kBAAAA,EAAA7G,WAAAO,KAAA0F,SAAAY,EAAApO,UAAAwN,EAAAY,EAAA7G,WAAAxD,MAAA3C,MACA,sCACNoM,qBAAAA,EAAAY,EAAA7G,WAAAxD,OAAAyJ,SAAAA,EAAAY,EAAA7G,WAAAO,MAAA0F,qBAAAA,EAAAY,EAAA7G,WAAAO,MAAA,kBAAA0F,EAAAY,EAAA7G,WAAAO,OACG,OAAA,EACK,GAAkB,SAAlBsG,EAAApO,QAAkB,CACtB,GAAA4N,EAAAa,sBAAAjB,EAAAY,EAAA7G,WAAAxD,MAAA3C,MAAA,0BACA,OAAQ,EACR,GAAAwM,EAAAc,iBAAAlB,EAAAY,EAAA7G,WAAAoH,UAAAvN,MAAA,sBACAoM,EAAAY,EAAA7G,WAAAxD,MAAA3C,MAAA,mBAAA,cAAAoM,EAAAY,EAAA7G,WAAAxD,OACA,OAAA,EACA6J,GAAAA,EAAAgB,iBAAApB,WAAAA,EAAAY,EAAA7G,WAAAxD,OAAA,cAAAyJ,EAAAY,EAAA7G,WAAAxD,OAAA,YAAAyJ,EAAAY,EAAA7G,WAAAxD,OACA,OAAA,EACA,GAAA6J,EAAAiB,wBAAA,IAAAT,EAAA7G,WAAA,cACA,OAAA,EACA,GAAAqG,EAAAkB,qBAAA,WAAAtB,EAAAY,EAAA7G,WAAAxD,OAAA,cAAAyJ,EAAAY,EAAA7G,WAAAxD,OAAA,cAAAyJ,EAAAY,EAAA7G,WAAAxD,OAAAyJ,cAAAA,EAAAY,EAAA7G,WAAAxD,OAAAyJ,WAAAA,EAAAY,EAAA7G,WAAAxD,OAAAyJ,EAAAY,EAAA7G,WAAAoH,UAAAvN,MAAAoM,cAAAA,EAAAY,EAAA7G,WAAAoH,UAAAvN,MAAA,cACA,OAAA,EACAwM,GAAAA,EAAAmB,uBAAAvB,6BAAAA,EAAAY,EAAA7G,WAAAxD,OAAAyJ,wBAAAA,EAAAY,EAAA7G,WAAAxD,OAAAyJ,eAAAA,EAAAY,EAAA7G,WAAAxD,OAAA,oBAAAyJ,EAAAY,EAAA7G,WAAAxD,OAAA,cAAAyJ,EAAAY,EAAA7G,WAAAxD,OAAA,iBAAAyJ,EAAAY,EAAA7G,WAAAxD,OAAA,+BAAAyJ,EAAAY,EAAA7G,WAAAxD,OACA,OAAA,CAED,CACD,CACE,OAAQ,CACR,CA4DMiL,CAAeb,EAAgBP,KAAmBM,GAAAC,EAAAlO,OAAAnJ,EAAAoW,MAAAiB,EAAArB,SAAAqB,EAAAzT,YAAAgC,QAAA,cAAA,IAAAO,QAGvD8F,IAFID,EAIL,IAAImM,EAAYhZ,OAAMiZ,OAAAf,EAAuB,CAAA3P,OAEzC,GADFoH,EAAArG,IAAAhF,EAAW0U,GACTzQ,IAAAsE,EACA,OAAI,KAEF+K,GAAkDA,EACnDtT,GACF,IACF4U,GAAAxB,EACF,GAAAsB,EAAAhP,OAAAnJ,EAAAK,QAAA,CACDgY,EAAeA,IAAQF,EAAa/H,iBAChC+H,EAAU/H,UACZ,IAAAkI,EAAe/U,EAAWW,WAAAT,GAC3B6U,GAAAzT,EAAAyT,KACGH,EAAeI,cAAS,EAC1B,CAAA,IACEJ,EAAAhP,OAAAnJ,EAAA8P,UAAAqI,EAAAhP,OAAAnJ,EAAAK,UAAAgY,EAAA,CACAvB,EAAM0B,gBAAAL,EAAAhP,OAAAnJ,EAAAK,SAAA,SAAA8X,EAAAjP,UACJkO,GAAkB,GAEhB,IAAAqB,EAAM,CAAsDtR,MACrD0H,OACLC,EAAQC,aACRC,gBACAC,YACAX,gBACAC,mBACAsI,YACW3H,mBACXjG,mBACAkG,aACA/F,cACA0N,iBACA1H,iBACAC,eACAC,eACA8H,qBACAL,cACAC,eACAC,oBACAC,mBACAC,wBACA5H,mBAGF,GAAA4I,EAAIhP,OAAAnJ,EAAsBK,SAAA8X,aAAAA,EAAAjP,cACxB,IADwBiP,EAAA1H,WAAAjR,YACxB,IACEkZ,IAAAA,KAAArT,MAAAC,KAAA/B,EAAAC,WAAAC,IAAA,CAAA,IACAkV,EAAA/B,GAAA8B,EAAAD,GACdE,GACWR,EAAA3U,WAAAgD,KAAAmS,EAEJ,CAEP,IAAAL,EAAA,KACG,GAAA9T,EAAAf,KAAA6U,EAAA/U,EAAAW,WAAAT,IACG,IAAAiV,IAAAA,KAAerT,MAASC,KAAA/B,EAAWC,WAAW8U,IAAe,CAC/D,IAAAK,EAAA/B,GAAA8B,EAAAD,GACEE,IACA9T,EAAMyT,KAAAK,EAAAC,UAAA,GACJT,EAAI3U,WAAkBgD,KAAAmS,GAC+B,CAEzC,CACR,IAAA7C,EACAvS,EAAAG,WAAAD,GAsFU,OAtFVqS,GACAnR,EAAAmR,IAAAjR,EAAAiR,KAAAqC,EACAS,UAAA,GACAT,EACAhP,OAAWnJ,EAAAK,SAAA,WAAA8X,EAAAjP,SAljBrB,SAAiBjH,EAAS4W,EAAY5B,GACpC,IAAA3U,EAAAL,EAAWM,cACX,GAAAD,EAAA,CAGJ,IACAwW,EADAC,GAAA,EAEE,IACAD,EAAIxW,EAAAJ,SAAA4W,UACJ,CAAA,MAAIjT,GACF,MACD,CACC,GAAA,aAAAiT,EAAA,CAcJ,IAAAE,EAAA,cACA,GAAA1W,EAAS8H,SAAA3E,OAAkBuT,GAAS/W,EAAAqT,MAAA0D,GAAA,KAAA/W,EAAAqT,IAEhC,OADF2D,WAAMJ,EAAA,GACJ5W,EAAA4R,iBAAA,OAAAgF,GACQ5W,EACR4R,iBAAA,OAAAgF,EANJ,KAbI,CACD,IAAAK,EAAAD,YAAA,KACGF,IACEF,IACAE,GAAQ,EACV,GACA9B,GACDhV,EAAA4R,iBAAA,QAAA,KACAsF,aAAAD,GACHH,GAAK,EACHF,GAAA,GAGJ,CArBE,CA2BE,CAmhBmBO,CACX3V,GACA,KACA,IACA4V,EAAA5V,EAAA8R,gBAAA,GACA8D,GAAArC,EAAA,CAAA,IACAsC,EAAA1C,GAAAyC,EAAA,CACAlS,IAAAkS,EACAxK,OAAAC,EACAC,aACAC,gBACAC,YACAX,gBACAC,mBACAsI,WAAA,EACA3H,mBACDjG,mBACDkG,aACE/F,cAAA0N,iBACE1H,iBAEdC,eACWC,eACF8H,qBACFL,cACDC,eACNC,oBACGC,mBACMC,wBACT5H,oBAEQ+J,GACItC,EACRvT,EACA6V,EAGA,IAEArC,GAGAkB,EAAAhP,OAAAnJ,EAAAK,SAAA,SAAA8X,EAAAjP,SAAA,iBAAAiP,EAAA1H,WAAAO,qBAAAmH,EAAA1H,WAAAO,iBAAAmH,EAAA1H,WAAAO,sBAAAmH,EAAA1H,WAAAhL,MAAA,QAAAoE,EAAAsO,EAAA1H,WAAAhL,QA5jBA,SACA8T,EAAAV,EAAAW,GAAA,IAEAC,EADAV,GAAA,EACqB,IAErBU,EAAAF,EAAArI,KAAA,CACA,MAAArL,GACA,MAAA,CACA,IACA4T,EADA,CACA,IACAP,EAAAD,YAAoB,KAClBF,IACJF,IACAE,GAAW,EACT,GACES,GACED,EAAA1F,iBAAO,QAAA,KAAAsF,aACCD,GAAWH,GACjB,EAAcF,GACd,GAVN,CAUqB,CA0iBrBa,CACAjW,GACA,KACA,GAAAyT,EAAA,CACA,IAAAyC,EAAA/C,GAAAnT,EAAA,CACA0D,MACA0H,OAAAC,EACAC,aACAC,gBACEC,YACEX,gBACGC,mBACDsI,WAAA,EACN3H,mBACOjG,mBACAkG,aACC/F,cACD0N,iBACC1H,iBACHC,eACCC,eACA8H,qBACDL,cACCC,eACNC,oBACQC,mBACRC,wBACE5H,oBAEAoK,GACEzC,EAAiDzT,EAErDkW,EAGE,IAEAxC,GAGAgB,CAAgB,CAsLlB,IAAAyB,GAAA,MAAAC,EAEAtV,WAAAA,GACEuV,oBAAuC,gBAAA,MACxCA,eAAAjS,KAAA,aAAA,MACDiS,oBAA6B,iBAC7BA,eAAYjS,KAAS,aAAgB,MACrCiS,eAAejS,KAAI,YAAe,MAClCiS,eAAejS,KAAY,kBAAA,MAC3BiS,eAAUjS,KAAA,cAAA,MACViS,eAAWjS,KAAA,eAAA,GACTiS,eAAYjS,KAAM,YAAe,GACjCiS,eAAQjS,iBACRiS,oBAAkB,YACxBA,eAAWjS,KAAA,aACL,CAAwB,cACzBrE,GAGC,IAFF,IAAAuW,EAAmB,GACjBC,EAAqBnS,KAAAoS,WACjBD,GACJD,EAAIvT,KAAWwT,GACbA,EAAcA,EAAa9D,YACyC,OACrE6D,CACD,CAA6BlW,QAAAA,CAC5B2E,GACJ,KAAAA,aAAAqR,GAAA,OAAA,EACD,GAAWrR,EAAA0R,gBAAAA,KAAAA,cACL,OAAA,EAAA,GAAA1R,IAAYX,KAAA,OAAA,EAChB,KAAIW,EAAM9E,YAAA,CACR,GAAA8E,EAAO9E,aAAgBmE,KAAA,OAAA,EACxBW,EAAAA,EAAA9E,UACD,CACD,OAAA,CACH,CAEArB,WAAAA,CAAA8X,GACI,MAAA,IAAAC,MAEJ,8GACA,CAEEC,YAAAA,CAAaF,EAAAG,GACb,MAAA,IAAAF,MAEA,+GACA,CAEA3X,WAAAA,CAAA8X,GACA,MAAA,IAAAH,MAEA,8GACA,CACFxY,QAAAA,GACA,MAAS,QACP,GAEF,IAAI4Y,GAAkB,CACpBra,mBAAqB,aAAA,gBAAA,eACnBC,WAAe,CAAA,OAAA,eAChBC,QAAA,CAAA,aAAA,gBAAA,oBACDC,iBAAc,IAEZma,GAAmB,CACnBta,KAAI,CAAO,WAAM,eACfC,WAAQ,CAAW,gBAAAC,QACpB,GACCC,iBAAQ,CAAA,gBAwBE,IACXoa,GAAA,CAAA,EACD,SAAIC,GAAsBpb,GACxB,GAAAmb,GAAsBnb,GACtB,OAAImb,GAAenb,GACjB,IAAAmB,EArBL,SAC0BnB,GAAA,IAAAqb,EACrBha,QACJC,YADI+Z,OACMA,EAAV/Z,WACEC,OAAa,MAFX8Z,EAEF7Z,gBAAa,EAFX6Z,EAEF7Z,WAAiBxB,GAAyB,OAE1CqB,GACNC,WAAeD,GAEJC,WACLD,QAGF,CAEE,CAMWia,CAAgCtb,IAASsB,WAAAtB,GACnD0B,EAAAP,EAAAQ,UACFC,EAAA5B,KAAAib,GAAAA,GAAAjb,QAAA,EACD6B,EAAOC,QACRF,GACDA,EAAaG,OACXC,IACA,IAAKC,EAAQC,EACT,OAAAJ,QACmCI,OAAnCA,EAAmCD,OAA7BA,EAASrC,OAAMuC,yBAAcT,EAAAM,SAAAC,EAAAA,EAAAG,UAAAF,EAAAA,EAAAG,WAAAC,SACrC,iBAAA,KAIDC,EAAAvC,KAAAkb,GAAAA,GAAAlb,QAAA,EACGwC,EAAoBV,QACxBS,GAAaA,EAAYR,OAEtBU,IACC,IAAAR,EACJ,yBAAiBP,EAAAe,KAAAR,OAAAA,EAAAP,EAAAe,SAAAR,EAAAA,EAAAI,WAAAC,SAAA,iBAAA,KAIjB,GAAAT,GAAyBW,EAEvB,OADF2Y,GAAsBnb,GAAMmB,EAAMQ,UAC5BR,EAAQQ,UAEZ,IACA,IAAAe,EAAeC,SAAOC,cAAwB,UAC/CD,SAAAE,KAAAC,YAAAJ,GACF,IAAAK,EAAAL,EAAAM,cACD,IAAAD,EAAQ,OAAO5B,EAAAQ,UACb,IAAIsB,EAAgBF,EAAO/C,GAAA2B,UAE3B,OADAgB,SAAIE,KAAQK,YAASR,GAChBO,EACNkY,GAAAnb,GAAAiD,EAD4BvB,CAE7B,CAAA,MAAA6Z,GACE,OAAI7Z,CACJ,CACA,CACE,IAAA8Z,GAAqB,CAAA,EAAa,SACnCC,GAAAzb,EAAAsD,EAAAtB,GACD,IAAAC,EACAsB,EAAavD,EAAQwD,IAAAA,OAAKxB,GAC3B,GAAAwZ,GAAAjY,GACD,OAASiY,GAAOjY,GAAAE,KACdH,GAEF,IAAII,EAAoB0X,GAAApb,GAClB2D,EAGM,OAHN1B,EAAArC,OAAAuC,yBACJuB,EACA1B,SACU,EAAMC,EAAQG,IACtB,OAAAuB,GAAyC6X,GAC1CjY,GAAAI,EACGA,EAAeF,KAAAH,IAFIA,EAAatB,EAGpC,CACE,IAAA0Z,GAAsB,CAAA,EACpB,SAAAC,GAAO3b,EAAAsD,EAAAb,GAAA,IACRc,EAAAvD,EAAAwD,IAAAA,OAAAf,GACD,GAAAiZ,GAAqBnY,GACnB,OAAAmY,GAAOnY,GAAAO,KACRR,GAEH,IACIS,EADgBqX,GAAoBpb,GACtByC,GAClB,MAA2B,mBAAtBsB,EAAiCT,EAAAb,IACtCiZ,GAAiBnY,GAASQ,EACxBA,EAAaD,KAASR,GAAM,CAqC9B,SAAAsY,KACE,OAAAR,GAA2C,oBAAApW,WACzC,CACA,IAAA6W,GAAI,YAtCR,SAAe3X,GACb,OAAAuX,GAAwB,OAAOvX,EAAM,aAAA,aAErC,SAAaA,GACb,OAAAuX,UAAkBvX,EAAA,aAChB,gBAAiC,SAC5BA,GACL,OAAAuX,UAAmBvX,EAAA,gBACjB,cACA,SAAWA,GAA8B,OAC1CuX,UAAAvX,EAAA,cAAA,WACF,SACFA,EAAAK,GACD,OAAIoX,GAAiB,OAAAzX,EAAa,WAA9ByX,CAAsCpX,EAC1C,cACA,SAAOL,GACR,OAAAyX,UAAAzX,EAAA,cAAAyX,EAAA,OAEC,SAAIzX,GACJ,OAAAA,GAAW,SAAQA,EACjBuX,GAAkC,aAAGvX,EAAA,QADpB,IAEf,cACE,SAAWA,GACX,OAAAA,EAAAQ,WACE,aAAmC,SACpCR,GACD,OAAAA,GAAO,eAAAA,EACRuX,aAAAvX,EAAA,cADQ,IACR,gBACF,SACFA,EAAAW,GACD,OAAI4W,GAA6B,UAAAvX,EAAO,gBAApCuX,CAAsC5W,EAC1C,mBAAO,SACRX,EAAAW,GACD,OAAA4W,GAAwB,UAAOvX,qBAA/BuX,CAA+B5W,EAC7B,EAgBDE,iBAAA6W,IAEC,SAAIE,GAAAlS,EAAAmS,EAAAC,QAAA,IAAAA,IAAAA,EAAArZ,UACJ,IAAA0M,EAAM,CAAU4M,SAAQ,EAAAC,SAAA,GAEpB,OADFF,EAAA1H,iBAAmB1K,EAAAmS,EAAW1M,GAC5B2M,IAAAA,EAAW/H,oBAAKrK,EAAAmS,EAAA1M,EAChB,CACE,IAAA8M,GAAmC,4NAAAC,GACpC,CACDC,IAAA,CAAA,EAAOnU,MACRA,KACPqM,QAAKjO,MAAA6V,KACG,GACwC9T,QAC3CA,KACCkM,QAAAjO,MAAQ6V,IACT,MAEFxT,iBAAAA,GACD4L,QAAAjO,MAAc6V,GACZ,EACApT,IAAA,KACEwL,QAAIjO,MAAG6V,KACG,GACiC7S,KAAAA,GAEjDiL,QAAKjO,MAAA6V,GACD,GAWa,SACRG,GAAAC,EAAAC,EAAAnN,QAAA,IAAAA,IAAAA,EAAA,CAAA,GAAA,IACFoN,EAAA,KACFC,EAAA,EACD,OAAI,WAAe,IAAA,IAAAC,EAAAC,UAAAhW,OAARiW,EAAQ/W,IAAAA,MAAA6W,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAARD,EAAQC,GAAAF,UAAAE,GACnB,IAAAC,EAAOC,KAAAD,MACRL,IAAA,IAAArN,EAAA4N,UACDP,EAASK,GAEP,IAAMG,EAAWV,GAAOO,EAAAL,GAClBS,EAAU7U,KACZ4U,GAAW,GAAKA,EAAQV,GACxBC,IACD7C,aAAA6C,GACFA,EAAA,MAEFC,EAAAK,EACDR,EAAAa,MAAaD,EAAON,IACdJ,IAAA,IAAApN,EAAAgO,WACJZ,EAAY/C,YAAO,KACjBgD,OAAOrN,EAAY4N,QAAoB,EAAAD,KAAAD,MACrCN,EAAQ,KACRF,EAAIa,MAAOD,EAAAN,EAAU,GACtBK,GAEH,CAAO,CACR,SACDI,GAAiBtB,EAAAhc,EAAAud,EAAAC,EAAAza,QAAA,IAAAA,IAAAA,EAAA6H,QACf,IAAI6S,EAAW1a,EAAAnD,OAAQuC,yBAAkB6Z,EAAAhc,GAe3C,OAdE+C,EAAAnD,OAAIC,eACJmc,EACEhc,EACAwd,EAASD,EAAA,CACPnU,GAAAA,CAAAnJ,GACEyZ,YAAe,KACf6D,EAAAnU,IAAA3F,KAAQ6E,KAAMrI,EAAM,GACpB,GACAwd,GAAOA,EAAArU,KACRqU,EAAArU,IAAA3F,UAAAxD,EAEJ,IAGH,IAAaqd,GAAOtB,EAAAhc,EAAAyd,GAAA,CAAA,GAAA,EAClB,CACA,SAAAC,GAAWC,EAAQjQ,EAAAkQ,GACjB,IACE,KAAAlQ,KAAQiQ,GACR,MAAI,OAGR,IAAAF,EAAOE,EAAAjQ,GACRmQ,EAAAD,EAAAH,GAWC,MAVoB,mBAAbI,IACPA,EAAIlc,UAAkBkc,EAAAlc,WAAA,CAAA,EACtB/B,OAAIke,iBAAqBD,EAAA,CACrBE,mBAAW,CACb5d,YAAW,EACZF,MAAAwd,MAIDE,EAAKjQ,GAAUmQ,EACX,KACLF,EAAAjQ,GAAA+P,CAAA,CAEC,CAAA,MAAAO,GACA,MAAI,MACgD,CACnD,CA9EW,oBAAdpT,QAAqBA,OAAAqT,OAAArT,OAAAsT,UACnB9B,GAAI,IAAA6B,MAAA7B,GAAA,CACJha,IAAAA,CAAA4Z,EAAYmC,EAAAC,aACND,GACF5J,QAAIjO,MAAU6V,IAEZ+B,QAAU9b,IAAA4Z,EAAamC,EAAGC,OAyEjC,IACDC,GAAiBrB,KAAAD,IAG6B,SAChDuB,GAAAvb,GAAA,IACKd,EAAAC,EAAAqc,EAAAC,EACD5W,EAAK7E,EAAMJ,SACZ,MAAA,CACH8b,KAAA7W,EAAA8W,iBAAA9W,EAAA8W,iBAAAnJ,gBAAA,IAAAxS,EAAA4b,YAAA5b,EAAA4b,YAAA/W,EAAAgX,gBAAArJ,aAAA,MAAA3N,OAAA,EAAAA,EAAA/E,QAAAZ,OAAAA,EAAA4Z,GAAAzX,cAAAwD,EAAA/E,YAAAZ,EAAAA,EAAAsT,cAAA,OAAArT,EAAA,MAAA0F,OAAA,EAAAA,EAAA/E,WAAA,EAAAX,EAAAqT,aAAA,EACIsJ,IAAAjX,EAAA8W,iBAAgB9W,EAAA8W,iBAAAjJ,eAAA1S,IAAAA,EAAA+b,YAAA/b,EAAA+b,aAAAlX,MAAAA,OAAAA,EAAAA,EAAAgX,gBAAAnJ,mBAAA7N,SAAAA,EAAA/E,QAAA,OAAA0b,EAAA1C,GAAAzX,cAAAwD,EAAA/E,YAAA,EAAA0b,EAAA9I,aAAA,OAAA+I,EAAA,MAAA5W,OAAA,EAAAA,EAAA/E,WAAA,EAAA2b,EAAA/I,YAAA,EAEpB,CACA,SAASsJ,KACP,OAAInU,OAAUoU,aAAArc,SAAuBic,iBAAAjc,SAAAic,gBAAAK,cAAAtc,SAAAE,MAAAF,SAAAE,KAAAoc,YACrC,CACF,SAAAC,KACA,OAAItU,OAAAuU,YAAgBxc,SAAAic,iBAAAjc,SAAAic,gBAAAQ,aAAAzc,SAAAE,MAAAF,SAAAE,KAAAuc,WACpB,CACA,SAAMC,GAAsBpW,GAC5B,OAAIA,EAGKA,EAAA/D,WAAiB+D,EAAQ9D,aAAA8D,EAAA4S,GAAAzX,cAAA6E,GAF9B,IAIF,CACE,SAAKqW,GAAOrW,EAAUuG,EAAAC,EAA2Bf,GAC/C,IAAAzF,EACD,OAAA,EAED,IAAIkE,EAAAkS,GAAcpW,GAClB,IAAAkE,EACA,OAAI,EACuB,IAEzB,GAAa,iBAAJqC,EAAI,CACd,GAAArC,EAAAyB,UAAgBtK,SAAQkL,GAAQ,OAAA,EAC/B,GAAAd,GAAoD,OAAjCvB,EAAK8B,QAAM,IAAAO,GAAsB,OAAA,OAEpD,GAAIf,EAAStB,EAAYqC,EAAUd,UAA0B,CAE9D,CAAA,MAAAS,GACF,CACD,GAAAM,EAAO,CACT,GAAAtC,EAAA+B,QAAAO,GAAA,OAAA,EACI,GAAAf,GAAuB,OAANvB,EAAM8B,QAAAQ,GAAA,OAAA,CACzB,CACE,OAAK,CACL,CAII,SAAA8P,GAAKrb,EAAQqL,EAAAgI,GACb,QAAS,UAATrT,EAAAyF,UAAkB4N,EAAgBiI,qBAELjQ,EACpBrH,MAAAhE,KAAAuI,CACL,CAAiB,SAAAgT,GAClBzD,EAAAzM,GAAA,GAAAnK,EACF4W,GAAA,OACT,EACkC,IAC3B7T,EAAAoH,EAAArH,MAAA8T,GAAA,IACFzM,EAAAxG,IAAAZ,GACF,OAAA,EAEC,IAAAoO,EAAMsF,GAAA1X,WAAc6X,GACpB,QAAAzF,GAAUA,EAASrR,WAAK8W,EAAU3L,kBAENkG,GAI3BkJ,GAAAlJ,EAAAhH,GACD,CAAO,SACRmQ,GAAAC,GACD,OAAM7d,QAAK6d,EAAAC,eACT,CAQD,SACMC,GAAK3b,EAAAqL,GACV,OAAKzN,QAAoB,WAAboC,EAAA4b,UAAsBvQ,EAAAnH,QAAAlE,GAClC,CAAO,SACR6b,GAAA7b,EAAAqL,GACD,OAAAzN,QACc,SAAZoC,EAAA4b,UAAiB5b,EAAAgB,WAAAhB,EAAAiB,cAAAjB,EAAA2P,cAAA,eAAA3P,EAAA2P,aAAA,QAAAtE,EAAAnH,QAAAlE,GAEjB,CAAmC,SACpC8b,GAAA9b,GACD,QAAAA,IACEA,aAAaoW,IAAgB,eAAApW,EAC7BpC,QAASoC,EAAQS,YAEhB7C,QAAA+Z,GAAAlX,WAAAT,IACD,CAlGgB,iBAAO2H,KAAAmR,KAAAD,MAAA1a,cACrBgc,GAAUA,KAAArB,IAAAA,MAAAiD,WAiGL,IAAAC,GAAA,MAETlb,WAAAA,GACElF,OAAwB,KAAA,GACxBA,EAAYwI,KAAA,aAAwB,IAAAL,SACpCnI,wBAAOkI,IACR,CACDE,KAAAA,CAAAsJ,GAAY,IAAA2O,EACV,OAAiC,QAAjCA,EAAI7X,KAAA8X,WAAche,IAAMoP,UAAS,IAAA2O,EAAAA,GAAA,CACjC,CACApX,GAAAA,CAAAyI,GACD,OAAAlJ,KAAA8X,WAAArX,IAAAyI,EACD,CAG6BtI,GAAAA,CAAAsI,EACvBrJ,GAAA,OACAG,KAAES,IAAAyI,GAAsBlJ,KAAMJ,MAAMsJ,IAG5C6O,OADQ,IAAAlY,OACRA,KACKA,EACDG,KAAA8X,WAAWhX,IAAAoI,EAAmB6O,GAC/B/X,KAAAgY,WAAAlX,IAAAiX,EAAA7O,GACD6O,GAPgD,IAC1CA,CAOJ,CAAOE,QAAAA,CACDpY,GACF,OAAAG,KAAIgY,WAASle,IAAA+F,IAAW,IACtB,CAAOmB,KAAAA,GAEPhB,KAAA8X,WAA0B,IAAAnY,QAAOK,KAC3CgY,WAAe,IAAAtY,IACLM,KAAAH,GAAA,CAAiB,CAClBqY,UAAAA,GACF,OACGlY,KAAAH,IACF,GAEA,SAAAsY,GAAavc,GAA+E,IAAAjC,EAE1Fye,EAAM,KAED,MAFU,gBAChBxc,WAAAjC,EAAA4Z,GAAArX,YAAAN,WAAAjC,EAAAiD,YAAAtE,KAAA+f,wBAAA9E,GAAApX,KAAAoX,GAAArX,YAAAN,MACDwc,EAAO7E,GAAApX,KAAAoX,GAAArX,YAAAN,KACRwc,CAAA,CAQqB,SACrBE,GAAA1c,GAAA,IACF0D,EAAA1D,EAAAyW,cACF,IAAA/S,EAAA,OAAA,EACD,IAAA8Y,EAXF,SACGxc,GAGG,IAFJ,IACEwc,EADFG,EAAY3c,EAERwc,EAAgBD,GAAII,IACpBA,EAAWH,EACX,OAAAG,CACE,CAICC,CAAA5c,GACL,OAAK2X,GAAKvX,SAAQsD,EAAO8Y,EACzB,CACA,SAAAK,GAAO7c,GACR,IAAA0D,EAAA1D,EAAAyW,cACD,QAAA/S,IACMiU,GAAMvX,SAAKsD,EAAO1D,IAAA0c,GAAA1c,GACtB,CACE,IAAA8c,GAA0B,CAAAC,IAAgCA,EAChEA,EAA0B,iBAAA,GAAA,mBACpBA,EAAAA,EAAuB,KAAK,GAAA,OAC5BA,EAAIA,EAAS,aAAqB,GAAiB,eACnDA,EAAIA,EAA0B,uBAAuB,sBAAoBA,EAC1EA,EAAA,KAAA,GAAA,OACDA,EAAOA,EAAA,OAAA,GAAA,SACRA,EAAAA,EAAA,OAAA,GAAA,SACDA,GAR8B,CAS5BD,IAAa,CAAA,GACbE,GAA+B,CAAAC,IAC/BA,EAAgBA,EAAa,SAAA,GAAA,WAC7BA,EAAiBA,EAAmB,UAAA,GAAA,YAClCA,EAAaA,EAAY,iBAAA,GAAA,mBACvBA,EAASA,EAAA,OAAA,GAAA,SACTA,EAAQA,EAAA,eAAA,GAAA,iBAAAA,EACHA,EAAA,MAAA,GAAA,QACLA,EAAUA,EAAA,UAAA,GAAA,YAAAA,EACXA,EAAA,iBAAA,GAAA,mBAAAA,EACFA,EAAA,eAAA,GAAA,iBACDA,EAAiBA,EAAA,eAAA,GAAA,iBAClBA,EAAAA,EAAA,KAAA,IAAA,OACDA,EAAOA,EAAA,IAAA,IAAA,MACLA,EAAkBA,EAAO,KAAA,IAAA,OACzBA,EAAaA,EAAsB,iBAAA,IAAA,mBACnCA,EAAYA,EAAuB,UAAA,IAAA,YACpCA,EAAAA,EAAA,kBAAA,IAAA,oBACDA,EAAcA,EAAA,cAAA,IAAA,gBACRA,GAlB2B,CAkBnBD,IACU,CAAA,GAAME,GACF,CAAAC,IAAAA,EAC9BA,EAAA,QAAA,GAAA,UACIA,EAAeA,EAAa,UAAA,GAAA,YAAAA,EACbA,EAAoB,MAAA,GAAA,QAAAA,EACtBA,EAAW,YAAA,GAAA,cAAAA,EACpBA,EAAA,SAAA,GAAA,WAAAA,EACYA,EAAS,MAAA,GAAA,QAAAA,EACXA,EAAA,KAAA,GAAA,OAAAA,EAClBA,EAAA,WAAA,GAAA,aACIA,EAAeA,EAAA,mBAAA,GAAA,qBACbA,EAAIA,EAA4B,SAAA,GAAA,WAChCA,EAAaA,EAAkC,YAAI,IAAA,cACnDA,GAZwB,CAatBD,IAAa,CAAA,GACbE,GAA0B,CAAAC,IAA+CA,EAC1EA,EAAA,MAAA,GAAA,QAAAA,EACIA,EAAA,IAAA,GAAA,MACLA,EAASA,EAAO,MAAA,GAAA,QACdA,GAJ0B,CAIlBD,IACE,CAAA,GAAWE,GACF,CAAAC,IAAAA,EAC3BA,EAAA,MAAA,GAAA,KAAAA,EACiBA,EAAY,MAAA,GAAA,QACrBA,EAAQA,EAAoB,OAAU,GAAA,SACvCA,GAJoB,CAKrBD,IAAc,CAAA,GACZE,IAAMC,IAAAA,EACIA,EAAS,KAAA,GAAA,OAAAA,EACNA,EAAI,MAAA,GAAA,QAAAA,EACzBA,EAAA,OAAA,GAAA,SAAAA,EACiBA,EAAmC,aAAA,GAAA,eAC5CA,EAAWA,EAA4B,WAAA,GAAA,aAC/CA,IACQD,IAAW,CAAA,GAA6B,SACzCE,GAAA1d,GAAA,MACF,SAAAA,CACD,CACE,MAAA2d,iBAEF/hB,EAAcwI,KAAA,SAAA,GACfxI,EAAAwI,KAAA,OAAA,MACDxI,EAAuBwI,KAAA,OAAA,KACrB,CACAlG,GAAAA,CAAA0f,GACD,GAAAA,QAAAlb,OACD,MAAS,IAAAiU,MAAA,kCAGN,IADC,IAAAkH,EAAYzZ,KAAA0Z,KACbC,EAAAA,EAAAA,EAAAH,EAAAG,IACDF,GAAcA,MAATA,OAASA,EAAAA,EAAAG,OAAA,KAEf,OAAAH,CACD,CACEI,OAAAA,CAAIje,GACF,IAAI+E,EAAA,CACJhJ,MAAIiE,EACJwY,SAAS,KACPwF,KAAI,MAGF,GAFYhe,EAAAke,KACbnZ,EACC/E,EAAA0S,iBAAYgL,GAA2B1d,EAAA0S,iBAAA,CACvC,IAAAmL,EAAW7d,EAAA0S,gBAAAwL,KAAAF,KAAAjZ,EACrBiZ,KAAeH,EACL9Y,EAAAyT,SAAYxY,EAAA0S,gBAAawL,KAAele,EACzC0S,gBAAAwL,KAAAF,KAAAjZ,EACF8Y,IACDA,EAAKrF,SAAWzT,EACH,MACZ,GAAA/E,EAAAyS,aAAAiL,GAAA1d,EAAAyS,cAAAzS,EAAAyS,YAAAyL,KAAA1F,SAAA,CACF,IAAAqF,EAAA7d,EAAAyS,YAAAyL,KAAA1F,SACDzT,EAAOyT,SAAAqF,EACR9Y,EAAAiZ,KAAAhe,EAAAyS,YAAAyL,KACDle,EAAOyS,YAAAyL,KAAA1F,SAAAzT,EACD8Y,IACJA,EAAOG,KAAQjZ,EAEd,MACDX,KAAO0Z,OACR1Z,KAAA0Z,KAAAtF,SAAAzT,GAECA,EAAIiZ,KAAQ5Z,KAAA0Z,KACZ1Z,KAAI0Z,KAAA/Y,EAEkB,OAAlBA,EAAAiZ,OACJ5Z,KAAA+Z,KAASpZ,GAELX,KAAA1B,QAAA,CAEF0b,UAAAA,CAAIpe,GACJ,IAAI6d,EAAQ7d,EAAKke,KACjB9Z,KAAI0Z,OAGED,EAAArF,UAQJqF,EAAIrF,SAAUwF,KAAOH,EAAIG,KACzBH,EAAIG,KACFH,EAAAG,KAAUxF,SAAAqF,EAAArF,SAEVpU,KAAA+Z,KAAAN,EAAArF,WAZ+BpU,KACzC0Z,KAAiBD,EAAAG,KACL5Z,KAAA0Z,KAAO1Z,KACR0Z,KAAAtF,SAAA,KAEJpU,KAAA+Z,KAAiB,MASFne,EACZke,aACAle,EAAAke,KACa9Z,KACvB1B,SAAA,EAEsB,IAgfiB2b,GA/ehCC,GAAAA,CAAAra,EAAAsa,IAAAta,EAAA,IAAAsa,EAAA,MACFC,GACD1d,WAAAA,GACElF,EAAMwI,KAAoB,UAAA,GAC3BxI,EAAAwI,KAAA,UAAA,GACDxI,EAAOwI,KAAA,QAAA,IACRxI,EAAAwI,KAAA,aAAA,IACDxI,0BAAUmI,SACRnI,EAAsBwI,KAAA,UAAA,IACpBxI,EAAKwI,KAAa,aAAgB,IACnCxI,EAAAwI,KAAA,WAAA,IACDxI,sBAAY6iB,KACb7iB,sBAAA6iB,KACD7iB,EAASwI,KAAA,aAA8B,IAAAqa,KACrC7iB,OAA4B,cAC5BA,EAAcwI,KAAA,cACdxI,OAAoB,iBAClBA,EAAWwI,KAAA,iBACjBxI,EAAKwI,KAAA,oBACDxI,EAAOwI,KAAA,oBACRxI,EAAAwI,KAAA,oBACDxI,OAA0B,cACxBA,EAAmBwI,KAAA,eACnBxI,EAASwI,KAAY,mBACrBxI,EAAOwI,KAAa,gBACrBxI,EAAAwI,KAAA,gBACDxI,EAAcwI,KAAA,kBACZxI,EAAOwI,KAAA,kBACRxI,EAAAwI,KAAA,OACHxI,EAAAwI,KAAA,UACIxI,EAASwI,KAAA,iBACbxI,EAAmBwI,KAAA,qBACfxI,EAAWwI,KAAA,oBACXxI,OAAwB,iBAC1BA,EAAsBwI,KAAA,wBACpBxI,OAAgC,iBAC9BA,EAAgBwI,KAAA,oBAAiBsa,IAClCA,EAAA/Z,QAAAP,KAAAua,iBACDva,KAAMwa,MAAA,IAEPhjB,EAAAwI,KAAA,QAAA,KACG,IAAAya,KAAAA,cAAWC,OAAX,CAwEJ,IArEF,IAAAC,EAAA,GACIC,MAAgBP,IACpBQ,MAA0BtB,GACtBuB,EAAgBlf,IAGZ,IAFJ,IAAAmf,EAAAnf,EACMof,EAAO7W,EACJ6W,IAAA7W,GAET6W,GADID,EAAKA,GAAAA,EAAA1M,cACIrO,KAAAgH,OAAApH,MAAAmb,GAEZ,OAAAC,CAAA,EAELC,EAAArf,IACA,IAAAqS,EAAAsF,GAAA1X,WAAAD,GACI,GAAAqS,GAAcwK,GAAO7c,IAAO,aAAAqS,EAAA5M,QAA5B,CAGF,IAAO8Y,EAAMrd,EAAAmR,GAAAjO,KAAAgH,OAAApH,MAAAuY,GAAAvc,IAAAoE,KAAAgH,OAAApH,MAAAqO,GACL+M,EAAAF,EAAmBlf,GAC1B,QAAAue,IAAA,IAAAa,EACM,OAAAH,EAAAhB,QAAAje,GAEL,IAAA6T,EAAcV,GAAUnT,EAAY,CAClC0D,SAAAA,IACA0H,OAAYhH,KAAAgH,OACZE,gBAASA,WACNC,cAAanH,KAAKmH,cACrBV,mBAAQA,cACVC,iBAAwB1G,KAAA0G,iBACnBsI,WAAA,EACLrH,mBAAsB,EACvBN,sBAAAA,iBACHjG,sBAAAA,iBACIkG,WAAkBtH,KAAAsH,WACpB/F,YAAiBvB,KAAMuB,YACjB0N,eAAajP,KAAOiP,eACnB1H,eAAevH,KAAGuH,eAClBE,aAAczH,KAAAyH,aACfD,aAAYxH,KAAMwH,aAClB0H,YAAYgM,IACN3D,GAAgB2D,EAAMlb,KAAAgH,SACzBhH,KAAAmb,cAAeC,UAAAF,GAEbzD,GAAqByD,EAAYlb,KAAKgH,SACrChH,KAAKqb,kBAAOC,iBACvBJ,GAGQxD,GAAgB9b,IACtBoE,KAAAub,iBAAAC,cAAAjI,GAAAlX,WAAAT,GAAAoE,KAAAV,IACM,EAET6P,aAAmBA,CAAAsM,EAAAC,KACb1b,KAAAmb,cAAiBQ,aAAAF,EAAAC,GACjB1b,KAAAub,iBAAUK,oBAAAH,EAAA,EAEVpM,iBAAMA,CAAAqC,EAAAgK,KACN1b,KAAAqb,kBAAyBQ,kBAAgBnK,EAAAgK,EAAA,IAGzCjM,IACFkL,EAAOhc,KAAA,CACRwb,WACGa,SACEc,KAAIrM,IAEZmL,EAAiBha,IAAA6O,EAAA5P,IAlDb,CAmDF,EAESG,KAAA+b,WAAAzd,QACL0B,KAAAgH,OAAO3G,uBAAyB0b,WAAAC,SAErC,IAAApgB,IAAAA,UAAAqgB,SACDC,GAAoBC,KAAAA,QAAAvgB,OAAAoL,UAAAhH,KAAAic,SAAAxb,IAAA8S,GAAA1X,WAAAD,KAGdqf,EAAQrf,GAEZ,IAAI,IAAQA,KAAMoE,KAAMoc,SACjBC,GAAkBrc,KAAAsc,WAAiB1gB,IAAIsgB,GAAoBlc,KAACmc,QAAAvgB,OAAAoL,QAEpEqV,GAAArc,KAAAic,SAAArgB,GACDqf,EAAerf,GAEToE,KAAAsc,WAAiB1b,IAAGhF,GAJvBqf,EAAArf,GAQF,IADE,IAAA2gB,EAAA,KACF1B,EAAAvc,QAAA,CACD,IAAQqC,EAAY,KACd,GAAA4b,EAAgB,CAChB,IAAMpC,EAAAna,KAAAgH,OAAApH,MAAA2T,GAAA1X,WAAA0gB,EAAA5kB,QACJqjB,EAAOF,EAASyB,EAAU5kB,YAC5BwiB,IAAO,IAAAa,IACfra,EAAiB4b,EAET,CACE,IAAA5b,EAEE,IADF,IAAI6b,EAAM3B,EAAAd,KACRyC,GAAU,CAAA,IACR9J,EAAA8J,EAEH,GADXA,EAAAA,EAAApI,SACW1B,EAAA,CACD,IAAOyH,EAAAna,KAAAgH,OAAApH,MAAA2T,GAAA1X,WAAA6W,EAAA/a,QAEjB,IAAiB,IADRmjB,EAAApI,EAAA/a,OACwB,SAClB,IAAuB,IAAvBwiB,EAAuB,CACtCxZ,EAAiB+R,EACF,MAEA,IAAK+J,EAAc/J,EAAA/a,MACrBsW,EAAAsF,GAAA1X,WAAA4gB,GACC,GAAIxO,GAAAA,EAAArR,WAAAtE,KAAA+f,uBAAA,CACR,IAAAD,EAAA7E,GAAApX,KAAA8R,GAEH,IAAA,IADPjH,KAAAA,OAAApH,MAAAwY,GACO,CACazX,EAAQ+R,EACV,KACH,CACC,CAEV,CACD,CAEH,IAAU/R,EAAQ,CACX,KAAQka,EAAOnB,MACbmB,EAAOb,WAAUa,EAAMnB,KAAM/hB,OAEtC,KACE,CACD4kB,EAAA5b,EAAAyT,SACHyG,EAAAb,WAAArZ,EAAAhJ,OACIsjB,EAAAta,EAAgBhJ,MACpB,CACM,IAAA+kB,EAAA,CACAC,WAAAA,MAAe5I,KAAAvS,IACf,IAAA5F,EAAY4F,EAAAsa,KACJ7N,EAAUsF,GAAG1X,WAAAD,GAIvB,OAHAqS,GAAsB,aAAtBA,EAAsB5M,SACtBrB,KAAA4c,yBAAqB3O,GAErB,CACApO,QAAAmH,OAAApH,MAAyBhE,GACzBjE,MAAA6J,EAAoB7J,MACpB,IACFklB,QAAiBrb,IAAOoZ,EAAIna,IAAAe,EAAA3B,MAAAgd,QAAArb,GAAAwF,KAAAA,OAAAvG,IAAAe,EAAA3B,MACtB+I,gBAAgBA,WAAOmL,KAAQ+I,IACjC,IAAUlU,WAAAA,GAAMkU,EACjB,GAAA,iBAAAlU,EAAAmU,MAAA,CACI,IAAMC,EAAIxe,KAAAC,UAAAqe,EAAAG,WACDC,EAAM1e,KAAYC,UAAUqe,EAAWK,kBAC9CH,EAAS1e,OAAAsK,EAAAmU,MAAAze,SACH0e,EAASE,GAAO7e,MAAA,QAAAC,SAAAsK,EAAAmU,MAAA1e,cAAAC,SACtBsK,EAAAmU,MAAAD,EAAAG,UAGH,CACE,MAAC,CACHpd,GAAKG,KAAOgH,OAAKpH,MAAAkd,EAAAhB,MACzBlT,aACQ,IACDiU,QAAAC,IAAAlC,EAAAna,IAAAqc,EAAAjd,MAAAgd,QAAAC,GAAA9V,KAAAA,OAAAvG,IAAAqc,EAAAjd,MACFsc,aAAAA,QACGxB,SAEE+B,EAAIC,MAAMre,QAAAoe,EAAA9T,WAAAtK,QAAAoe,EAAAP,QAAA7d,QAAAoe,EAAA/B,KAAArc,UAGZ0B,KAAA2c,MAAU,GACX3c,KAAA4I,WAAA,GACF5I,KAAAod,iBAAAzd,QACDK,KAAKmc,QAAW,GACdnc,KAAKoc,SAAqB,IAAU/B,IACrCra,KAAAic,aAAA5B,IACDra,KAAIsc,WAA+B,IAAAjC,IACpCra,KAAAqd,SAAA,CAAA,EACDrd,KAAMsd,WAASZ,GAjLd,CAiLoB,IAEnBllB,EAAYwI,KAAO,4BAAmBud,IACpC,IAAIC,EAAQxd,KAAAod,aAAAtjB,IAAAyjB,GACRC,IACJA,EAAI,CACF1B,KAAIyB,EACJ3U,WAAW,CAAA,EACXqU,UAAa,CAAA,EACrBE,iBAAa,CAAA,GAELnd,KAAA4I,WAAejK,KAAA6e,GAChBxd,KAAAod,aAAAtc,IAAAyc,EAAAC,IAECA,EAAA5U,WAAUjR,MAAK6F,MAAWC,KAC1B8V,GAAA5X,WAAc4hB,IACdE,GAAAlK,GAAYxX,YAAI0hB,IAAA,KACxB5f,KAAa,GAAA,IAELrG,EAAAwI,KAAgB,mBAAA0d,IACjB,IAAAzG,GAAAyG,EAAAhK,OAAA1M,KAAAA,YAAAiI,gBAGD,OAAOyO,EAAApc,MACP,IAAa,gBACd,IAAA3J,EAAA4b,GAAAxX,YAAA2hB,EAAAhK,QACGsD,GAAc0G,EAAAhK,OAAa1T,KAAAkH,WAAiBlH,KAAAmH,eAASxP,IAAAA,IAAA+lB,EAAAC,UACrD3d,KAAQ2c,MAAAhe,KAAA,CACVhH,MAAc6O,EACZkX,EAAAhK,OACO1T,KAAAyG,cACAzG,KAAA0G,kBACA,IAEF/O,EAAA2P,KAAAA,gBAAAA,WAAA3P,EAAAof,GAAA2G,EAAAhK,SAAA/b,EAAAoG,qBAAApG,EACbmkB,KAAA4B,EAAAhK,SAGQ,MAEA,IAAY,aACZ,IAAKA,EAAAgK,EAAAhK,OACAkK,EAAAF,EAAAE,cACAjmB,EAAA+lB,EAAAhK,OAAAnI,aAAAqS,GACb,GAAA,UAAAA,EAAA,CACK,IAAAtc,EAAAQ,EAAA4R,GACO/b,EAAQsJ,EAAU,CACjBE,QAAMuS,EACTtS,sBAAmBA,iBACrBC,QAAcqS,EAAMrS,QACrBC,OACO3J,MAAAA,EACT4J,YAAAA,KAAAA,aAEF,CACD,GAAWyV,GAAQ0G,EAAAhK,OAAAxM,KAAAA,WAAAC,KAAAA,eAAAxP,IAAAA,IAAA+lB,EAAAC,SACb,OAEE,IAAAH,OAAaJ,aAActjB,IAAA4jB,EAAAhK,QAC/B,GAAwB,WAAxBA,EAAcrS,SAAsB,QAAAuc,IAAA5d,KAAA0H,gBAAA/P,GAAA,CAChC,GAAA+b,EAAYhG,gBAGd,OAFFkQ,EAAsB,QAItB,CAcI,GAbCJ,IACLA,EAAA,CACD1B,KAAA4B,EAAAhK,OACD9K,WAAuB,CAAA,EACbqU,UAAA,CAAA,EACNE,iBAAoB,CAAA,GAEjBnd,KAAA4I,WAAAjK,KAAA6e,GACDxd,KAAMod,aAAYtc,IAAA4c,EAAAhK,OAAS8J,IAEb9J,SAAXkK,aAAWlK,EAAArS,SAAA,cAAAqc,EAAAC,UAAA,IAAAjc,eAChBgS,EAAMxO,aAAoB,sBAAA,SAExBe,EAAYyN,EAAArS,QAAAuc,GAOf,GANEJ,EAAA5U,WAAUgV,GAAsBzY,EAC/BnF,KAAMV,IAChBoC,EAAegS,EAAArS,SACLK,EAAMkc,GACNjmB,GAEH,UAAAimB,EAAA,CACF,IAAA5d,KAAA6d,cACM,IACA7d,KAAS6d,cAAmBxjB,SAAAyjB,eAAAC,oBAC3B,CAAM,MAAAlX,GAClB7G,KAAA6d,mBAAAve,GACG,CAEK,IAAY0e,EAAKhe,KAAI6d,cAAGvjB,cAAA,QAI7B,IAAA2jB,IAAAA,KAHUP,EAAAC,UACRK,EAAA9Y,aAAAwY,QAAAA,EAAAC,UAEFngB,MAAAC,KAAAiW,EAAAqJ,QAAA,CACY,IAAAmB,EAAiBxK,EAAAqJ,MAAWoB,iBAAAF,GACxBG,EAAO1K,EAAAqJ,MAAAsB,oBAAAJ,GACPC,IAASF,EAAQjB,MAAAoB,iBAAAF,IAAAG,IAAAJ,EAAAjB,MAAAsB,oBAAAJ,GAEtBT,EAAQP,UAAOgB,GADL,KAATG,EACcF,EAEQA,CAAAA,EAAAE,GAG7BZ,EAAAL,iBAAAc,GAAAC,CAAAA,EAAAE,EAEF,CACK,IAAAH,IAAAA,KAAAzgB,MAAAC,KAAAugB,EAAAjB,OACS,KAAArJ,EAAAqJ,MAAAoB,iBAAAF,KACPT,EAAAP,UAAAgB,IAAA,EAGR,KAAA,SAAAL,GAAA,WAAAlK,EAAArS,UACaqS,EAAA9M,QAAA,gBACJ4W,EAAK5U,WAAA,aAAA,QAEJ4U,EAAM5U,WAAG,aAAA,aAIhB,MAEA,IAAe,YACvB,GAAaoO,GAAA0G,EAAAhK,OAAAxM,KAAAA,WAAAC,KAAAA,eACL,GAAA,OACD,GAAA,aAAAuW,EAAAhK,OAAArS,QAEC,YADHrB,KAAA4c,yBAAAc,EAAAhK,QAGDgK,EAAOY,WAAA/d,SAAA3E,GAAA2iB,KAAAA,QAAA3iB,EAAA8hB,EAAAhK,UACRgK,EAAAc,aAAAje,SAAA3E,IACQ,IAAA6iB,EAAAzX,KAAAA,OAAApH,MAAAhE,GACIue,EAAArd,EAAA4gB,EAAAhK,QAAA1M,KAAAA,OAAApH,MAAA2T,GAAApX,KAAAuhB,EAAAhK,SAAA1T,KAAAgH,OAAApH,MAAA8d,EAAAhK,QACFsD,GAAS0G,EAAAhK,YAAiBxM,WAAelH,KAAAmH,eAAA,IAAA8P,GAAArb,EAAAoL,KAAAA,OAAAiI,KAAAA,kBAvmBlD,SAAerT,EAAAqL,GACf,OAA2B,IAA3BA,EAASrH,MAAQhE,EACf,CAqmBgD8iB,CAAA9iB,EAAAoE,KAAAgH,UAG/ChH,KAAAoc,SAAA3b,IAAA7E,IACF+iB,GAAA3e,KAAAoc,SAAAxgB,GACQoE,KAAKsc,WAAA1b,IAAAhF,IACIoE,KAAKoc,SAAG3b,IAAAid,EAAAhK,UACf,IADe+K,GACXtH,GAAeuG,EAAAhK,YAAA1M,UACjBiV,KAAAA,SAAgBxb,IAAA7E,IAAAoE,KAAAqd,SAAAnD,GAAAuE,EAAAtE,IAC1BwE,GAAA3e,KAAAic,SAAArgB,GAEIoE,KAAAmc,QAAAxd,KAAA,CACRwb,WACUta,GAAA4e,EACG1N,YAAQjU,EAAK4gB,EAAAhK,UAAA1W,EAAA0gB,EAAAhK,eAAA,KAGzB1T,KAAU+b,WAAApd,KAAA/C,GAAA,IAId,IAEIpE,EAAsBwI,KAAA,WAAA,CAAUpE,EAAA8X,KAC9B,IAAA1T,KAAA4e,qBAAeC,cAAoBjjB,EAAAoE,QACrCoc,KAAAA,SAAY3b,IAAA7E,UAAAqgB,SAAAxb,IAAA7E,GAAZ,CACA,GAAAoE,KAAAgH,OAAAtG,QAAuB9E,GAAQ,CAC/B,GAAAqb,GAAkBrb,EAAAoE,KAAQgH,OAAAhH,KAAaiP,gBACvC,OAEAjP,KAAKic,SAAYrb,IAAAhF,GACjB,IAAKkjB,EAAe,KACfpL,GAAO1M,KAAAA,OAAAtG,QAAAgT,KACPoL,EAAO9X,KAAAA,OAAApH,MAAA8T,IAEPoL,IAAc,IAAAA,IACd9e,KAAAqd,SAAenD,GAAMlT,KAAAA,OAAQpH,MAAQhE,GAAKkjB,KAAQ,QAGvD9e,KAAKoc,SAAAxb,IAA+BhF,GACrCoE,KAAAsc,WAAAhc,OAAA1E,GAEKob,GAAApb,EAAAsL,KAAAA,gBAAAC,eAAA,KACAoM,GAAK5X,WAAYC,GAAA2E,SAAAsQ,GAAA7Q,KAAAue,QAAA1N,KACnB6G,GAAU9b,IACX2X,GAAU5X,WAAY4X,GAAAlX,WAAQT,IAAA2E,SAAesQ,IAC5C7Q,KAAU4e,qBAAahe,IAAAiQ,EAAA7Q,MACxBA,KAAUue,QAAO1N,EAAKjV,EAAQ,IAtBnB,CAyBV,GAEF,CACAmjB,IAAAA,CAAAhY,GACA,CACD,aACD,aACE,gBACE,gBACA,mBACA,mBACA,mBACE,aACA,cACE,kBAAqB,eAE/B,eACQ,iBACD,iBACD,MACD,SACF,gBACD,oBACE,mBACA,gBACE,wBACAxG,SAAS7I,IACPsI,KAAAtI,GAAQqP,EAAUrP,EAAA,GAElB,CACEsnB,MAAAA,GAAwBhf,KACzBya,QAAA,EAAAza,KACFif,cAAAD,QAAA,CAEDE,QAAAA,GACDlf,KAAAya,QAAA,EACFza,KAAAif,cAAAC,WACDlf,KAAAwa,MACE,CACA2E,QAAAA,GACE,OAAAnf,KAAOya,OAEP2E,IAAAA,GACApf,KAAA0a,QAAe,EACb1a,KAAAif,cAAWG,MAAA,CAEbC,MAAAA,GACDrf,KAAA0a,QAAA,EACF1a,KAAAif,cAAAI,SACDrf,KAAAwa,MACE,CACExZ,KAAAA,GACDhB,KAAAub,iBAAeva,QACdhB,KAAAif,cAAgBje,OAChB,EACoD,SAClD2d,GAAAW,EAAA1jB,GAAsB0jB,EACvBhf,OAAA1E,GAAA2X,GACP5X,WAAWC,GAAA2E,SAAAsQ,GAAA8N,GAAAW,EAAAzO,IACL,CAAoC,SAClCqL,GAAWC,EAAYvgB,EAAAqL,GAAA,OACD,IADCkV,EACvB7d,QAEkB,SACL6d,EAAWvgB,EAAMqL,GAAG,IAQrCsY,EARqC5e,EACjC4S,GAAY1X,WAAWD,GAAA4jB,EAAAA,WAC0C,IAClErF,EAAAlT,EAAArH,MAAAe,GACF,GAAAwb,EAAAvR,MAAA6U,GAAAA,EAAA5f,KAAAsa,IAAA,MAAA,CAAAuF,GACQ,GAET/e,EAAI4S,GAAK1X,WAAgB8E,EACzB,EAPoC,KAChCA,GAAA,GAAA4e,EAAAC,IAAA,OAAAD,EAAAG,EAOF,OAAQ,CAAQ,CAXfC,CAAAxD,EAAAvgB,EAAAqL,EACD,CAYA,SAAAoV,GAAkBvb,EAAKlF,GAAG,OAC3B,IAAAkF,EAAA8e,MACFC,GAAA/e,EAAAlF,EAAA,CAEC,SAAKikB,GAAM/e,EAAAlF,GACX,IAAAqS,EAAWsF,GAAI1X,WAAAD,GAAqB,QAClCqS,MAGFnN,EAAIL,IAAAwN,IAGA4R,GAAU/e,EAAAmN,GAAA,CASV,IAAA6R,GAAQC,IACR,IAAA9F,GACA,OAAA8F,EAYF,OAVYC,WACR,IACA,OAAAD,KAAKzL,UAAsB,CAAA,MACrCtW,GACU,GAAAic,KAAiB,IAATA,GAASjc,GACjB,OAEA,MAAAA,CAA2B,CAC5B,CAES,EAEViiB,GAAc,GACd,SAAAC,GAAW7I,GACX,IAAsB,GAC9B,iBAAaA,EAAA,CACL,IAAApV,EAAUoV,EAAI8I,eACf,GAAAle,EAAA3D,OACD,OAAI2D,EAAS,EAEX,MAAA,GAAI,SAAYoV,GAAMA,EAASpV,KAAA3D,OAC/B,OAAK+Y,EAAApV,KAAa,EAEd,CAAA,MAAAme,GACA,CACA,OAAA/I,GAAAA,EAAQ3D,MACR,CACA,SAAA2M,GAAkBtZ,EAASuZ,GAC3B,IAAAC,MAASnG,GAAkB6F,GACtBthB,KAAA4hB,GACLA,EAAAxB,KAAQhY,GACR,IAAAyZ,EAAA,IAAQlN,KAAR,CACAwM,GAAQS,EAASE,iBAASjlB,KAAA+kB,KAUlC,OARQC,EAAAE,QAAAJ,EAAQ,CACR1X,YAAK,EAAsB+X,mBAC5B,EAAAC,eACF,EAAAC,uBACF,EACPC,WAAK,EACFC,SAAA,IAEKP,CACF,CAiEE,SAAAQ,GAA0BC,GAMvB,IANuBC,mBACxBA,EAAU5hB,IACRA,EACA0H,OAAAC,EAAKC,WACHA,EAAAC,cAA0BA,EAC3Bga,SAAAA,GACFF,EACF,IACI,IAAbE,EAAaC,iBACL,MAAA,OAED,IACFC,GAAA,IAAAF,EAAAC,uBAAA,IAAAD,EAAAC,iBAAA,GAAAD,EAAAC,iBACDE,EAAY,GACbC,EAAA,KA4EC,OArBAjqB,OAAK6I,KAAA2Y,IAAuB+D,QAC5BnlB,GAAO8pB,OAAAC,MAAAD,OAAA9pB,MAAAA,EAAAgqB,SAAA,eAAA,IAAAL,EAAA3pB,KACR6I,SAAAohB,IACH,IAAAC,EAAAlgB,EAAAigB,GACIE,EA1DkBF,IACdtK,IACJ,IAAI3D,EAAWwM,GAAA7I,GACb,IAAAL,GAAUtD,EAAMxM,EAAUC,GAAA,GAA1B,CAGI,IAAA2a,EAAa,KACXC,EAAYJ,EACZ,GAAA,gBAActK,EAAK,CACnB,OAAAA,EAASyK,aACV,IAAA,QACFA,EAAA9I,GAAAgJ,MACF,MACP,IAAe,QACLF,EAAY9I,GAAiBiJ,MAC5B,MACN,IAAA,MACFH,EAAA9I,GAAAkJ,IAGGJ,IAAkB9I,GAAiBiJ,MACzCnJ,GAAoB6I,KAAc7I,GAAAqJ,UAC5BJ,EAA4B,aACvBjJ,GAAA6I,KAAA7I,GAAAsJ,UACLL,EAA4B,YAE/B/I,GAAAkJ,SACa9K,GAAAC,KACRyK,EAAQ9I,GAAAiJ,OAEL,OAAXH,GACMP,EAAmBO,GACpBC,EAAA9c,qBAAA6c,IAAA9I,GAAAiJ,OAAAF,EAAA9c,WAAA,UAAA6c,IAAA9I,GAAAgJ,SACFF,EAAA,OAEchJ,GAAK6I,KAAyB7I,GAAAuJ,QACvCP,EAAQP,EACRA,EAAiB,MAEnB,IAAK1a,EAAAuQ,GAAqBC,GAAaA,EAAAC,eAAA,GAAAD,EACvC,GAAAxQ,EAAA,CAEU,IACRhH,EAAAoH,EAAArH,MAAA8T,IACR4O,QAAAA,EAAAC,QAAAA,GAAA1b,EACKiZ,GAAAoB,EAAApB,CAAA0C,EAAA,CACFlhB,KAAAwX,GAAAiJ,GACKliB,KACA4iB,EAAAH,EACAnY,EAAAoY,GACgB,OAAhBT,GAAgB,CAAAA,gBARlB,CArCI,CA8CJ,EAOFY,CAAiBf,GACjB,GAAArf,OAAWqgB,aACX,OAAA7J,GAAoB6I,IACtB,KAAA7I,GAAsBqJ,UACpB,KAAMrJ,GAAQsJ,QACTR,EAAOA,EAAA7jB,QACb,QACH,WAEA,MACM,KAAA+a,GAAsB8J,WACxB,KAAA9J,GAAkB+J,SAClB,OAGJvB,EAAS3iB,KAAA6U,GAAAoO,EAAqBC,EAAAviB,GAAA,IAEtBwgB,IAAqB,KACzBwB,EAAO/gB,SAAGuiB,GAAAA,KAAA,GACH,CAEX,SAAAC,GAAAC,GAOG,IAPHC,SACAA,EAAS3jB,IACPA,EACA0H,OAAIC,EAAMC,WACRA,EAAAC,cACEA,EAAAga,SAAgBA,GACjB6B,EA4BD,OAAAxP,GAAO,SA1BXsM,GACI9L,GACF8L,IAAoBoD,IAClB,IAASxP,EAASwM,GAAUgD,GAC1B,GAAIxP,IAAasD,GAAUtD,EAAOxM,EAASC,GAAA,GAA3C,CAGF,IAAKtH,EAASoH,EAAArH,MAAA8T,GACd,GAAOA,IAAApU,GAAAA,EAAA6jB,YAAA,CACR,IAAAC,EAAApN,GAAA1W,EAAA6jB,aACDF,EAAU,CACFpjB,KACF4iB,EAAKW,EAAOjN,KACdhM,EAASiZ,EAAc7M,KAE1B,MACI0M,EAAU,CACRpjB,KACD4iB,EAAA/O,EAAWzG,WACX9C,EAAAuJ,EAAQvG,WAbX,CAeC,IAEAgU,EAAIkC,QAAY,MAGU/jB,EAC5B,CAsByB,IAAAgkB,GACZ,CAAA,QAAiB,WAAA,UAClBC,GAAkB,IAAY5jB,QAqH3C,SACD6jB,GAAqB9lB,GAgBpB,OAdC,SAAS+lB,EAASpe,GAChB,GAAAqe,GAAiB,oBAAsBD,EAAOE,sBAAWC,iBAAAF,GAAAD,iBAAAA,EAAAE,sBAAAE,cAAAH,GAAA,oBAAAD,EAAAE,sBAAAG,iBAAAJ,GAAAD,qBAAAA,EAAAE,sBAAAI,iBAAA,CACzD,IAGCpK,EAHQnc,MAASC,KAClBgmB,EAASE,WAAWrmB,UAEnBkG,QAAAigB,GACFpe,EAAA2e,QAAArK,QACI,GAAA8J,EAASQ,iBAAA,CACd,IACDtK,EADQnc,MAAAC,KAAAgmB,EAAAQ,iBAAA3mB,UACRkG,QAAAigB,GACDpe,EAAK2e,QAAOrK,EACV,CACA,OAAKtU,CACL,CACD6e,CAAAxmB,EAfY,GAeZ,CAEC,SAAAymB,GAAkB9a,EAAKpC,EAAemd,GACtC,IAAAvkB,EAAKwkB,EACL,OAAAhb,GACAA,EAAOib,UAAAzkB,EAAAoH,EAAArH,MAAAyJ,EAAAib,WACRD,EAAAD,EAAAxkB,MAAAyJ,GACD,CACEgb,UACAxkB,OALc,CAAA,CAOd,CAkME,SAAA0kB,GAA2BC,EAG9BC,GAAA,IACD9qB,EAAAC,EAAWqc,GAJoBjP,OAC5BC,EAAAoU,kBACDA,GAA0CmJ,EAG1CE,EAAS,KACqBA,EAAX,cAAjBD,EAAAjN,SAAkCvQ,EAAArH,MAAA6kB,GACzBxd,EAAKrH,MAAA2T,GAAApX,KAAAsoB,IAAA,IACZE,EAAa,cAAAF,EAAAjN,SAAA,OAAA7d,EAAA8qB,EAAAtB,kBAAA,EAAAxpB,EAAAsO,SAAAgO,OAAAA,EAAArc,OAAAA,EAAA6qB,EAAApS,oBAAAzY,EAAAA,EAAAupB,kBAAAlN,EAAAA,EAAA1d,WACbqsB,SAAWD,SAAAA,EAAAtrB,WAAA/B,OAAAuC,yBACV8qB,MADUA,OACVA,EAAAA,EAAAtrB,UACJ,2BACG,EACF,OAAO,OAAPqrB,IAAiC,IAArBA,GAAqBC,GAAAC,GAGpCttB,OAAAC,eAAAktB,EAAA,qBAAA,CACH3sB,aAAA8sB,EAAA9sB,aACID,WAAY+sB,EAAA/sB,WAChBiC,GAAAA,GACI,IAAA+qB,EACA,OAA0BA,OAA1BA,EAAaD,EAAa9qB,UAAA+qB,EAAAA,EAAA1pB,KAAA6E,KAC5B,EACEc,GAAAA,CAAAgkB,GACA,IAAKD,EACAE,EAAO,OAAAF,EAAAD,EAAA9jB,UAAA,EAAA+jB,EAAA1pB,KAAA6E,KAAA8kB,GACZ,UAAKJ,IAAO,IAAAA,EACZ,IACKrJ,EAAM2J,iBAAAF,EAAAJ,EACZ,CAAA,MAAA7d,GACD,CAEC,OAAAke,CACD,IAEIjF,IAAuB,KACrBxoB,OAAAC,eAAcktB,EAAgB,qBAAA,CAC/B3sB,aAAA8sB,EAAA9sB,aACFD,WAAA+sB,EAAA/sB,WAEDiC,IAAK8qB,EAAsB9qB,IAE5BgH,IAAA8jB,EAAA9jB,KACD,KA5BE,MA6ByD,CAmTrD,SAAAmkB,GAAYC,EAAAC,QAAA,IAAAA,IAAAA,EAAA,CAAA,GAAA,IAOb1oB,EANP2oB,EAAiBF,EAAA5lB,IAAW6jB,YACpB,IAAAiC,EAAQ,MAChB,QAnGI,SACAF,EAAAC,GAAA,IACA7H,WACJA,EAAA+H,YACAA,EAAAnE,mBACIA,EAAgB+B,SAChBA,EAAAqC,iBACFA,EAAYC,QACVA,EAAMC,mBACNA,EAAYC,iBACbA,EAAAC,mBACDA,EAAoBC,iBAClBA,EAAUC,OACVA,EAAAC,YACDA,EAAAC,gBACDA,GACEZ,EACAA,EAAA5H,WAAa,WACd6H,EAAAY,UACHZ,EAAAY,YAAAzR,WAEAgJ,KAAWhJ,UACX,EACA4Q,EAAIG,YAAgB,WAChBF,EAAAa,WACAb,EAAAa,aAAiB1R,WAEjB+Q,KAAM/Q,UACN,EACA4Q,EAAAhE,mBAAsB,WACvBiE,EAAA/D,kBACD+D,EAAU/D,oBAAe9M,WAEvB4M,KAAY5M,UACV,EACE4Q,EAAAjC,SAAe,WACbkC,EAAA9B,QAAwC8B,EAClD9B,UAAe/O,WACc2O,KACpB3O,UAAA,EAED4Q,EAAAI,iBAAyB,WACvBH,EAAAc,gBAAgCd,EACjCc,kBAAA3R,WAEJgR,KAAAhR,UACD,EACD4Q,EAAAK,QAAA,WACDJ,EAAYe,OACVf,EAAIe,SAAS5R,WAEXiR,KAAKjR,UAA+C,EAEtD4Q,EAAAM,mBAA8B,WAC/BL,EAAAgB,iBACDhB,EAASgB,mBAAW7R,WAElBkR,KAAYlR,UACb,EACH4Q,EAAAO,iBAAA,WACAN,EAASiB,gBACPjB,EAAAiB,kBAAiB9R,WAEnBmR,KAASnR,UACP,EACF4Q,EAAAQ,mBAAA,WACIP,EAASkB,kBACblB,EAASkB,oBAAU/R,WAEfoR,KAAWpR,UACb,EACE4Q,EAAAS,iBAA8B,WAC/BR,EAAAmB,gBACDnB,EAAMmB,kBAAQhS,WAEZqR,KAAgBrR,UACjB,EACD4Q,EAAAU,OAAc,WACRT,EAAAoB,MACJpB,EAAIoB,QAAAjS,WAEJsR,KAAItR,UACJ,EACA4Q,EAAAW,YAAgB,WACZV,EAAAqB,WACJrB,EAAAqB,aAASlS,WAELuR,KAAAvR,UAAS,EAET4Q,EAAAY,gBAAS,WACVX,EAAAsB,eACCtB,EAAIsB,iBAAWnS,WACHwR,KACXxR,UAAA,CAED,CAOAoS,CAAIxB,EAAAC,GAEND,EAAAyB,YACElqB,EAAI4jB,GAA2B6E,EAAQA,EAAA5lB,MAEvC,IAAAsnB,EAv5BK,SACRC,GAKC,IALDxB,YACDA,EAAIlE,SACFA,EAAA7hB,IAAoBA,EAEtB0H,OAAIC,GACF4f,EAAiD,IAClD,IAAA1F,EAAA6E,UACD,MAAO,OAGP,IAGAc,EAHIC,EAA4C,iBAAhC5F,EAAQ6E,UAAwB7E,EAAA6E,UAAA,GAC9CgB,EAAoB,iBAAA7F,EAAA8F,kBAAA9F,EAAA8F,kBAAA,IACrBC,EAAA,GAEGC,EAAOnT,GACT8L,IACDzK,IACG,IAAK+R,EAAW1S,KAAQD,MAAAqS,EAC1BzB,EACD6B,EAAAnT,KAAAsT,IACMA,EAAAC,YAAAF,EACRC,KAEKhS,GAEH6R,EAAA,GACDJ,EAAuB,IAAA,IAGvBE,GAECO,EAAAzH,GACD9L,GACE8L,IAAYoD,IACb,IAAAxP,EAAAwM,GAAAgD,IACMZ,QAAAA,EAAAC,QAAAA,GAAAnL,GAAA8L,GAAAA,EAAA5L,kBAAA4L,EACR4D,IACDA,EAAa/Q,MAETmR,EAAYvoB,KAAK,CACvB8jB,EAAAH,EACMnY,EAAOoY,EACb1iB,GAAWoH,EAAArH,MAAA8T,GACL4T,WAAOvR,KAAA+Q,IAEVK,EACU,oBAAAK,WAAAtE,aAAAsE,UAAA5O,GAAA6O,KAAAvE,aAAAwE,WAAA9O,GAAA+O,UAAA/O,GAAAgP,UACL,IAEJb,EACA,CACIhS,UAAQ,KAIXuM,GACD9N,GAAA,YAAW+T,EAAqBjoB,GAChCkU,GAAA,YAAK+T,EAAwBjoB,GAC7BkU,UAAO+T,EAAAjoB,IAET,OAAAwgB,IAAW,KACTwB,EAAK/gB,SAAKuiB,GAAcA,KAAA,GAEtB,CAu1BU+E,CAAA3C,GAChB4C,EAAa9G,GAAAkE,GACL6C,EAAWhF,GAAAmC,GACZ8C,EAxtBI,SACRC,EAAAC,GAAA,IAAA5C,iBAAAA,GAAA2C,GAAAxtB,IAAAA,GAAAytB,EACDC,GAAM,EACJC,GAAY,EAkBN,OAAA5U,GAAA,SAjBPsM,GACD9L,GACE8L,IAAoB,KAChB,IAAM1V,EAASqM,KACdvM,EAAY0M,KACbuR,IAAW/d,GAAKge,IAAAle,IACfob,EAAgB,CACdpb,MAAAsX,OAAAtX,GACRE,OAAAoX,OAAApX,KAEC+d,EAAO/d,EACDge,EAAOle,EACT,IACS,MAIazP,EAClB,CAksBL4tB,CAAAnD,EAAA,CACFzqB,IAAA2qB,IAEDkD,EAlsBc,SAAQC,GAW1B,IAX0BhD,QAA4CA,EAAAjmB,IAEpDA,EAAO0H,OAAAC,EACRC,WAAAA,EAAAC,cACFA,EACfqhB,YAAAA,EACAC,eAAAA,EACSrnB,iBACCA,EAAQG,YACNA,EAAA4f,SAAiBA,EAAAuH,qBACkCA,GAC/DH,EACA,SACAI,EAAmBtR,GACT,IAAA3D,EAAOwM,GAAY7I,GAC7BuR,EAAmBvR,EAASwR,UAClBxnB,EAAOqS,GAAYA,EAAKrS,QAIxB,GAHDqS,GAAmB,WAAnBrS,IACCqS,EAAAH,GAAOzX,cAAY4X,IAEnBA,GAAOrS,KAAUiiB,GAAA9f,QAAAnC,QAAA2V,GAAAtD,EAAAxM,EAAAC,GAAA,MAGrBuM,EAAIpN,UAAatK,SAAOwsB,IAAAC,GAAA/U,EAAA9M,QAAA6hB,IADvB,CAIC,IAAAjnB,EAAIkS,EAAS/b,MACXmxB,GAAM,EACPxnB,EAAAQ,EAAA4R,IAAA,GACM,UAAPpS,GAAO,aAAAA,EACRwnB,EAAApV,EAAAnK,SACPnI,EAAAC,EAAAK,gBAAAN,EAAAE,MACGE,EAAAP,EAAA,CACKE,QAAOuS,EACPtS,mBACAC,UACJC,OACD3J,MAAA6J,EACDD,iBAGEwnB,EACArV,EACAgV,EAAI,CAAAlnB,OAAAsnB,YAAAF,iBAAA,CAAApnB,OAAAsnB,cAEF,IAAA1jB,EAASsO,EAAKtO,KACV,UAAJ9D,GAAiB8D,GAAQ0jB,GACvBxpB,EAAA9C,iBAAe,6BAAmB4I,EAAA7E,MAAAA,SAAAsE,IACnC,GAAAA,IAAA6O,EAAA,CACF,IAAAsV,EAAAnkB,EAAAlN,MACIoxB,EACElkB,EACR6jB,EAAA,CAAAlnB,KAAAwnB,EAAAF,WAAAA,EAAAF,eAAA,GAAA,CAAApnB,KAAAwnB,EAAAF,WAAAA,GAEK,IA7BA,CAgCJ,CACA,SAAAC,EAAkBrV,EAAOuV,GACzB,IAAIC,EAAA3F,GAAAzpB,IAAA4Z,GACJ,IAAAwV,GAAoBA,EAAS1nB,OAAAynB,EAAAznB,MAAA0nB,EAAAJ,YAAAG,EAAAH,UAAA,CAC3BvF,GAAcziB,IAAQ4S,EAAEuV,GACxB,IAAIppB,EAAAoH,EAAcrH,MAAA8T,GAChBoM,GAAayF,EAAbzF,CAAmB0C,KACpByG,EAAA,CACFppB,OAED,CACD,CACD,IACMyhB,GADmB,SAAfH,EAAO+E,MAAQ,CAAA,UAAA,CAAA,QAAA,WACFnS,KACnB6N,GAAQpO,GAAAoO,EAAc9B,GAAiB6I,GAAKrpB,KAE5C8lB,EAAQ9lB,EAAA6jB,YAAA,IACTiC,EACC,MAAQ,KACR9D,EAAS/gB,SAAMuiB,GAAOA,KAAA,EAErB,IACPqG,EAAqB/D,EAAmB9tB,OAAKuC,yBACvCurB,EAAQgE,iBAAmB/vB,UAC3B,SAECgwB,EAAA,CACP,CAAAjE,EAAegE,iBAAY/vB,UACrB,SAAA,CAAA+rB,EAASgE,iBAAK/vB,UAAA,WACpB,CAAA+rB,EAAekE,kBAAYjwB,UACrB,SAAA,CAAA+rB,EAAWmE,oBAAgBlwB,UAAa,SACkB,CAAA+rB,EAC/CkE,kBAAajwB,UAAU,iBAChC,CAAA+rB,EAAMoE,kBAAqBnwB,UAAK,aAuBlC,OArBA8vB,GAAaA,EAAsBroB,KACzCwgB,EAAA3iB,QACM0qB,EAAatV,KACnBsT,GAAerS,GACTqS,EAAQ,GACdA,EACM,GAAA,CACKvmB,GAAAA,GACCgf,GAAU6I,EAAV7I,CAAU,CACjBpM,OAAA1T,KACG6oB,WAAkB,GAGhB,IAEA,EACFzD,MAKFtF,IAAO,KACbwB,EAAK/gB,SAAAuiB,GAAAA,KAAA,GACM,CA+kBA2G,CAAAvE,GACRwE,EA7PQ,SACAC,GAMJ,IANInE,mBAAAA,EACAte,WAAAA,EACAC,cACHA,EACAH,OAAAC,EAAAka,SACAA,EAAA7hB,IAAAA,GACDqqB,EAEC9H,EAAO/B,IACPxe,GAAK0S,GACL8L,IAAazI,IACX,IAAA3D,EAAOwM,GAAA7I,GACP,GAAG3D,IAAAsD,GAAAtD,EAAAxM,EAAAC,GAAA,GAAH,CAGE,IAAAqF,YAAIA,EAASQ,OAAIA,EAAAJ,MAAAA,EAAAF,aAAAA,EAAAI,KAAAA,GAAA4G,EACf8R,EAAI,CACFlkB,OACAzB,GAAAoH,EAAArH,MAAA8T,GAAAlH,cAEAQ,SAAkBJ,QACnBF,eAEHI,QATA,CAUA,IAEEqU,EAAAtiB,OAAU,OAGdyiB,EACA9N,CAAAA,GAAAqO,OAAAA,EAAMzI,GAAAwQ,MAAAtqB,GAAAkU,GAChB,QAAeqO,EAAAzI,GAAAyQ,OAAAvqB,GACLkU,GAAA,SAAOqO,EAAIzI,GAAoB0Q,QAAAxqB,GAC/BkU,GAAU,eAAAqO,EAAUzI,GAAa2Q,cAAAzqB,GACjCkU,GAAI,aAAAqO,EAAezI,GAAiB4Q,YAAY1qB,IACjB,OAAAwgB,IACxB,KACLwB,EAAA/gB,SAAAuiB,GAAgBA,KAAA,GACV,CAsNfmH,CAAA/E,GACHgF,EAAAA,OAEAC,EAAmBA,OAEfC,EAAWA,OAEbC,EAAYA,OAEVnF,EAAKyB,YACLuD,EA5jBA,SAAoBI,EAAAC,GAAS,IAAT9E,iBAASA,EAAAze,OAAAC,EAAAoU,kBAAAA,GAAAiP,GAAA7vB,IAAAA,GAAA8vB,EAC3B,IAAA9vB,EAAA+vB,gBAAsB/vB,EAAE+vB,cAAAnxB,UACxB,MAAI,OAEH,IACFoxB,EAAAhwB,EAAA+vB,cAAAnxB,UAAAoxB,WACDhwB,EAAA+vB,cAAcnxB,UAAAoxB,WAAA,IAAA9U,MAAA8U,EAAA,CACd3V,MAAOgL,IACR,CAAApM,EAAAgX,EAAAC,KACD,IAAAjtB,EAAcic,GAAegR,GACtB9qB,GAAAA,EAAAwkB,QAAUA,GAAAF,GACbuG,EACAzjB,EACDoU,EAAA+I,aASE,OAPGvkB,QAAKA,GAAewkB,IAA2B,IAArBA,IAC1BoB,EAAc,CACb5lB,KACNwkB,UACI1J,KAAS,CAAA,CAAAiQ,KAAAltB,EAAA6V,MAAAoG,MAGXjG,EAAWoB,MAAA4V,EAAAC,EAAA,MAIdlwB,EAAA+vB,cAAYnxB,UAAawxB,QAAO,SAAAC,EAAAC,EAAApR,QAAA,IAAAA,IAAAA,EAAA3Z,KAAA1C,SAAAgB,QAC9B,IAAIZ,EAAAotB,EAAA,MAAAC,EAAA,KACJ,OAAItwB,EAAA+vB,cAAAnxB,UAAAoxB,WAAA3V,MAAA9U,KAAA,CAAAtC,EAAAic,GACF,EAA4B,IAyB/B5b,EAwBCitB,EAhDCC,EAAYxwB,EAAA+vB,cAAAnxB,UAAA4xB,WACXxwB,EAAA+vB,cAAYnxB,UAAA4xB,WAAa,IAAAtV,MAAAsV,EAAA,CAAAnW,MAC1BgL,IACD,CAAApM,EAAIgX,EAAYC,KACd,IAAAhR,GAAgBgR,GACjB9qB,GAAAA,EAAAwkB,QAAAA,GAAAF,GACDuG,EACDzjB,EACFoU,EAAA+I,aASE,OAPIvkB,IAAUwkB,IAAVxkB,GAAUwkB,IAAA,IAAAA,IACboB,EAAW,CACX5lB,KACMwkB,UACFlI,QAAO,CAAA,CAAA5I,MAASoG,MAGrBjG,EAAAoB,MAAA4V,EAAAC,EAAA,MAIKlwB,EAAA+vB,cAAOnxB,UAAS6xB,oBAASvR,GAAA,OAC1Blf,EAAA+vB,cAAAnxB,UAAA4xB,WAAAnW,MAAA9U,KAAA,CAAA2Z,GAAA,EAGLlf,EAAA+vB,cAAkBnxB,UAAO0E,UACvBA,EAAItD,EAAM+vB,cAASnxB,UAAY0E,QAC7BtD,EAAA+vB,cAAOnxB,UAAgB0E,QAAE,IAAA4X,MAAA5X,EAAA,CAC1B+W,MAAAgL,IACF,CAAApM,EAAAgX,EAAAC,KACF,IAAAnpB,GAAAmpB,GACY9qB,GAAAA,EAAAwkB,QAAAA,GAAUF,GACduG,EACDzjB,EACFoU,EAAgB+I,aASd,OAPLvkB,IAAAwkB,IAAAxkB,GAAAwkB,IAAA,IAAAA,IACFoB,EAAA,CACS5lB,KACHwkB,UACHtmB,QAAWyD,IAGAkS,EAAAoB,MAAS4V,EAASC,EAAA,OAM3BlwB,EAAA+vB,cAAUnxB,UAAS2xB,cACjBA,EAAOvwB,EAAA+vB,cAAkBnxB,UAAA2xB,YAAAvwB,EAC1B+vB,cAAAnxB,UAAA2xB,YAAA,IAAArV,MAAAqV,EAAA,CACTlW,MAAOgL,IACF,CAAApM,EAAAgX,EAAAC,KACD,IAAYnpB,GAAMmpB,GACN9qB,GAAAA,EAAAwkB,QAAAA,GAASF,GACjBuG,EACDzjB,EACFoU,EAAA+I,aASE,OAPKvkB,IAAoBwkB,IAApBxkB,GAAoBwkB,IAAA,IAAAA,IACvBoB,EAAU,CACb5lB,KACOwkB,UACD2G,YAAexpB,IAGpBkS,EAAAoB,MAAA4V,EAAAC,EAAA,OAKG,IAAAQ,EAAyB,CAAA,EAAAC,GAC1B,mBAAAD,EACFvH,gBAAAnpB,EAAAmpB,iBAEHwH,GAAgC,kBAC9BD,EAAmBtH,aAAgBppB,EAAAopB,cAElCuH,GAAA,sBACFD,EAAApH,iBAAAtpB,EAAAspB,kBAECqH,GAAQ,qBACVD,EAAyBrH,gBAAOrpB,EAAAqpB,kBAGlC,IAAIuH,EAAO,CAAA,EAkEV,OAjEC/zB,OAAKg0B,QAAKH,GAAsB5qB,SAAAgrB,IAAA,IAAAC,EAAAlqB,GAAAiqB,EAChCF,EAAoBG,GAAW,CAEnCf,WAAAnpB,EAAAjI,UAAAoxB,WAEEQ,WAAY3pB,EAAAjI,UAAA4xB,YAEd3pB,EAAAjI,UAAcoxB,WAAgB,IAAA9U,MAC5B0V,EAAWG,GAAAf,WACb,CACA3V,MAAcgL,IACZ,CAAApM,EAAagX,EAAAC,KACf,IAAAjtB,EAAAic,GAAAgR,GACc9qB,GAAAA,EAAAwkB,QAAAA,GAAgBF,GACjBuG,EAAAzG,iBACbhd,EACIoU,EAAc+I,aAkBb,OAhBSvkB,IAAqBwkB,IAArBxkB,GAAqBwkB,IAAA,IAAAA,IACvBoB,EAAmB,CACpB5lB,KACEwkB,UACF1J,KACE,CAAA,CACFiQ,KAAeltB,EACP6V,UACRiQ,GAAkCkH,GAC1B/Q,GAAS,OAOvBjG,EAAAoB,MAAA4V,EAAAC,EAAA,MAKDrpB,EAAAjI,UAAe4xB,WAAMtV,IAAAA,MACvB0V,EAAsBG,GAAAP,WACpB,CACInW,MAAMgL,IACR,CAAKpM,EAAQgX,EAAAC,KACd,IAAAhR,GAAAgR,GACF9qB,GAAAA,EAAAwkB,QAAAA,GAAAF,GACQuG,EAAWzG,iBACdhd,EACGoU,EAAK+I,aAWZ,OATJvkB,IAAAwkB,IAAAxkB,GAAAwkB,IAAA,IAAAA,IACaoB,EAAA,CACX5lB,KACFwkB,UACalI,QACK,CAAA,CAAA5I,MAAAiQ,IAAAA,GAAAkH,GAAA/Q,OAIFjG,EAAAoB,MAAA4V,EAAAC,EAAA,KAId,IAEC7K,IAAA,KACHrlB,EAAA+vB,cAAAnxB,UAAAoxB,WAAAA,EACIhwB,EAAA+vB,cAAcnxB,UAAc4xB,WAAAA,EAC9BltB,IAAYtD,EAAM+vB,cAAWnxB,UAAA0E,QAAAA,GAC3BitB,IAAYvwB,EAAA+vB,cAAAnxB,UAAA2xB,YAAAA,GACZ1zB,OAAKg0B,QAAOH,GAAA5qB,SAAAkrB,IAAA,IAAAD,EAAAlqB,GAAAmqB,EACZnqB,EAAIjI,UAAaoxB,WAAUY,EAAQG,GAAAf,WACjCnpB,EAAIjI,UAAQ4xB,WAAUI,EAAYG,GAAAP,UAAA,GAClC,GAEA,CA4XeS,CAAaxG,EAAA,CAAAzqB,IAAA2qB,IAC/B+E,EAAA5F,GAAAW,EAAAA,EAAA5lB,KACD8qB,EAhVC,SACauB,EAAAC,GAKL,IALKlG,mBACZA,EACD1e,OAAAC,EAAA4kB,oBACHA,EAAAxQ,kBACAA,GACAsQ,GAAAlxB,IAAAA,GAAAmxB,EACME,EAAArxB,EAAiBsxB,oBAAgB1yB,UAAAyyB,YACvCrxB,EAAMsxB,oBAAiB1yB,UAAIyyB,YAAY,IAAAnW,MAAAmW,EAAA,CACvChX,MAAMgL,IACA,CAAApM,EAAAgX,EAAcC,KACd,IAAAhxB,GACAqW,EAAcrY,EAAYq0B,GAAArB,EAC1B,GAAAkB,EAA0BprB,IAAAuP,GAC1B,OAAQ8b,EAAehX,MAAC4V,GAAA1a,EAAArY,EAAAq0B,IAExB,IAAAnsB,GAAAA,EAAAwkB,QAAAA,GAAoBF,GACa,OAAjCxqB,EAAA+wB,EAAiB/G,iBAAgBhqB,EAAAA,EAAAsqB,iBACjChd,EACAoU,EAAsB+I,aAetB,OAbAvkB,IAAoB,IAApBA,GAAoBwkB,IAAY,IAAAA,IAChCqB,EAAkB,CAClB7lB,KACAwkB,UACAvjB,IAAW,CACXkP,WACArY,QACAq0B,YAGMzY,MAAOiQ,GAAWkH,EAAA/G,cAGfjQ,EAAAoB,MAAW4V,EAAUC,EAAA,MAIlC,IAAIsB,EAAWxxB,EAAAsxB,oBAAA1yB,UAAA4yB,eA4BT,OA3BNxxB,EAAAsxB,oBAAoB1yB,UAAA4yB,eAAA,IAAAtW,MAAAsW,EAAA,CAClBnX,MAAOgL,IACR,CAAApM,EAAAgX,EAAAC,KACD,IAAShxB,GACDqW,GAAa2a,EACpB,GAAAkB,EAAAprB,IAAAuP,GACD,OAASic,EAAYnX,MAAA4V,GAAA1a,IAEpB,IAAAnQ,GAAAA,EAAAwkB,QAAAA,GAAAF,GACwBxqB,OAAhBA,EAAA+wB,EAAU/G,iBAAMhqB,EAAAA,EAAAsqB,iBACnBhd,EACAoU,EAAe+I,aAad,OAXEvkB,IAAe,IAAXA,GAAcwkB,IAAA,IAAAA,IACzBqB,EAAY,CACL7lB,KACAwkB,UACA6H,OAAA,CACAlc,YAGAuD,MAAAiQ,GAAAkH,EAAA/G,cAGFjQ,EAAQoB,MAAS4V,EAAWC,EAAS,MAIvC7K,IAAA,KAAArlB,EACDsxB,oBAAK1yB,UAAAyyB,YAAAA,EAAArxB,EACLsxB,oBAAK1yB,UAAA4yB,eAAAA,CAAA,GACA,CA0QOE,CAAAjH,EAAA,CACdzqB,IAAO2qB,IAELF,EAAAkH,eACF/B,EArOO,SACFgC,GAAA,IAAAzG,OAAAA,EAAAtmB,IAAAA,GAAA+sB,EACD5xB,EAAA6E,EAAA6jB,YAAA,IACD1oB,EAAA,MACI,OAGH,IAAA6mB,EAAO,GACPgL,MAAG3sB,QACD4sB,EAAU9xB,EAAA+xB,SACV/xB,EAAA+xB,SAAO,SAAmBC,EAAQpX,EAAAqX,GAClC,IAAAC,MAAaJ,EAAIE,EAAApX,EAAAqX,GAMd,OALDJ,EAAAxrB,IAAI6rB,EAAU,CACZF,SACA9hB,OAAA,iBAAA0K,EAAAqX,cAEAE,WAAiBvX,iBAARA,EAAQA,EAAA7W,KAAAC,UAAAjB,MAAAC,KAAAovB,IAAAA,WAAAxX,OAEpBsX,CACD,EACA,IAAAG,EAAW1X,GACT9V,EAAAytB,MACA,OAAW,SACZ5X,GAAA,OACF,SAAQwX,GAWP,OAVFvb,WACA0O,IAAM,KACN,IAAAuH,EAAAiF,EAAAxyB,IAAA6yB,GACDtF,IACIzB,EAAMyB,GACTiF,EAAYhsB,OAAAqsB,GACZ,IAEE,GAEAxX,EAAOL,MAAY9U,KAAA,CAAA2sB,GAAY,KAQjC,OAJArL,EAAA3iB,MAAA,KAAAlE,EACD+xB,SAAAD,CAAA,IAECjL,EAAA3iB,KAAOmuB,GACPhN,IAAS,KACTwB,EAAA/gB,SAAWuiB,GAAAA,KAAW,GAEpB,CAuLWkK,CAAgB9H,KAGlC,IAAA+H,EA1LiB,SACXC,GACD,IAAA5tB,IAAAA,EAAO0H,OAAIC,EAAWC,WAAAA,EAAQC,cAAAA,EAAA0e,YAAAA,GAAAqH,EAC9BC,GAAc,EACZC,EAAQtN,IAAA,KACR,IAAA0G,EAAIlnB,EAAA+tB,eACF,MAAA7G,GAAO2G,UAAgB3G,OAAyB,EAAGA,EAAA8G,cAAnD,CACEH,EAAA3G,EAAQ8G,cAAA,EAGR,IAHQ,IAAAC,EACT,GACDC,EAAIhH,EAAIiH,YAAmB,EACzB3kB,EAAAA,EAAAA,EAAQ0kB,EAAA1kB,IAAA,CAAA,IACT4kB,EAAAlH,EAAAmH,WAAA7kB,IACF8kB,eAAAA,EAAAC,YAAAA,EAAAC,aAAAA,EAAAC,UAAAA,GAAAL,EACF1W,GAAA4W,EAAA1mB,EAAAC,GAAA,IAAA6P,GAAA8W,EAAA5mB,EAAAC,GAAA,IAEDomB,EAAA5uB,KAAM,CACNqvB,MAAA/mB,EAAArH,MAAAguB,GACDC,cACDI,IAAAhnB,EAASrH,MAAAkuB,GACPC,aAEE,CACElI,EAAI,CAAA0H,UAhB+C,CAgBrC,IAGZ,OAFWH,IAEX5Z,GAAS,kBAAA4Z,EAAS,CAiK7Bc,CAAAhJ,GACHiJ,EAjKa,SACFC,GAEK,IAFL9uB,IACDA,EAAAwmB,gBACAA,GAAMsI,EAEN3zB,EAAA6E,EAAA6jB,YACA,OAAA1oB,GAAAA,EAAAmT,eAEawH,GAAS3a,EAAAmT,eAEpB,UAAiC,SAClCuH,GACD,OAAA,SAAA/P,EAAgB1I,EAAYqK,GAC5B,IACA+e,EAAM,CACPuI,OAAA,CACDjpB,SAGJ,CAAA,MAAAyB,GACAoF,QAAOC,KAAA,sCAAA9G,EACR,CACD,OAAS+P,EAAYL,MAAA9U,KAAA,CAAAoF,EAAA1I,EAAAqK,GACnB,KAhBM,MAmBN,CAuIJunB,CAAApJ,GACIqJ,EAAS,GACb,IAAA,IAASC,KAAUtJ,EAAAuJ,QACnBF,EAAc5vB,KACV6vB,EAAAhO,SAAkBgO,EAAAE,SAAAtJ,EAAAoJ,EAAAznB,UAGtB,OAAI+Y,IAAa,KACbG,GAAW1f,SAAAouB,GAAAA,EAAA3tB,UACA,MAAXvE,GAAWA,EAAAmyB,aACfhI,IACEkB,IACAC,IACFC,IACAM,IACEoB,IACEQ,IACAC,IACAC,IACDC,IACH4C,IACIkB,IACFI,EAAYhuB,SAAQuiB,GAAAA,KAAA,GAElB,CACA,SAAKY,GAAe7N,GACpB,YAAc,IAATvT,OAASuT,EACd,CACA,SAAKuV,GAAevV,GACpB,OAAKrc,aACN,IAAA8I,OAAAuT,IAECvT,OAAIuT,GAAQxc,0BAAIiJ,OAAAuT,GAAAxc,WAAAiJ,eAAAA,OAAAuT,GAAAxc,UAEhB,CACE,MAAAw1B,GAA+BnyB,WAAAA,CAChCoyB,GACDt3B,EAAUwI,KAAe,wBAAA,IAAAL,SACzBnI,mCAAImI,SACJK,KAAI8uB,aAAAA,CACJ,CACAlvB,KAAAA,CAAI6b,EAAOsT,EAAAC,EAAAC,GACX,IAAIC,EAAOF,GAAAG,KAAAA,mBAAA1T,GACP2T,EAASH,GAAAI,KAAAA,mBAAA5T,GACT5b,EAAAqvB,EAAWp1B,IAAAi1B,GAK2B,OAJ1ClvB,IACEA,EAAAG,KAAQ8uB,eACRI,EAAcpuB,IAAAiuB,EAAAlvB,GACduvB,EAAatuB,IAAOjB,EAAAkvB,IAEnBlvB,CACC,CAAiBK,MAAAA,CACzBub,EAAiBsT,GACT,IAAAG,EAAYlvB,KAAAmvB,mBAAA1T,GACb2T,EAAApvB,KAAAqvB,mBAAA5T,GACD,OAAIsT,EAAShb,KACXlU,GAAIG,KAAAJ,MAAS6b,EAAK5b,EAAAqvB,EAAAE,IAEhB,CACAE,WAAAA,CAAA7T,EAAK5b,EAAAkU,GACL,IAAAqb,EAAArb,GAAAsb,KAAAA,mBAAA5T,GAAA,GACS,iBAAnB5b,SAAiCA,EACvB,IAAAkvB,EAAOK,EAAAt1B,IAAA+F,GACP,OAAAkvB,IAAA,CAEA,CACEQ,YAAAA,CAAA9T,EAAQ+T,GACR,IAAAJ,EAAcpvB,KAAKqvB,mBAAA5T,GACnB,OAAA+T,EAAAzb,KAAOlU,GAAQG,KAAKsvB,YAAO7T,EAAS5b,EAAAuvB,IAClC,CAAqBpuB,KAAAA,CAAAya,GAEvB,IAAAA,EAEmB,OADjBzb,KAAAyvB,sBAAoC,IAAK9vB,aACzCK,KAAA0vB,0BAAiB/vB,SAGrBK,KAAAyvB,sBAAcnvB,OAAAmb,GACdzb,KAAA0vB,sBAAApvB,OAAAmb,EAAA,CAEA0T,kBAAAA,CAAO1T,GAAU,IAClByT,EAAAO,KAAAA,sBAAA31B,IAAA2hB,GAKD,OAJRyT,IACQA,EAAiB,IAAAxvB,IAClBM,KAAAyvB,sBAAA3uB,IAAA2a,EAAAyT,IAECA,CACA,CAAAG,kBAAAA,CACD5T,GACF,IAAA2T,EAAAM,KAAAA,sBAAA51B,IAAA2hB,GAKC,OAJF2T,IACAA,MAAmB1vB,IACjBM,KAAA0vB,sBAAuB5uB,IAAK2a,EAAA2T,IAExBA,CACF,EAEA,MAAAO,GACAjzB,WAAAA,CAAKqK,GACLvP,EAAMwI,KAAe,UAAA,IAAAL,SAAAnI,kCACtBmI,SACPnI,EAAWwI,KAAA,0BAAA,IAAA6uB,GAAAzqB,IACL5M,EAAuBwI,KAAA,gCACvBxI,EAAMwI,sCAASL,SAChBnI,EAAAwI,KAAA,UACDxI,EAAUwI,KAAA,cACRxI,EAAcwI,KAAA,eACdxI,EAAewI,KAAA,gBAChBxI,EAAAwI,KAAA,qBACFxI,EAAAwI,KAAA,4BACDA,KAAAsd,WAAAvW,EAAqBuW,WACnBtd,KAAI4vB,YAAa7oB,EAAM6oB,YACvB5vB,KAAIqb,kBAAiBtU,EAAAsU,kBACrBrb,KAAI6vB,yBAAU9oB,EAAA8oB,yBACd7vB,KAAI8vB,6BAAA,IAAAjB,GACJ7uB,KAAAqb,kBAAwB+I,YAAalM,WAAA1c,KACnCwE,KAAAqb,kBAAgB+I,cAGdpkB,KAAAgH,OAAID,EAAeC,OACpBhH,KAAA6vB,0BACFvtB,OAAA0J,iBAAA+jB,UAAAA,KAAAA,cAAAv0B,KAAAwE,MACgB,CACfob,SAAAA,CACMhhB,GACZ4F,KAAAgwB,QAAAlvB,IAAA1G,GAAA,GACGA,EAAAM,eACDsF,KAAMiwB,qBAAQnvB,IAAA1G,EAAAM,cAAAN,EACZ,CACA81B,eAAAA,CAAWnQ,GACX/f,KAAAmwB,aAAcpQ,CACZ,CACApE,YAAAA,CAAOvhB,EAAOshB,GACd,IAAA/hB,EAAIC,EACFoG,KAAAsd,WAAY,CACb3C,KACD,CAAA,CACER,SAAYna,KAAAgH,OAAApH,MAAAxF,GACb4gB,OAAA,KACGc,KAAAJ,IAEsBS,QAChC,GACUQ,MAAA,GAAA/T,WACK,GACLwnB,gBAAO,IAEVpwB,KAAA6vB,2BACM,OAAPl2B,EAAOS,EAAAM,gBAAAf,EAAAqS,iBACR,UACDhM,KAAO+vB,cAAAv0B,KACRwE,eACDpG,EAAQoG,KAAAmwB,eAAOv2B,EAAAuB,KAAA6E,KAAA5F,GACTA,EAAQsT,iBAAItT,EAAAsT,gBAAA2iB,oBAAAj2B,EAAAsT,gBAAA2iB,mBAAA/xB,OAChB,GAAA0B,KAAKqb,kBAAoB2J,iBACzB5qB,EAAasT,gBAAW2iB,mBACxBrwB,KAAMgH,OAAOpH,MAAIxF,EAAAsT,iBAEjB,CACEqiB,aAAAA,CAAMO,GACN,IAAAC,EAAkBD,EAClB,GAAmB,UAAnBC,EAAmB7lB,KAAApJ,MACzBivB,EAAWxtB,SAAAwtB,EAAA7lB,KAAA3H,QAEeutB,EAAAjb,OACpB,CACA,IAAAjb,OAAmB61B,qBAAOn2B,IAAAw2B,EAAAjb,QAC3B,GAAAjb,EAAA,CACF,IAAAo2B,EAAAC,KAAAA,0BACDr2B,EACEm2B,EAAiB7lB,KAAa2M,OAE3BmZ,GACHxwB,KAAI4vB,YACJY,EACID,EAAqB7lB,KAAAgmB,WARxB,CAFmB,CAYlB,CACAD,yBAAAA,CAAUr2B,EAAAyM,GACX,IAAAlN,EACD,OAAMkN,EAAAvF,MACJ,KAAKoX,GAAMiY,aACjB3wB,KAAA4wB,wBAAA5vB,MAAA5G,GACI4F,KAAM8vB,6BAAW9uB,MAAA5G,GACjB4F,KAAO6wB,gBAAiBhqB,EAAA6D,KAAQoR,KAAA1hB,GAC9B,IAAIwN,EAAOf,EAAA6D,KAAcoR,KAAKjc,GAGhC,OAFEG,KAAM8wB,2BAA2BhwB,IAAI1G,EAAAwN,GACtC5H,KAAA+wB,kBAAAlqB,EAAA6D,KAAAoR,KAAAlU,GACK,CACAopB,UAAOnqB,EAAAmqB,UACb1vB,KAAOoX,GAAeuY,oBAChBvmB,KAAA,CACA2K,OAASuD,GAAgBsY,SAC3BvW,KACD,CAAA,CACKR,cAAenT,OAASpH,MAAAxF,GAC/B4gB,OAAA,KACUc,KAAAjV,EAAU6D,KAAAoR,OAGnBK,QAAe,GACXQ,MAAO,GACT/T,WAAW,GACXwnB,gBAAA,IAG0B,KACzB1X,GAAAyY,KACD,KAAAzY,GAAW0Y,KACZ,KAAA1Y,GAAA2Y,iBACF,OAAA,EAEC,KAAA3Y,GAAW4Y,OACX,OAAMzqB,EAER,KAAI6R,GAAA6Y,OAMH,OALGvxB,KAAAwxB,WACJ3qB,EAAO6D,KAAOgS,QACZtiB,EACI,CAAS,KAAA,WAAA,wBAEdyM,EAED,KAAA6R,GAAcuY,oBACZ,OAAQpqB,EAAA6D,KAAS2K,QACb,KAAAuD,GAAoBsY,SAoBrB,OAnBDrqB,EAAM6D,KAAAiQ,KAAApa,SAAY3E,IACdoE,KAAAwxB,WAAc51B,EAAAxB,GAClB,WACI,SACJ,eAEI4F,KAAA6wB,gBAAsBj1B,EAAAkgB,KAAA1hB,GACtB,IAAMwN,EAAAkpB,KAAAA,2BAAAh3B,IAAAM,GACVwN,GAAa5H,KAAQ+wB,kBAAQn1B,EAAAkgB,KAAAlU,EAAA,IAE3Bf,EAAI6D,KAAIyR,QAAO5b,SAAW3E,IACxBoE,KAAAwxB,WAAA51B,EAAAxB,EAAA,CAAA,WAAA,MAAA,IAEFyM,EAAA6D,KAAM9B,WAAarI,SAAI3E,IACxBoE,KAAAwxB,WAAA51B,EAAAxB,EAAA,CAAA,MAAA,IAECyM,EAAA6D,KAAMiS,MAAApc,SAAY3E,IAClBoE,KAAMwxB,WAAK51B,EAAYxB,EAAA,CAAA,MAAA,IAExByM,EAEC,KAAA+R,GAAa6O,KACf,KAAA7O,GAAAgP,UACD,KAAAhP,GAAA+O,UAIK,OAHP9gB,EAAA6D,KAAAwc,UAAA3mB,SAAA8mB,IACGrnB,KAAUwxB,WAAYnK,EAACjtB,EAAW,CAAM,MAAA,IAE/ByM,EAEZ,KAAA+R,GAAA6Y,eACI,OAAI,EAEP,KAAK7Y,GAAqB8Y,iBAC3B,KAAA9Y,GAAA+Y,iBACF,KAAA/Y,GAAAgZ,OACD,KAAYhZ,GAAOiZ,eACX,KAAKjZ,GAAMkZ,MAEb,OADF9xB,KAAAwxB,WAAA3qB,EAAA6D,KAAAtQ,EAAA,CAAA,OACUyM,EAEhB,KAAA+R,GAAAmZ,eACG,KAAAnZ,GAAAoZ,iBAGM,OAFGhyB,KAAAwxB,WAAO3qB,EAAA6D,KAAAtQ,EAAA,CAAA,OACX4F,KAAQiyB,gBAAIprB,EAAA6D,KAAAtQ,EAAA,CAAA,YACNyM,EAEJ,KAAK+R,GAAUsZ,KAChB,OAAUrrB,EAEb,KAAO+R,GAAAuZ,UAIJ,OAHDtrB,EAAK6D,KAAA6iB,OAAQhtB,SAAcmtB,IACxB1tB,KAAQwxB,WAAK9D,EAAYtzB,EAAK,CAAA,QAAA,OAAA,IAEhCyM,EAEA,KAAA+R,GAASwZ,kBAMP,OALHpyB,KAAKwxB,WAAQ3qB,EAAQ6D,KAAAtQ,EAAA,CAAA,OAClB4F,KAAAiyB,gBAAqBprB,EAAA6D,KAAKtQ,EAAoB,CAAA,aAC3B,OAAnBT,EAAQkN,EAAA6D,KAAO2nB,SAAI14B,EAAA4G,SAAAwc,IACnB/c,KAAAiyB,gBAAuBlV,EAAA3iB,EAAA,CAAA,WAAA,IAEvByM,GAKP,OAAI,CACF,CAAmC9I,OAAAA,CACpCu0B,EAAA76B,EAAA2C,EAAA+F,GACD,IAAK,IAAAzI,KAAayI,GACb3C,MAAK+0B,QAAO96B,EAAMC,sBAAiBD,EAAKC,MAC9C8F,MAAA+0B,QAAA96B,EAAAC,IACDD,EAAAC,GAAc46B,EAAOpyB,OACd9F,EACD3C,EAAKC,IAGLD,EAAAC,GAAU46B,EAAA1yB,MAAoBxF,EAAA3C,EAAAC,KAGjC,OAAAD,CACF,CAAA+5B,UAAAA,CAAA/5B,EAAA2C,EAAA+F,GAED,OAAYH,KAAAjC,QAAQ6yB,KAAAA,wBAAAn5B,EAAA2C,EAAA+F,EAClB,CACA8xB,eAAAA,CAAOx6B,EAAA2C,EAAA+F,GAAA,OACLH,KAAQjC,QAAI+xB,KAAAA,6BAAAr4B,EAAA2C,EAAA+F,EAAA,CACF0wB,eAAAA,CACVlwB,EAAAvG,GACN4F,KAAAwxB,WAAA7wB,EAAAvG,EAAA,CAAA,KAAA,WACG,eAAAuG,GACDA,EAAKhF,WAAe4E,SAAAiyB,IAClBxyB,KAAK6wB,gBAAkB2B,EAAAp4B,EAAA,GAET,CACkB22B,iBAAAA,CACpCpwB,EAAAiH,GACIjH,EAAMW,OAAKnJ,EAAc8P,UAAAtH,EAAAiH,SAAAjH,EAAAiH,OAAAA,GACzB,eAAcjH,GACdA,EAAIhF,WAAe4E,SAAAiyB,IACpBxyB,KAAA+wB,kBAAAyB,EAAA5qB,EAAA,GAGC,EAEA,MAAA6qB,GACA/1B,WAAAA,CAAIqK,GACJvP,EAAIwI,KAAA,aAAyC,IAAA0yB,SAC7Cl7B,EAAawI,KAAA,cACbxI,EAAYwI,KAAA,YACZxI,EAAcwI,KAAA,iBACZxI,EAAcwI,KAAA,UACdxI,EAAYwI,KAAK,kBAAA,IACjBA,KAAAsd,WAAavW,EAAOuW,WAClBtd,KAAAijB,SAAKlc,EAASkc,SACdjjB,KAAA4Q,cAAc7J,EAAS6J,cAAe5Q,KACvCgH,OAAUD,EAAAC,OACThH,KAAA+e,MACA,CAAiBA,IAAAA,GAEjB/e,KAAAgB,QACEhB,KAAA2yB,kBAAWn6B,QAAA6B,SACT,CACAmhB,aAAAA,CAAAve,EAAAqC,GAAA,GAAAtC,EACKC,KACL+C,KAAA4yB,WAAAnyB,IAAAxD,GAAA,CAAA+C,KAAA4yB,WACDhyB,IAAA3D,GAAA,IACXujB,EAAmBH,GAAcmC,EAAA,CAAA,EAEvBxiB,KAAA4Q,cAAA,CACVtR,MACUge,gBAAeA,WACftW,OAAMhH,KAAAgH,OACNuU,iBAAAvb,OAEA/C,GACD+C,KACT6yB,gBAAiBl0B,UAAkB6hB,EAASoO,eACpC5uB,KAAA6yB,gBAAYl0B,KACZokB,GAAaP,EACd,CAAA,EAAAxiB,KAAA4Q,cAAA,CACDqS,SAAQjjB,KAAKijB,SAGX3jB,IAAArC,EACA+J,OAAOhH,KAAAgH,WAGLoK,YAAQ,KACRnU,EAAIozB,oBAAqBpzB,EAAqBozB,mBAAA/xB,OAAA,GAC9C0B,KAAA4Q,cAAeyK,kBAAiB2J,iBACjC/nB,EAAAozB,mBACFrwB,KAAAgH,OAAApH,MAAA2T,GAAApX,KAAAc,KAEP+C,KAAW6yB,gBAAAl0B,KACL4lB,GACD,CACFvd,YAAAA,OACOqU,kBAAArb,KAAA4Q,cAAAyK,mBAENpe,GAEE,GACE,EAtCI,CAuCF,CAIA2e,mBAAAA,CAAAkX,GACFA,EAAKp4B,eAAAo4B,EAAAplB,iBACH1N,KAAA2yB,kBACAG,EAAAp4B,cAAAlC,QAAAs6B,EACGplB,gBAEH,CAGAilB,iBAAAA,CACGxxB,EAAA7B,GACH,IAAAyzB,EAAK/yB,KACLA,KAAA6yB,gBAAAl0B,KAAAyW,GAEAjU,EAAK9H,UACL,gBACH,SAAA8b,GACF,OAAA,SAAA6d,GACI,IAAOC,EAAA9d,EAAAha,UAAA63B,GACbviB,EAAA8C,GAAAlX,WAAA2D,MAGG,OAFJyQ,GAA0BgI,GACzBsa,OAAAA,EAAAvX,cAAA/K,EAAAnR,GACgB2zB,CACX,CACA,IAGJ,CACAjyB,KAAAA,GACEhB,KAAA6yB,gBAAiBtyB,SAAAshB,IACjB,IACAA,GACE,CAAA,MAAAhb,GACR,KAEQ7G,KAAA6yB,gBAAmB,GACnB7yB,KAAA4yB,WAA6B,IAAIF,OAC/B,EAGkB,IAFR,IAAAQ,GACpB,mEACYC,GAAgB,oBAAhBtG,WAAgB,GAAA,IAAAA,WAAA,KAAAuG,KACjBA,GAAAF,GAAAE,KAAAD,GACXD,GAAeG,WAAAD,KAAAA,GACG,IAgBdE,GAA0B,IAAA5zB,IAY1B,IAAA6zB,GAAOA,CAAA57B,EAAA8C,EAAAsP,KACR,GAAApS,IAAA67B,GAAA77B,EAAA8C,IAAA,iBAAA9C,GAAA,CAED,IACM87B,EAfJ,SAAgB1pB,EAAY2pB,GAC5B,IAAAC,EAAeL,GAAAx5B,IAAAiQ,GAQb,OAPH4pB,IACDA,EAAyB,IAAQj0B,IAC/B4zB,GAAIxyB,IAAAiJ,EAAA4pB,IAEJA,EAAclzB,IAAAizB,IACZC,EAAA7yB,IAAA4yB,EAAgB,IAEhBC,EAAS75B,IAAO45B,EAAW,CAMzBE,CAAA7pB,EADNpS,EAAA+E,YAA2B0I,MAEzBuU,EAAI8Z,EAASjwB,QAAA7L,GAIe,OAHd,IAAdgiB,IACEA,EAAO8Z,EAAAn1B,OACPm1B,EAAI90B,KAAAhH,IAELgiB,CATF,CASE,EACM,SACRka,GAAAl8B,EAAA8C,EAAAsP,GACD,GAAApS,aAAc6F,MACZ,OAAI7F,EAAAoc,KAAA+f,GAAAD,GAAAC,EAAAr5B,EAAAsP,KACA,GAAS,OAATpS,EACJ,OAAOA,EACL,GAAAA,aAAuBo8B,cAAiBp8B,aAAGq8B,cAAAr8B,aAAAs8B,YAAAt8B,aAAA4S,aAAA5S,aAAAk1B,YAAAl1B,aAAAu8B,aAAAv8B,aAAAw8B,YAAAx8B,aAAAy8B,WAAAz8B,aAAA08B,kBAE3C,MAAA,CACDC,QAFK38B,EAAA+E,YAAkB0I,KAGxBmP,KAAO,CAAAjd,OAAAi9B,OAAA58B,KAET,GAIGA,aAAA68B,YAIH,MAAA,CACEF,QAHO38B,EAAA+E,YAAA0I,KAIPqvB,OA5DKC,SAAAC,GAAA,IACI7rB,EAAb8rB,EAAa,IAAA/H,WAAA8H,GAAA9rB,EAAA+rB,EAAAt2B,OAAAm2B,EAAA,GACL,IAAA3rB,EAAAA,EAAAA,EAASD,EAAOC,GAAA,EAAA2rB,GACjBvB,GAAA0B,EAAA9rB,IAAA,GACF2rB,GAAAvB,IAAA,EAAA0B,EAAA9rB,KAAA,EAAA8rB,EAAA9rB,EAAA,IAAA,GACD2rB,GAAKvB,IAAO,GAAA0B,EAAA9rB,EAAA,KAAA,EAAA8rB,EAAA9rB,EAAA,IAAA,GACV2rB,GAAIvB,GAA0B,GAApB0B,EAAO9rB,EAAQ,IAO3B,OALCD,EAAA,GAAA,EACD4rB,EAAMA,EAAQ/uB,UAAA+uB,EAAAA,EAAAn2B,OAAA,GAAA,IACfuK,EAAA,GAAA,IACD4rB,EAAKA,EAAQ/uB,UAAA+uB,EAAAA,EAAAn2B,OAAA,GAAA,MAEPm2B,CACJ,CA2CDC,CAAA/8B,IAKDA,GAAAA,aAAyBk9B,SACN,MACf,CACAP,QAFS38B,EAAM+E,YAAA0I,KAGfmP,MACNsf,GAAAl8B,EAAAgT,OAAAlQ,EAAAsP,GACGpS,EAAAm9B,WACDn9B,EAAAo9B,aAGM,GAAAp9B,aAAkBq9B,iBAAA,CAAA,IAClB5vB,EAAQzN,EAAO+E,YAAO0I,MAC9BqI,IAAAA,GAAA9V,EACG,MAAA,CACD28B,QAAYlvB,EACVqI,OAEI,GAAA9V,aAAsBs9B,kBAAA,CAG3B,MAAA,CACDX,QAHc,mBAIZ7mB,IAHJ9V,EAAAqT,aAKQ,OAAArT,aAAkBu9B,UAE1B,CACGZ,QAFa38B,EAAO+E,YAAW0I,KAGlCmP,MAAAsf,GAAAl8B,EAAA+S,KAAAjQ,EAAAsP,GAAApS,EAAAuS,MAAAvS,EAAAyS,SAEIopB,GAAgB77B,EAAA8C,IAAA,iBAAA9C,EAGX,CACH28B,QAHU38B,EAAA+E,YAAA0I,KAIVmO,MAHUggB,GAAA57B,EAAA8C,EAAAsP,IAMbpS,CACC,CACE,IAAAw9B,GAAgBA,CAAA5gB,EAAA9Z,EAAAsP,IACdwK,EAAIR,KAAA+f,GAAWD,GAAiBC,EAAAr5B,EAAAsP,KAChBypB,GACKA,CAAA77B,EAAU8C,KAC7B,IAcN26B,EAdoB,CAAA,kBAEd,cAAc,mBACf,eAEJ,oBACD,cACD,6BACD,eACF,uBACI,yBAEJ,6BAEuBvY,QACnBzX,GACJ,mBADoB3K,EAAA2K,KAEpB,OAAI5L,QACA47B,EAAe71B,MACf6F,GAAAzN,aAAa8C,EAAA2K,KAEjB,EAyDI,SAAAiwB,GAA2B56B,EAAAyM,EAAAC,EAAAmuB,GAC5B,IAAAhU,EAAA,GACH,IACA,IAASwL,EAAiB1X,GACpB3a,EAAAw6B,kBAAA57B,UACA,cACF,SAAU8b,GACd,OAAa,SAAMogB,GAAiB,IAAAC,IAAAA,EAAAlhB,UAAAhW,OAAAiW,MAAA/W,MAAAg4B,EAAAA,EAAAA,OAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAAlhB,EAAAkhB,EAAAnhB,GAAAA,UAAAmhB,GAChC,IAAUze,GAAQ9P,KAAAA,EAAYC,GAAU,GAAA,CACnC,IAAAuuB,EAb8D,SAC5CH,GACvB,MAAuC,uBAAvCA,EAAuC,QAAAA,CAAA,CAWlCI,CAAAJ,GAEN,GADsB,cAAKv1B,OAAA6J,KAAAA,UAAA6rB,GAC3BJ,GAAA,CAAA,QAAA,UAAAt7B,SAAA07B,GACM,GAAAnhB,MAAA,iBAAAA,EAAA,GAAA,CACO,IAAAqhB,EAAArhB,EAAA,GACZqhB,EAAAC,wBACUD,EAAAC,uBAAA,QAGEthB,EAAAuhB,OAAA,EAAA,EAAA,CAChBD,uBAAA,GAIM,CACG,OAAA1gB,EAAAL,MAAAygB,KAAAA,CAAAA,KAAAhhB,GACT,KAGE+M,EAAA3iB,KAAYmuB,EACV,CAAA,MAAAiJ,GACA9pB,QAAKjO,MAAY,yDACjB,CACA,MAAI,KACFsjB,EAAA/gB,SAAQuiB,GAAaA,KAAG,CAExB,CACA,SAAAkT,GAAa38B,EAAAiI,EAAAye,EAAA7Y,EAAAC,EAAA1M,GACX,IAAA6mB,EAAW,GACX2U,EAAK3+B,OAAS4+B,oBAAiB78B,GAAS88B,EAAA,SAAAtgB,GACpB,GAGtB,CACA,gBACA,SACA,qBACA,uBACE7b,SAAQ6b,GACT,OAAA,EAEc,IAEf,GAA2B,mBAAvBxc,EAAgBwc,GAAO,OAAA,EAG5B,IAAAiX,EAAA1X,GACD/b,EACAwc,GACA,SAAKV,GACC,OAAO,WAAa,IAAA,IAAAihB,EAAA9hB,UAAAhW,OAAbiW,EAAa/W,IAAAA,MAAA44B,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAb9hB,EAAa8hB,GAAA/hB,UAAA+hB,GACtB,IAAStR,EAAG5P,EAAaL,WAASP,GAElC,GADRgf,GAAaxO,EAAAtqB,EAAAuF,MACE,YAAAA,KAAA8J,SAAAkN,QAAAlN,OAAA5C,EAAAC,GAAA,GAAA,CACR,IAAAmvB,EAAAnB,GAAA5gB,EAAA9Z,EAAAuF,MACF+lB,EAAA,CACFzkB,OACO0O,SAAA6F,EACGtB,KAAO+hB,GAENvW,EAAA/f,KAAA8J,OAAYic,EACf,CACN,OAAAhB,CACM,KAGPzD,EAAO3iB,KAAKmuB,EACb,CAAA,MAAAyJ,GACD,IAAQC,EAAWxhB,GAAA3b,EAAAwc,EAAA,CACjB/U,GAAAA,CAAOmoB,GACRlJ,OAAAjW,OAAA,CACDxI,OACQ0O,SAAU6F,EACjBtB,MAAA0U,GACDwN,QAAY,GAEN,IAEFnV,EAAK3iB,KAAQ63B,EACb,CACE,EAlDA,IAAK3gB,IAAAA,KAAIogB,EAAOE,EAAAtgB,GAmDhB,OAAAyL,CAAgB,CA8BZ,IACEmT,GAyUV7E,GACA8G,GACDzX,GA5US0X,GAAM,2jHACiGC,GACnHt0B,oBAAAA,QAAAA,OAAAu0B,MAAAA,IAAAA,KAAAC,EADcrC,GACdkC,GAD+B9J,WAAYpvB,KAAAs5B,KAAStC,KAAA3uB,GAAaA,EAAAutB,WAAA,OACjE,CAAA/xB,KAAA,kCAAA,SAAA01B,GACWjwB,GACD,IAAAkwB,EACE,IAEI,KADFA,EAAAL,KAAAt0B,OAASD,KAAUC,OAAS40B,WAAQC,gBAAAP,KAC9B,KAAA,GACF,IAAAQ,EAAA,IAAIC,OAAAJ,EAAU,CAA8B7xB,KACvC,MADuC2B,OACvC,EAAAA,EAAA3B,OAImB,OAHxBgyB,EAAAprB,iBACE,SAAA,MAAA1J,OAAAD,KACAC,OAAQ40B,WAAMI,gBAAoBL,EAAA,IACZG,CAC1C,CAAA,MAAAvwB,GACiB,OAAA,IACFwwB,OAAA,+BACwBV,GACzB,CAAoCvxB,KACrC2B,MAAAA,OAAAA,EAAAA,EAAA3B,OAGN,QACF6xB,IAAA30B,OAAAD,KAAAC,OAAA40B,WAAAI,gBAAAL,EACD,CAAwD,CACzD,MACDM,GACE76B,WAAAA,CAAKqK,GACLvP,EAAcwI,KAA6B,yBAAM,IAAAN,KAC/ClI,EAAIwI,iBAA0B,CAAAw3B,SAAA,EAAAC,SAAA,OAC9BjgC,OAAmB,UACnBA,OAA0B,cACxBA,EAAIwI,KAAA,kBACFxI,EAAMwI,KAAA,UAAA,GAAAxI,EACQwI,KAAA,UAAA,GACdxI,EAAWwI,KAAA,mBAAiB0T,CAAAA,EAAAqS,OAC7B/lB,KAAA03B,UAAAD,UAAAz3B,KAAA03B,UAAAF,WAAAx3B,KAAA03B,UAAAD,WACFz3B,KAAA03B,UAAAD,WACFz3B,KAAA03B,UAAAD,SAAAC,KAAAA,UAAAF,UACIG,KAAAA,uBAAel3B,IAAAiT,IAChB1T,KAAK23B,uBAAa72B,IAAA4S,EAAA,IAEpB1T,KAAA23B,uBAA0B79B,IAAA4Z,GAAA/U,KAAAonB,EAAA,IAExB,IAAA5E,SACAA,EAAO,MAAM1mB,IACXA,EAAAyM,WACAA,EAAIC,cACFA,EAAIM,aACFA,EAAAF,eAAMA,GAENR,EACA/G,KAAAsd,WAAMvW,EAAKuW,WAAqBtd,KAAAgH,OACjCD,EAAAC,OAAAS,GAEJ,QADE0Z,GACFnhB,KAAA43B,2BAAAn9B,EAAAyM,EAAAC,GACFM,GAAA,iBAAA0Z,GACDnhB,KAAI63B,sBAAe1W,EAAU1mB,EAAAyM,EAAAC,EAAA,CAC3BI,kBAEE,CACEvG,KAAAA,GACEhB,KAAA23B,uBAAkBG,QAAM93B,KAAA+3B,gBACT/3B,KAAA+3B,gBAA6B,CAE5C/Y,MAAAA,GAAuBhf,KAAAya,QAClB,CACL,CAAiCyE,QAAAA,GAClClf,KAAAya,QACU,CACX,CAAyB2E,IAAAA,GAC1Bpf,KACF0a,QAAA,CAAA,CACF2E,MAAAA,GAEHrf,KAAK0a,QAAY,CACjB,CACDmd,qBAAAA,CAAAG,EAAAv9B,EAAAyM,EAAAC,EAAAJ,GAAA,IAAAkxB,EAAAj4B,KACDk4B,EAAoB7C,GAClB56B,EACAyM,EACEC,GACE,GACqCgxB,EACI,IAAAz4B,IACjD03B,MAAAJ,GACUI,EAAAgB,UAAIvxB,IACF,IAAAhH,GAAAA,GAAOgH,EAAA6D,KAET,GAF0BytB,EACzBr3B,IAAAjB,GAAA,GACM,WAAAgH,EAAA6D,KAAP,CAAO,IACR+pB,OAAAA,EAAAnzB,KAAAA,EAAA4I,MAAAA,EAAAE,OAAAA,GAAAvD,EAAA6D,KACD1K,KAAAsd,WAAO,CACfzd,KACQyB,KAAA4X,GAAqB,MACtBmf,SACF,CAAA,CACOroB,SAAiB,YAE1BuE,KAAA,CAAA,EAAA,EAAArK,EAAAE,IAEK,CACA4F,SAAyB,YAExBuE,KACD,CAAA,CACM+f,QAAA,cACD/f,KACA,CAAA,CACD+f,QAAiB,OACX5pB,KAAA,CAAA,CAAA4pB,QAA2B,cAAOG,WACjCnzB,UAIhB,EACM,MA1BQ,CA8BT,EAEH,IAECg3B,EAFDC,MAAAP,EACDQ,EAAqB,EAmBfC,EAAoBzH,IAjBR0H,IACdC,EACDC,EAgBEJ,GAAAxH,EAAAwH,EAAAD,EACFD,EAAAO,sBAAAJ,IAGJD,EAAAxH,GArBK2H,EAAW,GACZC,EAAAE,IACFA,EAAAt8B,iBAAA+D,UAAAA,SAAAuJ,IACIkN,GAAelN,EAAA5C,EAAAC,GAAA,IAChBwxB,EAAkBh6B,KAAAmL,EAChB,IAEFgvB,EAAAt8B,iBAAmB+D,KAAAA,SAAAw4B,IACdA,EAAA18B,YACNu8B,EAAAG,EAAA18B,WACG,GACF,EAEIu8B,EAAKn+B,EAAAJ,UACNs+B,GAQJp4B,QAAa,WAAA,IAAAy4B,EAAAC,GAAA,UAAYnvB,GACxB,IAAAnQ,EACIkG,EAAAo4B,EAAUjxB,OAAKpH,MAAOkK,GAC1B,IAAAquB,EAAAr+B,IAAA+F,IACEiK,IAAAA,EAAAI,OAAA,IAAAJ,EAAAM,OADF,CAGD,GADP+tB,EAAAr3B,IAAAjB,GAAA,GACO,CAAA,QAAA,UAAA7F,SAAA8P,EAAAD,WAAA,CACF,IAAAgL,EAAA/K,EAAAE,WAAAF,EAAAD,YAC+C,KAAA,OAApClQ,EAA0B,MAAnBkb,OAAmB,EAAAA,EAAUqkB,6BAAA,EAAAv/B,EAAAk8B,wBACjDhhB,EAAAijB,MAAAjjB,EAAAskB,iBAEC,CAGA,IAAUjvB,EAAAJ,EAAUgN,aAAYhN,EAAUI,MACnCE,EAAON,EAAA6M,cAAa7M,EAAAM,OACrBgvB,QAAAC,kBAAAvvB,EAAA,CACAwvB,YAAApvB,EACFqvB,aAAUnvB,IAEVgtB,EAAMoC,YACP,CACG35B,KACFu5B,SACDlvB,MAAAA,EACGE,OAAAA,EACF7C,eAAWR,EAAAQ,gBAEd,CAAA6xB,GAxBK,CA0BR,IAAA,OAAA,SAAAK,GAAA,OAAAT,EAAAlkB,MAAA9U,KAAAsU,UAAA,CAAA,CA9BkB,IA+BhBgkB,EAAIO,sBAAqBJ,GAAU,EAEnCH,EAAIO,sBAAyBJ,GAC3Bz4B,KAAA+3B,eAAS,KACTG,IACDwB,qBAAApB,EAAA,CAEC,CACAV,0BAAAA,CAAuBn9B,EAAAyM,EAAAC,GACvBnH,KAAA25B,uBACE35B,KAAA45B,oCACA,IAAA1B,EAAsB7C,GACvB56B,EACDyM,EACAC,GACE,GAEA0yB,EAnXqB,SACnB9Z,EAAAtlB,EAAAyM,EAAAC,GACR,IAAAma,EAAS,GACHwY,EAAAxiC,OAAA4+B,oBACNz7B,EAAAs/B,yBAAU1gC,WACV2gC,EAAA,SAAAnkB,GAEF,IACM,GAAiB,mBAAjBpb,EAAAs/B,yBAAiB1gC,UAAAwc,GAAA,OAAA,EAGrB,IAASiX,EAAA1X,GACT3a,EAAAs/B,yBAAa1gC,UACbwc,GACA,SAAAV,GACA,OAAU,WAAA,IAAA,IAAA8kB,EAAA3lB,UAAAhW,OAAAiW,EAAA/W,IAAAA,MAAAy8B,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAA3lB,EAAA2lB,GAAA5lB,UAAA4lB,GAWN,OAVUljB,GAAAhX,KAAA8J,OAAA5C,EAAAC,GAAA,IACRiK,YAAA,KACI,IAAAklB,EAAAnB,GAAA5gB,EAAA9Z,EAAAuF,MACV+f,OAAejW,OAAA,CACNxI,KAAA4X,GAAA,MACHlJ,SAAA6F,EACItB,KAAA+hB,GACJ,GACI,GAENnhB,EAAiBL,MAAA9U,KAAAuU,EACf,KAGR+M,EAAA3iB,KAAAmuB,EACA,CAAM,MAAAqN,GACN,IAAS3D,EAAiBxhB,GACxBva,EAAOs/B,yBAA2B1gC,UACpCwc,EACA,CACM/U,GAAAA,CAAMmoB,GACNlJ,OAAOjW,OAAA,CACDxI,KAAA4X,GAAiB,MACnBlJ,SAAW6F,EACRtB,KAAM,CAAA0U,GACTwN,QAAW,GAEf,IAGAnV,EAAO3iB,KAAM63B,EAAA,CACb,EAzCJ,IAAM3gB,IAAAA,KAAAikB,EAAAE,EAAAnkB,GA0CK,MACP,KACNyL,EAAA/gB,SAAAuiB,GAAAA,KAAA,CAEI,CA+TesX,CACZp6B,KAAAua,gBAAA/e,KACFf,MAAAA,EACDyM,EACEC,GAEAkzB,EArOE,SAA6Bta,EAAAtlB,EAAAyM,EAAcC,GACzC,IAAAma,EAAI,GAuBV,OAtBMA,EAAA3iB,QACAq3B,GACAv7B,EAAA6/B,sBAAmBjhC,UACnB6f,GAAQqhB,MACRxa,EACE7Y,EAAQC,EAC8H1M,SAI3I,IAAAA,EAAA+/B,wBACFlZ,EAAQ3iB,QACPq3B,GACDv7B,EAAA+/B,uBAAAnhC,UACD6f,GAAOuhB,OACR1a,EACD7Y,EACEC,EACI1M,IAIK,KACP6mB,EAAI/gB,SAAOuiB,GAAAA,KAAa,CAEpB,CA0MuB4X,CACzB16B,KAAAua,gBAAc/e,KAAQwE,MACtBvF,EACEyM,EACAC,GACAnH,KACD+3B,eAAA,KACFG,IACD2B,IACAQ,GAAqB,CAEvB,CACAT,iCAAAA,GACEf,uBAAmB,IAAO74B,KAAA26B,+BAC1B,CACAhB,oBAAAA,GACE,IAAAiB,EAAyB5J,IACvBhxB,KAAA03B,UAAMF,SAAaxG,EACnB6H,sBAAkB+B,EAAM,EAE1B/B,sBAAA+B,EAAA,CAEAD,2BAAAA,GACA36B,KAAA23B,uBAAAp3B,SACD,CAAAs6B,EAAA/wB,KACF,IAAAjK,EAAAmH,KAAAA,OAAApH,MAAAkK,GACD9J,KAAM86B,8BAAGhxB,EAAAjK,EAAA,IAGTg5B,gCAAmB8B,+BACnB,CACAG,6BAAAA,CAA0BhxB,EAAAjK,GACxB,IAAAG,KAAIya,SAAUza,KAAA0a,OAAd,CAGM,IAAAqgB,EAAoBpD,KAAAA,uBAAsB79B,IAAAgQ,GAAA,GAAAixB,IAC3C,IAAAl7B,EAD2C,CAC3C,IACF00B,EAAAwG,EAAAhnB,KAAApc,IACT,IAAaqjC,6IAAAC,CAAAtjC,EAAAujC,GACL,OAAIF,CAAA,KAEF15B,KAAAA,GAASy5B,EAAU,GAA4B/6B,KAChDsd,WAAA,CAAAzd,KAAAyB,OAAA+2B,SAAA9D,IAAAv0B,KACF23B,uBAAAr3B,OAAAwJ,EAPI,CAFD,CASH,EAEJ,MACDqxB,GACEz+B,WAAAA,CAAOqK,GACRvP,iCAAAk7B,SACDl7B,EAAcwI,KAAA,cACZxI,EAAwBwI,KAAA,uBACzBxI,yBAAA4jC,IACDp7B,KAAIsd,WAAMvW,EAAAuW,WACRtd,KAAAq7B,oBAAwBt0B,EAAAs0B,mBACzB,CACDxf,iBAAAA,CAAUyf,EAAA5f,GACR,aAAYA,EAAY9S,YACzB5I,KAAAsd,WAAA,CACG3C,KAAA,GACFwB,QAAY,GACbQ,MAAA,GACG/T,WACF,CAAA,CACD/I,GAAA6b,EAAA7b,GACG+I,WAAY8S,EAAA9S,eAId5I,KAAAsb,iBAAmBggB,EACpB,CACDhgB,gBAAAA,CAAYggB,GACVt7B,KAAOu7B,oBAAA96B,IAAA66B,KACRt7B,KAAAu7B,oBAAA36B,IAAA06B,GACHt7B,KAAAw7B,6BAAAF,GACA,CACEtW,gBAAAA,CAAcF,EAAAJ,GAAA,IAAA+W,EAAAz7B,KAChB,GAAA,IAAA8kB,EAAAxmB,OAAA,CACI,IAAAo9B,EAAe,CACnB77B,GAAA6kB,EACAiX,SAAS,IAELtJ,EAAA,GAAkBuJ,EAAA,SAAAvyB,GAElB,IAAAgb,EACAoX,EAAYrX,YAAA3jB,IAAA4I,GASPgb,EAAOoX,EAAArX,YAAAxkB,MAAAyJ,IARVgb,EAAAoX,EAAarX,YAAAxjB,IAAAyI,GACfgpB,EAAA1zB,KAAA,CACF0lB,UACQhnB,MAAIG,MAAAC,KAAA4L,EAAAhM,OAAAw+B,SAAApc,CAAAA,EAAA9F,KAAA,CACLiR,KAAAjtB,EAAc8hB,EAAApW,EAAAzL,MACd2V,MAAAoG,SAIL+hB,EAAIC,SAAAh9B,KAAA0lB,EACJ,EAbA,IAAAhb,IAAAA,KAAgByb,EAAA8W,EAAAvyB,GAchBgpB,EAAK/zB,OAAa,IAAAo9B,EAAgBrJ,OAAmBA,GACrDryB,KAAKq7B,oBAAaK,EArBtB,CAsBI,CACA16B,KAAAA,GAA2ChB,KACzCokB,YAAMpjB,QACJhB,KAAAu7B,wBAAY7I,OAAA,CAGhB8I,4BAAAA,CAAcM,GAAqB,EAGjC,MAAAC,GACEr/B,WAAAA,GAAkBlF,qBACnBmI,SACDnI,EAAkBwI,KAAA,UAAA,EAChB,CAAkB6e,aAAAA,CACnBle,EAAAq7B,GACP,IAAAC,EAAWC,KAAAA,QAAApiC,IAAA6G,GACL,OAAIs7B,GAAAz+B,MAAeC,KAAAw+B,GAAArxB,MAAAD,GAAAA,IAAAqxB,GACnB,CAAsBp7B,GAAAA,CACvBD,EAAAgK,GACF3K,KAAAm8B,SACDn8B,KAAQm8B,QAAA,EACNtD,uBAAuB,KACvB74B,KAAOk8B,QAA2B,IAAAv8B,QACnCK,KAAAm8B,QAAA,CAAA,KAGAn8B,KAAAk8B,QAAAp7B,IAAAH,QAAAu7B,QAAApiC,IAAA6G,IAAA0Z,IAAAA,KAAAzZ,IAAA+J,GACD,CACEyxB,OAAAA,GAAO,EAKR,IACDC,IAAkB,EAChB,IACE,GAA6B,IAA7B7+B,MAAMC,UAAUwM,GAAa,EAARA,IAAQ,GAAA,CAC3B,IAAAqyB,GAAAjiC,SAAAC,cAAA,UAAAD,SACEE,KAAAC,YAAA8hC,IAAA9+B,MACVC,MAAArG,OAAAA,EAAAklC,GAAA5hC,oBAAAtD,EAAAA,EAAAoG,MAAAC,OAAAD,MAAAC,KAAApD,SACOE,KAAAK,YAAA0hC,GAAA,CAEH,CAAA,MAAA95B,GACDyJ,QAAAswB,MAAA,gCAAA/5B,EAAA,CAEC,IAm5BAwG,GACIwzB,GAp5BJx1B,GAnwIG,IAAKy1B,EAmwII,SACbC,GAAA31B,QAAA,IAAAA,IAAAA,EAAA,CAAA,GACD,IAAAyT,KACEA,EAAAmiB,iBACDA,EAAAC,iBACDA,EAAc11B,WACZA,EAAY,WAAOC,cACpBA,EAAA,KAAAqhB,YACDA,EAAU,YAAAC,eACRA,EAAmB,KAAAhiB,cACpBA,EAAA,UAAAC,iBACDA,EAAU,KAAAW,iBACRA,GAAmB,EAAAw1B,cACpBA,EACDz7B,iBAAe07B,EACb7tB,eAAO8tB,EAAAx7B,YACRA,EAAA+F,WACDA,EAAW6d,MACTA,EAAA6X,OACDA,EAAA7b,SACDA,EAAI,CAAA,EAAA5Z,eACFA,EAAmB,CAAA,EAAA01B,cACpBA,EAAAtW,UACDA,GAAW,EAAAlf,aACTA,GAAgB,EAAAooB,yBACdA,GAAY,EAAAqN,YACbA,GAAAn2B,qBAAAA,EAAAm2B,YAAAn2B,EAAAm2B,YAAA,QAAAxU,qBACDA,GAAI,EAAA0D,aACJA,GAAc,EAAA5kB,aACdA,GAAI,EAAAinB,QACFA,EAAA/mB,gBACDA,EAAeA,MAAA,GAAAmkB,oBACdA,MAAaxR,IAAA,IACdJ,aAAAkjB,GACDp2B,EAtkEUkT,GAukEGkjB,EAAA,IACjBC,GAAWvN,GAAAvtB,OAAA2L,SAAA3L,OACL+6B,GAAa,EACb,IAAAD,EACD,IACF96B,OAAA2L,OAAA5T,WACIgjC,GAAsB,EAE1B,CAAA,MAAAx2B,GACHw2B,GAAA,CACI,CAEJ,GAAID,IAAkB5iB,EAClB,MAAA,IAAAjI,MAAiB,6BAErB,IAAI6qB,IAAWC,EACX,MACF,YAEsB,IAApBJ,QAAqC,IAAP9b,EAAO6E,YACtC7E,EAAA6E,UAAAiX,GAECj2B,GAAIhG,QACJ,IAkCDs8B,EAlCCl8B,GAAwB,IAATy7B,EAAS,CACtBU,OAAI,EACFC,MAAK,EAAE,kBACK,EACZC,OAAK,EAAGC,OACT,EACDC,QAAI,EACFjQ,OAAA,EAAyCkQ,QAC1C,EACCC,KAAA,EAAkBr8B,MAC1B,EACQs8B,MAAA,EAAkB17B,KAC1B,EACQ27B,MAAI,EACFxgB,UAAM,EAAIygB,QACR,EAAAC,UACZ,YAAAnB,EACSA,EAAA,CAAAmB,UAAA,GACThvB,GAAa,IAAA8tB,GAAA,QAAAA,EAAA,CACLptB,QAAM,EAAyCD,SAChD,EACFG,aAAA,EACDc,gBAAO,EACRZ,gBAAA,EACDE,gBAAoB,EAClBC,mBAAkB,EAChBE,sBAAW,EAGZD,mBAAA,QAAA4sB,EACFjtB,qBAAA,QAAAitB,EACD7lB,mBAAc,QAAA6lB,GACZA,GAAwC,CAAA,GAp1FxC,SAAOtiC,QAAA,IAAAA,IAAAA,EAAA6H,QACR7H,aAAAA,IAAAA,EAAAyjC,SAAA7kC,UAAAkH,UACD9F,EAAAyjC,SAAO7kC,UAAgBkH,QAAA/C,MAAAnE,UAAAkH,SAEN,iBAAA9F,IAAUA,EAAI0jC,aAAA9kC,UAAAkH,UAC5B9F,EAAA0jC,aAAA9kC,UAAAkH,QAAA/C,MAAAnE,UAAAkH,QACM,CA+0FP69B,GAEJ,IAAAC,EAAA,EACIC,EAAcz3B,IAClB,IAAc2nB,IAAAA,KAAUC,GAAA,GACfD,EAAA8P,iBACTz3B,EAAa2nB,EAAA8P,eAAkBz3B,IAO3B,OAJAm2B,IACAK,IACAx2B,EAAAm2B,EAAYn2B,IAEZA,CAAA,EAEF+oB,GAAUA,CAAAnQ,EAAQiR,KAClB,IAAM/2B,EACFkN,EAAA4Y,EAKE,GAJJ5Y,EAAAmqB,UAASjb,cACTpc,EAASsmB,GAAU,WAAWtmB,EAAAwlB,aAAAtY,EAAAvF,OAAAoX,GAAAiY,cAAA9pB,EAAAvF,OAAAoX,GAAAuY,qBAAApqB,EAAA6D,KAAA2K,SAAAuD,GAAAsY,UAC5BjR,GAAI1f,SAAqBg+B,GAAQA,EAAArf,aAE/Bke,EACK,MADe5iB,GACDA,EAAA8jB,EAAAz3B,GAAA6pB,QACjB,GAAA2M,EAAW,CAAe,IACpC/M,EAAA,CACOhvB,KAAA,QACD+V,MAAOinB,EAAKz3B,GACb9D,OAAAT,OAAAC,SAAAQ,OACF2tB,cAECpuB,OAAS2L,OAAAurB,YAAmBlJ,EAAY,IACzC,CACD,GAAIzpB,EAAAvF,OAASoX,GAAQiY,aACnB2M,EAAwBz2B,EACxBw3B,EAAkB,OACd,GAAAx3B,EAAAvF,OAAiBoX,GAAAuY,oBAAA,CACnB,GAAApqB,EAAA6D,KAAS2K,SAAOuD,GAAsBsY,UAAArqB,EAAA6D,KAAA0lB,eACvC,OAECiO,IACF,IAAOG,EAAa5B,GAAQyB,GAAAzB,EAChC6B,EAAsB9B,GAAiB91B,EAAAmqB,UAAAsM,EAAAtM,UAAA2L,GACnC6B,GAAWC,IACf/H,IAA+B,EAE/B,GAEA,IAAAgI,EAA+BhhB,IAC3BkS,GAAW,CACftuB,KAASoX,GAAAuY,oBACLvmB,KAAM8X,EAAA,CACPnN,OAAAuD,GAAAsY,UACHxT,IAEA,EAEIihB,EAAiBtX,GAAAuI,GAAA,CACjBtuB,KAAAoX,GAAgBuY,oBAChBvmB,KAAA8X,EAAA,CACAnN,OAAAuD,GAAegZ,QACfvK,KAGAuX,EAAavX,GAAAuI,GAAA,CACbtuB,KAAAoX,GAAauY,oBACbvmB,KAAA8X,EAAA,CACAnN,OAAAuD,GAAYiZ,gBACZxK,KAUFhM,EAAwB,IAAA8f,GAAO,CACjC7d,WAAAohB,EACArD,oBATcr2B,GAAA4qB,GAAA,CACVtuB,KAAAoX,GAAUuY,oBACVvmB,KAAA8X,EAAA,CACJnN,OAASuD,GAAsBwZ,mBACzBptB,OAOJmW,EAAoB,IAAMwU,GAAA,CACxB3oB,UACEsW,WAAAohB,EACArjB,oBAAQwU,2BACCD,iBAGP,IAAApB,IAAAA,KAAQC,GAAA,GAAAD,EACNqQ,WAAOrQ,EACjBqQ,UAAA,CACOC,WAAA93B,GACF4pB,wBAAAzV,EAAAyV,wBACGd,6BAA6B3U,EAAI2U,+BAGrC,IAAAlR,MAAOmd,GACR9c,OAAAsY,GAAA,CACD9vB,eACA6V,WAAOshB,EACLnkC,IAAA6H,OACE4E,aACAC,gBACDH,UACFma,SAAAA,EAAArX,OACDvC,mBAEF,IAAAgU,MAAAkX,GAAA,CACEnV,WAAOohB,EACTzb,SAAA0b,EACA/tB,cAAU,CACV1J,aACAC,gBACAV,gBACAC,mBACAW,mBACAjG,mBACAmG,iBACAD,aACA/F,cACAkG,eACAD,eACA2Z,WACAlS,iBACAkM,gBACAE,oBACA4D,iBACAvX,kBACAkX,wBAEA5X,YAEA0vB,GAAiB,SAAAhG,GACjB,QADiB,IAAAA,IAAAA,GAAA,GACjB/J,EAAA,CAGAiJ,GACA,CACAtuB,KAAYoX,GAAAyY,KACZzmB,KAAY,CACZ9M,KAAY0E,OAAAC,SAAA3E,KACZsM,MAAY0M,KACZxM,OAAYqM,OAGZia,GAEArV,EAAYra,QACZua,EAAYwD,OACZkB,GAAY1f,SAAAg+B,GAAAA,EAAAnf,SACZ,IAAAze,EApjHyB,SACnB/E,EAAAmL,GAAoB,IAErBC,OAAAC,EAAA,IAAAw1B,EAAAv1B,WAAAA,EACuB,WAAAC,cAC1BA,EAAO,KAAAV,cACLA,EAAK,UAAAC,iBACLA,EAAQ,KAAAW,iBACRA,GAAA,EAAAG,aACAA,GAAA,EAAAC,aACAA,GAAA,EAAAo1B,cACAA,GAAA,EAAAv1B,WACAA,EAAA/F,YACAA,EAAAw9B,QACAA,GAAA,EAAAx3B,eACAA,EAAAgI,mBACAA,EAAAL,YACAA,EAAAC,aACAA,EAAAC,kBACAA,EAAAC,iBACAA,EAAAC,sBACAA,EAAA5H,gBACAA,EAAAA,MAAA,IACAX,GAAA,CAAA,EAwGJ,OAAIgI,GAAsBnT,EAAA,CACxB0D,IAAO1D,EACToL,OAAAC,EACAC,aACAC,gBACIV,gBACJC,mBACAsI,WAAM,EACJ3H,mBACAjG,kBAhHE,IAAAy7B,EAAA,CACAU,OAAA,EACAC,MAAA,EACA,kBAAA,EACAC,OAAA,EACDC,OAAA,EACHC,QAAA,EAoEAjQ,OAAS,EACPkQ,QAAa,EACfC,KAAA,EACAr8B,MAAS,EACPs8B,MAAO,EACP17B,KAAS,EACT27B,MAAW,EACTxgB,UAAS,EACPygB,QAAI,EACFC,UAAA,IACD,IAAApB,EAAA,CACDoB,UAAO,GACbpB,EA2BGv1B,aACH/F,cACI0N,gBA5BkB,IAAA8vB,GAAA,QAAAA,EAEpB,CACApvB,QAAY,EACVD,SAAe,EACfG,aAAO,EACLc,gBAAY,EACZb,qBAAgB,QAAAivB,EAEfhvB,gBAAA,EACFE,gBAAA,EACFC,mBAAA,EACDC,oBAAO,EACTC,sBAAA,QAEI2uB,KAAMA,EAcNx3B,iBACAC,eACFC,eACE8H,qBACAL,cACAC,eACAC,oBACEC,mBACDC,wBACD5H,kBACEC,mBAAc,GAEhB,CA45GQq3B,CAAA3kC,SAAA,CACZ2M,UACAE,aACAC,gBACAV,gBACAC,mBACAW,mBACAw1B,cAAYz7B,EACZkG,aACA/F,cACAw9B,QAAY9vB,EAmWR1H,iBACAE,eACAD,eAuEJ0H,YAAStT,IACA2b,GAAS3b,EAAcoL,KAChCmU,EAAAC,UAAAxf,GAES6b,GAAmB7b,EAAAoL,KACtBqU,EAAQC,iBAAA1f,GAEN8b,GAAc9b,IACZ2f,EAAgBC,cAAKjI,GAAAlX,WAAAT,GAAAvB,SACvB,EAEF8U,aAAUA,CAAAsM,EAAYC,KAC5BP,EAAAQ,aAAAF,EAAAC,GACOH,EAAeK,oBAAAH,EAAA,EAEpBpM,iBAAsBA,CAAAisB,EAAI5f,KAC1BL,EAAwBQ,kBAAYyf,EAAA5f,EAAA,EAElChU,oBACc,IACZ/G,EACE,OAAAsL,QAAWC,KAAA,mCAEnB0jB,GACG,CACDtuB,KAAOoX,GAAAiY,aACTjmB,KAAA,CACIoR,KAAAnb,EACIs+B,cAAAjpB,GAAA1T,UAGRouB,GAEAzQ,GAAmB1f,SAAQg+B,GAAAA,EAAAlf,WACvBhlB,SAAAg2B,oBAA4Bh2B,SAAAg2B,mBAAmB/xB,OAC7C+c,GAAAA,EAAA2J,iBACA3qB,SAAAg2B,mBACJrpB,GAAWpH,MAAAvF,UA1eb,CA4eG,EACH,IACI,IAAAinB,EAAO,GACPZ,EAAAphB,IACA,IAAA3F,EACF,OAAAmmB,GAA2BmF,GAA3BnF,CACE,CACKxC,WAAOohB,EACPrZ,YAASA,CAAA6B,EAAA7R,IAAAua,GAAA,CACVtuB,KAAMoX,GAAAuY,oBACHvmB,KAAO,CACb2K,SACG6R,eAGAhG,mBAAUjM,GAAA2a,GAAA,CACPtuB,KAAAoX,GAASuY,oBACfvmB,KAAA8X,EAAA,CACGnN,OAAOuD,GAAwB+Y,kBACtB1c,KAGjBgO,SAAa0b,EACLrZ,iBAAiBrQ,GAAA2a,GAAA,CACjBtuB,KAAKoX,GAAcuY,oBACnBvmB,KAAK8X,EAAA,CACAnN,OAAAuD,GAAmB6Y,gBACzBxc,KAGCsQ,QAAM0D,GAAA2G,GAAmB,CACrBtuB,KAAAoX,GAAAuY,oBACPvmB,KAAA8X,EAAA,CACFnN,OAAAuD,GAAAkZ,OACY7I,KAGPzD,mBAAqB6B,GAAAuI,GAAa,CAC/BtuB,KAAAoX,GAAiBuY,oBACvBvmB,KAAA8X,EAAA,CACInN,OAAWuD,GAAY8Y,kBAC7BrK,KAGK5B,iBAAWhG,GAAAmQ,GAAA,CACXtuB,KAASoX,GAAMuY,oBACfvmB,KAAA8X,EAAA,CACEnN,OAAOuD,GAAMmZ,gBAClBtS,KAGGiG,mBAAoBjG,GAAUmQ,GAAY,CAC1CtuB,KAAAoX,GAAkBuY,oBAClBvmB,KAAM8X,EAAA,CACNnN,OAAOuD,GAAAoZ,kBACHvS,KAGZkG,iBAAWiZ,EACLhZ,OAAOyB,GAASuI,GAAQ,CACzBtuB,KAAAoX,GAAAuY,oBACMvmB,KAAA8X,EAAA,CACDnN,OAASuD,GAAYsZ,MACrB7K,KAGFxB,YAAewB,IAChBuI,GAAA,CACMtuB,KAAMoX,GAAMuY,oBACbvmB,KAAI8X,EAAA,CACbnN,OAAAuD,GAAAuZ,WACU9K,IAEC,EAETvB,gBAAAhgB,IACM8pB,GAAY,CACpBtuB,KAAAoX,GAAAuY,oBACHvmB,KAAA8X,EAAA,CACInN,OAAiBuD,GAAAsmB,eACJp5B,IAET,EAEFoB,aACGshB,cACPC,iBACAhiB,gBACAC,mBACAtF,mBACAiG,mBACO8Z,WACPwF,YACAlf,eACAD,eACQkhB,uBACR0D,eACF9sB,MACSiC,cACA+F,aACTI,kBACIP,gBACF8H,iBACO1H,iBACNP,UACMmU,gBACDE,oBACAE,mBACAqD,uBACFK,iBACD4M,sBACC4C,SAAQ,OAAA90B,EAAA80B,MAAAA,OAAAA,EAAAA,EAAA5R,QAAAwK,GAAAA,EAAA7G,iBAAA,EAAA7mB,EAAAoa,KAAAsT,IAAA,CACT7G,SAAA6G,EAAA7G,SACGzZ,QAAasgB,EAAAtgB,QACV2nB,SAAMhS,GAAckT,GAAM,CAC1BtuB,KAAAoX,GAAA4Y,OACD5mB,KAAO,CACNy0B,OAAQ9X,EAAOjiB,KACrBsX,mBAGG,IAEFyI,EACN,EAEAhK,EAAe+U,iBAAW91B,IACpB,IACNknB,EAAW3iB,KAAA+hB,EAAAtmB,EAAAsT,iBACL,CAAA,MAAQ1P,GACTiO,QAAAC,KAAAlO,EACD,KAEA,IAAA+gB,EAAOA,KACL2X,KACApV,EAAM3iB,KAAI+hB,EAAArmB,WACXgiC,IAAA,CAAA,EA4BD,MA1BwB,gBAAtBhiC,SAAI4W,YAAuC,aAAA5W,SAAA4W,WAC3C8N,KAECuC,EAAA3iB,KACF6U,GAAA,oBAAA,KACDoc,GAAO,CACRtuB,KAAAoX,GAAA2Y,iBACK3mB,KAAO,CAAA,IAEgC,qBAAtCwyB,GAAsCne,GAAO,KAGhDuC,EAAK3iB,KACL6U,GACN,QACM,KACDoc,GAAA,CACUtuB,KAAKoX,GAAQ0Y,KACnB1mB,KAAQ,CAAA,aAEHwyB,GAAAne,GAAA,GAEVzc,UAII,KACJgf,EAAS/gB,SAAQuiB,GAAKA,MACpBlE,EAAkBwd,UAClBC,IAAa,EAt5FbpiB,QAAY,CAu5FK,CAC6B,CAC/C,MAAAjc,GACFiO,QAAAC,KAAAlO,EACD,CACE,CACA0+B,GAAA0C,eAAiB,CAAIC,EAAA3iB,KACrB,IAAA2f,GACD,MAAA,IAAA9pB,MAAA,iDAECqd,GAAI,CACJtuB,KAAIoX,GAAS6Y,OACb7mB,KAAI,CACF20B,MACD3iB,YAED,EACDggB,GACD4C,WAAgB,KACdrf,GAAU1f,SAAKg+B,GAAAA,EAAAvf,UAAA,EAChB0d,GACG6C,iBAAoB7O,IACtB,IAAA2L,GACA,MAAK,IAAA9pB,MAAiB,mDAEpBmkB,GAAchG,EAAQ,EACmBgM,GAC1C11B,OAAAA,IAEGw1B,GAEAxzB,KAAAA,GAAO,CAAA,IADTwzB,GAAAgD,WAAe,GAAA,aAAgBhD,GAAAA,GAAAiD,QAAiB,GAAU,UAAOjD,GAAAA,GAAAkD,QAAA,GAAA,cCh+KnEtoC,4JAHAuoC,GAAYroC,OAAOC,eAEnB0a,GAAgBza,CAACC,EAAKC,EAAKC,IADTC,EAACH,EAAKC,EAAKC,IAAUD,KAAOD,EAAMkoC,GAAUloC,EAAKC,EAAK,CAAEG,YAAY,EAAMC,cAAc,EAAMC,UAAU,EAAMJ,UAAWF,EAAIC,GAAOC,EACjHioC,CAAgBnoC,EAAoB,iBAARC,EAAmBA,EAAM,GAAKA,EAAKC,GAEpGK,GAAcV,OAAOC,eAErBU,GAAkBA,CAACR,EAAKC,EAAKC,IADTO,EAACT,EAAKC,EAAKC,IAAUD,KAAOD,EAAMO,GAAYP,EAAKC,EAAK,CAAEG,YAAY,EAAMC,cAAc,EAAMC,UAAU,EAAMJ,UAAWF,EAAIC,GAAOC,EACnHO,CAAkBT,EAAoB,iBAARC,EAAmBA,EAAM,GAAKA,EAAKC,GAUtGU,GAAsB,CAC1BC,KAAM,CAAC,aAAc,aAAc,gBAAiB,eACpDC,WAAY,CAAC,OAAQ,eACrBC,QAAS,CAAC,aAAc,gBAAiB,oBACzCC,iBAAkB,IAEdC,GAAoB,CACxBJ,KAAM,CAAC,WAAY,eACnBC,WAAY,CAAC,gBACbC,QAAS,GACTC,iBAAkB,CAAC,gBAEfE,GAA2B,CAAA,EACjC,SAASC,GAAwBlB,GAC/B,GAAIiB,GAAyBjB,GAC3B,OAAOiB,GAAyBjB,GAClC,IAAMmB,EAAaG,WAAWtB,GACxB0B,EAAmBP,EAAWQ,UAC9BC,EAAgB5B,KAAOW,GAAsBA,GAAoBX,QAAO,EACxE6B,EAAuBC,QAC3BF,GACAA,EAAcG,OACXC,IACC,IAAIC,EAAKC,EACT,OAAOJ,QACoG,OAAxGI,EAA4E,OAAtED,EAAMrC,OAAOuC,yBAAyBT,EAAkBM,SAAqB,EAASC,EAAIG,UAAe,EAASF,EAAGG,WAAWC,SAAS,iBAC1J,KAIQC,EAAcvC,KAAOgB,GAAoBA,GAAkBhB,QAAO,EAClEwC,EAAqBV,QACzBS,GAAeA,EAAYR,OAExBU,IACC,IAAIR,EACJ,MAA2C,mBAA7BP,EAAiBe,KAA+D,OAAnCR,EAAMP,EAAiBe,SAAmB,EAASR,EAAII,WAAWC,SAAS,iBAAe,KAI3J,GAAIT,GAAwBW,EAE1B,OADAvB,GAAyBjB,GAAOmB,EAAWQ,UACpCR,EAAWQ,UAEpB,IACE,IAAMe,EAAWC,SAASC,cAAc,UACxCD,SAASE,KAAKC,YAAYJ,GAC1B,IAAMK,EAAML,EAASM,cACrB,IAAKD,EAAK,OAAO5B,EAAWQ,UAC5B,IAAMsB,EAAkBF,EAAI/C,GAAK2B,UAEjC,OADAgB,SAASE,KAAKK,YAAYR,GACrBO,EACEhC,GAAyBjB,GAAOiD,EADVvB,CAEjC,CAAA,MAAAyB,GACI,OAAOzB,CACR,CACH,CACA,IAAM0B,GAA2B,CAAA,EACjC,SAASC,GAAuBrD,EAAKsD,EAAUtB,GAC7C,IAAIC,EACEsB,EAAcvD,MAAOwD,OAAOxB,GAClC,GAAIoB,GAAyBG,GAC3B,OAAOH,GAAyBG,GAAUE,KACxCH,GAEJ,IAAMI,EAAqBxC,GAAwBlB,GAC7C2D,EAGA,OAHqB1B,EAAMrC,OAAOuC,yBACtCuB,EACA1B,SACW,EAASC,EAAIG,IAC1B,OAAKuB,GACLP,GAAyBG,GAAYI,EAC9BA,EAAkBF,KAAKH,IAFCA,EAAStB,EAG1C,CACA,IAAM4B,GAAyB,CAAA,EAC/B,SAASC,GAAqB7D,EAAKsD,EAAUb,GAC3C,IAAMc,EAAcvD,MAAOwD,OAAOf,GAClC,GAAImB,GAAuBL,GACzB,OAAOK,GAAuBL,GAAUO,KACtCR,GAEJ,IACMS,EADqB7C,GAAwBlB,GACRyC,GAC3C,MAA+B,mBAApBsB,EAAuCT,EAASb,IAC3DmB,GAAuBL,GAAYQ,EAC5BA,EAAgBD,KAAKR,GAC9B,CAuCA,IAAMU,GAAU,CACdC,WAvCF,SAAsBC,GACpB,OAAOb,GAAuB,OAAQa,EAAI,aAC5C,EAsCEC,WArCF,SAAsBD,GACpB,OAAOb,GAAuB,OAAQa,EAAI,aAC5C,EAoCEE,cAnCF,SAAyBF,GACvB,OAAOb,GAAuB,OAAQa,EAAI,gBAC5C,EAkCEG,YAjCF,SAAuBH,GACrB,OAAOb,GAAuB,OAAQa,EAAI,cAC5C,EAgCEI,SA/BF,SAAoBJ,EAAIK,GACtB,OAAOV,GAAqB,OAAQK,EAAI,WAAjCL,CAA6CU,EACtD,EA8BEC,YA7BF,SAAuBN,GACrB,OAAOL,GAAqB,OAAQK,EAAI,cAAjCL,EACT,EA4BEY,KA3BF,SAAgBP,GACd,OAAKA,GAAQ,SAAUA,EAChBb,GAAuB,aAAca,EAAI,QADb,IAErC,EAyBEQ,YAxBF,SAAuBR,GACrB,OAAOA,EAAGQ,WACZ,EAuBEC,WAtBF,SAAsBT,GACpB,OAAKA,GAAQ,eAAgBA,EACtBb,GAAuB,UAAWa,EAAI,cADJ,IAE3C,EAoBEU,cAnBF,SAAyBV,EAAIW,GAC3B,OAAOxB,GAAuB,UAAWa,EAAI,gBAAtCb,CAAuDwB,EAChE,EAkBEC,iBAjBF,SAA4BZ,EAAIW,GAC9B,OAAOxB,GAAuB,UAAWa,EAAI,mBAAtCb,CAA0DwB,EACnE,EAgBEE,iBAfF,WACE,OAAO7D,GAAwB,oBAAoB8D,WACrD,GA4GA,MAAM+/B,GACJ//B,WAAAA,GACEzE,GAAgB+H,KAAM,YAA6B,IAAIN,KACvDzH,GAAgB+H,KAAM,cAA+B,IAAIL,QAC1D,CACDC,KAAAA,CAAMhE,GACJ,IAAIjC,EACJ,IAAKiC,EAAI,OAAO,EAChB,IAAMiE,EAAiC,OAA3BlG,EAAMqG,KAAKF,QAAQlE,SAAe,EAASjC,EAAIkG,GAC3D,OAAOA,QAAAA,GAAM,CACd,CACDE,OAAAA,CAAQF,GACN,OAAOG,KAAKC,UAAUnG,IAAI+F,IAAO,IAClC,CACDK,MAAAA,GACE,OAAO1C,MAAMC,KAAKuC,KAAKC,UAAUE,OAClC,CACDL,OAAAA,CAAQlE,GACN,OAAOoE,KAAKI,YAAYtG,IAAI8B,IAAO,IACpC,CAGDyE,iBAAAA,CAAkBzE,GAChB,IAAMiE,EAAKG,KAAKJ,MAAMhE,GACtBoE,KAAKC,UAAUK,OAAOT,GAClBjE,EAAGD,YACLC,EAAGD,WAAW4E,SACXC,GAAcR,KAAKK,kBAAkBG,IAG3C,CACDC,GAAAA,CAAIZ,GACF,OAAOG,KAAKC,UAAUQ,IAAIZ,EAC3B,CACDa,OAAAA,CAAQC,GACN,OAAOX,KAAKI,YAAYK,IAAIE,EAC7B,CACDC,GAAAA,CAAIhF,EAAIiF,GACN,IAAMhB,EAAKgB,EAAKhB,GAChBG,KAAKC,UAAUa,IAAIjB,EAAIjE,GACvBoE,KAAKI,YAAYU,IAAIlF,EAAIiF,EAC1B,CACD9C,OAAAA,CAAQ8B,EAAIjE,GACV,IAAMmF,EAAUf,KAAKD,QAAQF,GAC7B,GAAIkB,EAAS,CACX,IAAMF,EAAOb,KAAKI,YAAYtG,IAAIiH,GAC9BF,GAAMb,KAAKI,YAAYU,IAAIlF,EAAIiF,EACpC,CACDb,KAAKC,UAAUa,IAAIjB,EAAIjE,EACxB,CACDoF,KAAAA,GACEhB,KAAKC,UAA4B,IAAIP,IACrCM,KAAKI,YAA8B,IAAIT,OACxC,EA8QH,SAASwG,GAAkBxF,EAAO1B,EAAOmH,GACvC,IAAKzF,EAAO,OAAO,EACnB,GAAIA,EAAM/D,WAAa+D,EAAM9D,aAC3B,QAAKuJ,GACED,GAAkBzK,GAAQG,WAAW8E,GAAQ1B,EAAOmH,GAE7D,IAASC,IAAAA,EAAS1F,EAAM2F,UAAUhI,OAAQ+H,KAAY,CACpD,IAAME,EAAY5F,EAAM2F,UAAUD,GAClC,GAAIpH,EAAMsE,KAAKgD,GACb,OAAO,CAEV,CACD,QAAKH,GACED,GAAkBzK,GAAQG,WAAW8E,GAAQ1B,EAAOmH,EAC7D,CAi0BA,MAAI4L,GAEFtV,WAAAA,GACFmjC,gBAAA7/B,KAAA,gBAAA,MACA6/B,qBAA+B,aAAA,MAC/BA,qBAA6B,iBACzBA,gBAA8B7/B,KAAA,aAAA,MAClC6/B,gBAAgC7/B,KAAA,YAAA,MAChC6/B,gBAAkD7/B,KAAA,kBAAA,MAChD6/B,gBAAW7/B,KAAA,cAAA,MACX6/B,gBAAS7/B,KAAA,eAAA,GACR6/B,gBAAO7/B,KAAe,YAAe,GACxC6/B,gBAAqC7/B,KAAA,YACjC6/B,gBAAS7/B,KAAA,YACT6/B,gBAAwB7/B,KAAA,aAC5B,CACE,cAAArE,GAGE,IAFA,IAAMuW,EAAO,GACbC,OAAYC,WACPD,GACLD,EAAUvT,KAAAwT,GACRA,EAAYA,EAAA9D,YAEd,OAAI6D,CACF,CAAclW,QAAAA,CACf2E,GACD,KAAIA,aAAUqR,IACZ,OAAA,EAAA,GAAKrR,EAAA0R,gBAASrS,KAAAqS,cAAA,OAAA,EACf,GAAA1R,IAAAX,KAAA,OAAA,EACD,KAAIW,EAAO9E,YAAS,CAClB,GAAI8E,EAAA9E,aAA0BmE,KAAA,OAAA,EAC5BW,EAAKA,EAAO9E,UACZ,CAAc,OACtB,CACQ,CAEArB,WAAAA,CAAK8X,GACL,MAAA,IAAKC,MAER,8GACD,CAEEC,YAAAA,CAAMF,EAAAG,GACP,MAAA,IAAAF,MAEH,+GACE,CAEA3X,WAAAA,CAAI8X,GACF,MAAA,IAAKH,MAEP,8GACD,CACDxY,QAAAA,GACE,MAAK,QACL,EAEA,IAAA4Y,GAAI,CACFra,KAAA,CAAA,aAAiB,aAAyB,gBAAA,eAAAC,WAC3C,CAAA,OAAA,eACDC,QAAI,CAAA,aAAkB,gBAAO,oBAC7BC,iBAAY,IAEZma,GAAe,CACfta,KAAI,CAAM,WAAA,eACVC,WAAW,CAAA,gBACTC,QAAI,GACJC,iBAA0B,CAAA,gBAEhCoa,GAAW,CAAA,EACL,SAAAC,GAAwBpb,GAAA,GACzBmb,GAAAnb,GACD,OAAOmb,GAAsBnb,GAC3B,IAAAmB,EAAaG,WAAYtB,GACzB0B,EAAoBP,EAAMQ,UAC1BC,EAAe5B,KAAKib,GAAMA,GAAAjb,QAAA,EACxB6B,EAAoBC,QACpBF,GAAoEA,EACrEG,OACDC,IACC,IAAKC,EAAIC,EACb,OAAAJ,QACU,OAAXI,EAAW,OAAAD,EAAArC,OAAAuC,yBAAAT,EAAAM,SAAA,EAAAC,EAAAG,UAAA,EAAAF,EAAAG,WAAAC,SACL,iBAAA,KAIJC,EAAYvC,KAAOkb,GAAsBA,GAAAlb,QAAA,EAC1CwC,EAAAV,QACHS,GAAAA,EAAAR,OAEAU,IACI,IAAAR,EACJ,yBAAoBP,EAAgBe,KAAAR,OAAAA,EAAAP,EAAAe,SAAAR,EAAAA,EAAAI,WAAAC,SAAA,iBAAA,KAIlC,GAAAT,GAAaW,EAEb,OADA2Y,GAAenb,GAAAmB,EAAAQ,UACfR,EAAYQ,UAEZ,IACA,IAAOe,EAAAC,SAAAC,cAAA,UACPD,SAAAE,KAAaC,YAAAJ,GACb,IAAAK,EAAcL,EAAAM,cACd,IAAAD,EAAW,OAAA5B,EAAAQ,UACX,IAAQsB,EAAAF,EAAA/C,GAAA2B,UAEV,OADEgB,SAAWE,KAAAK,YAAAR,GACbO,EACSkY,GAAkBnb,GAAAiD,EAD3BvB,CAEE,CAAA,MAAAuQ,GACF,OAAAvQ,CACA,CAAwC,CAEpC,IAAA8Z,GAAe,CAAA,EAAA,SAChBC,GAAAzb,EAAAsD,EAAAtB,GACD,IAAAC,EACMsB,EAAavD,EAAMwD,IAAAA,OAAAxB,GACvB,GAAAwZ,GAAmBjY,GACnB,OAAIiY,GAAkBjY,GAAcE,KAClCH,GAEA,IAAAI,EAAQ0X,GAAApb,GACT2D,EAGL,OAHK1B,EAAArC,OAAAuC,yBACDuB,EACE1B,SACKC,EAAAA,EAAAG,IACL,OAAAuB,GACA6X,GAAajY,GAAgBI,EAC9BA,EAAAF,KAAAH,IAFuBA,EAAWtB,EAElC,CACF,IACD0Z,GAA2B,CAAA,EACzB,SAAIC,GAAA3b,EAAAsD,EAAAb,GACJ,IAAIc,EAAMvD,EAASwD,IAAAA,OAAQf,GACzB,GAAAiZ,GAAiBnY,GACvB,OAAAmY,GAA8BnY,GAAWO,KACnCR,GAEA,IACNS,EADuBqX,GAAyBpb,GACrCyC,GACL,MAAwB,mBAAxBsB,EAA2CT,EAAAb,IAAAiZ,GAC5CnY,GAAAQ,EACGA,EAAYD,KAAAR,GAChB,CAsCC,IACFuY,GAAA,CACD5X,WAvCE,SAAcC,GACZ,OAAAuX,UAASvX,EAAA,aACT,EAsCFC,WAtCY,SACXD,GACD,OAAIuX,GAAsB,OAAAvX,EAAA,aACxB,EAoCFE,cAnCE,SAAWF,GACT,OAAAuX,GAA0B,OAAAvX,EAAO,gBAAiB,EAmCtDG,YAlCG,SACFH,GACD,OAAAuX,UAAOvX,EAAA,cAAA,EAiCRI,SAhCA,SACYJ,EAAOK,GAClB,OAAIoX,GAA0B,OAAAzX,EAAA,WAA1ByX,CAAqCpX,EACzC,EA8BFC,YA7BE,SAAIN,GACJ,OAAIyX,GAAe,OAAYzX,EAAQ,cAAnCyX,EACF,EA4BFlX,KA3BE,SAAQP,GAAuB,OACrCA,GAAW,SAAAA,EACLuX,GAAwB,aAASvX,EAAW,QADvC,IACuC,EA0B9CQ,YAxBA,SAAgBR,GAChB,OAAKA,EAAAQ,WAAyB,EAwB9BC,WAvBD,SACWT,GACV,OAAAA,GAAW,eAAYA,EACvBuX,aAAiBvX,EAAA,cADe,IAE9B,EAoBAU,cAnBA,SAAQV,EAAAW,GAAA,OACT4W,GAAA,UAAAvX,EAAA,gBAAAuX,CAAA5W,EACD,EAiBoCC,iBAhBpC,SAAsBZ,EAAAW,GACpB,OAAA4W,GAAwB,UAAEvX,qBAA1BuX,CAA0B5W,EAC1B,EAgBFE,iBAfE,WACA,OAAAqW,GAAqC,oBAAApW,WAAS,GAqBhD,IAAAmX,GAAI,4NACJC,GAAK,CACLC,IAAI,CAAA,EACFnU,MAAA,KACAqM,QAAIjO,MAAO6V,KACZ,GAED9T,QAAIA,KACFkM,QAAKjO,MAAA6V,IACI,MAETxT,iBAAAA,GACE4L,QAAAjO,MAAO6V,GAAA,EACRpT,IACFA,KACDwL,QAAKjO,MAAQ6V,KACT,GAEJ7S,KAAAA,GACEiL,QAAAjO,MAAO6V,GAAqB,GAGhB,oBAAZvR,QAA+BA,OAAMqT,OAAArT,OAAAsT,UAAA9B,GAChC,IAAA6B,MAAA7B,GAAA,CACLha,IAAAA,CAAA4Z,EAAImC,EAASC,KACJ,QAALD,GACF5J,QAAQjO,MAAK6V,IAEb+B,QAAY9b,IAAA4Z,EAAOmC,EAAAC,OA0EvB,IAAAC,GAAYrB,KAAOD,IAkBf,SAAAsC,GAAgBpW,GAChB,OAAAA,EAGGA,EAAA/D,WAAA+D,EAAA9D,aAAA8D,EAAA4S,GAAAzX,cAAA6E,GAFJ,IAGJ,CAtBU,iBAAwB4C,KAAAmR,KAASD,MAAM1a,cAC5Cgc,GAAIA,KAAU,IAAgBrB,MAAaiD,WAmO5C,SACFQ,GAAAvc,GACF,IAAAjC,EACDye,EAAO,KAGL,MAFA,gBAAkBxc,WAAOjC,EAAA4Z,GAAArX,YAAAN,WAAAjC,EAAAiD,YAAAtE,KAAA+f,wBAAA9E,GAAApX,KAAAoX,GAAArX,YAAAN,MACzBwc,EAAa7E,GAAKpX,KAAAoX,GAAOrX,YAAUN,KACnCwc,CAAmC,CACpC,SACDI,GAAiB5c,GAGb,IAFF,IACAwc,EADAG,EAAe3c,EAEbwc,EAAWD,GAAeI,IAChCA,EAAoBH,EACd,OAAAG,CACA,CACA,SAAAD,GAAmB1c,GAAsD,IAC1E0D,EAAA1D,EAAAyW,cACD,IAAA/S,EAAO,OAAA,EACR,IAAA8Y,EAAAI,GAAA5c,GACD,OAAA2X,GAAAvX,SAAesD,EAAQ8Y,EACrB,CA4C0C,IAtCtC,IAAA0nB,GAAQxoC,OAAA0nB,OAAA1nB,OAAAC,eAAA,CAAAwoC,UACH,KACL3E,iBAjEuB,MACvB1+B,WAAAA,GAC0CuV,QACpB,KAAA,GAAgBA,yBACtCtS,SAAAsS,yBACRvS,IAAA,CAEIE,KAAAA,CAAAsJ,GAAW,IAAA2O,EACZ,OAAA,QAAAA,EAAAC,KAAAA,WAAAhe,IAAAoP,UAAA,IAAA2O,EAAAA,GAAA,CACD,CACEpX,GAAAA,CAAAyI,GAAO,OACDlJ,KAAA8X,WAAarX,IAAAyI,EACf,CAGmCtI,GAAAA,CAAAsI,EAC5BrJ,GACL,OAAAG,KAAAS,IAAOyI,GAAUtJ,KAAAA,MAAAsJ,IAGrB6O,OADC,IAAAlY,EACGG,KAAOH,KACCA,EACVG,KAAA8X,WAAUhX,IAAIoI,EAAA6O,GACd/X,KAAAgY,WAAIlX,IAASiX,EAAU7O,GACvB6O,GAPmB,IAClBA,CAOC,CAAeE,QAAAA,CAChBpY,GACD,OAAAG,KAAOgY,WAAAle,IAAA+F,IAAA,IAAA,CACRmB,KAAAA,GAEJhB,KAAA8X,eAAAnY,QACDK,KAAAgY,eAAYtY,IACVM,KAAIH,GAAK,CACP,CACAqY,UAAAA,GACA,OAAOlY,KAAAH,IACL,GA6BU,WACXiU,GACF,OAAAA,EACD,EACDiD,wBACDipB,iBA9HO,SACTA,EAAar/B,EAAAs/B,GACL,IAAAtmC,EAAAC,EACDsmC,EAAAtmC,OAAAA,EAAA,OAAAD,EAAAgH,EAAA0R,oBAAA,EAAA1Y,EAAAwpB,kBAAAvpB,EAAAA,EAAAsmC,aAAA,IACFA,GAAAA,IAAAD,EACF,MAAA,CACDxd,EAAA,EACEtY,EAAM,EACNg2B,cAAU,EACRC,cAAc,GAEZ,IACAC,EAAcH,EAAW7yB,wBACjCizB,EAAAN,EAAAE,EAAAD,GACKE,EAAAE,EAAAj2B,OAAA81B,EAAAvpB,aACD,MAAA,CACD8L,EAAA4d,EAAA5d,EAAA6d,EAAAH,cAAAG,EAAA7d,EACDtY,EAAAk2B,EAAWl2B,EAAAm2B,EAAAH,cAAAG,EAAAn2B,EACTg2B,gBACAC,cAAOE,EAAAF,cAAAD,EACR,EA2GCI,cAlGA,SAAAA,EAAOnjC,EAAAoc,GACR,IAAA9b,EAAAN,EAAAoc,EAAA,IACD,OAAuB,IAAvBA,EAAUlb,OACDZ,EAEF6iC,EACN7iC,EAAAJ,SAAAkc,EAAA,IAAAlc,SACDkc,EAAM/V,MACJ,GAEE,EAyFF+8B,qBAzF+B,SAC9BC,GACD,IAAAvZ,MAAOuZ,GACR9mB,EAAAuN,EAAArjB,MACD,MAAA,CAAAqjB,YAAW3T,MAAgBoG,EACzB,EAqFAnB,qBACDL,iBACD1B,gBA/PE,WACD,OAAAnU,OAAAoU,aAAArc,SAAAic,iBAAAjc,SAAAic,gBAAAK,cAAAtc,SAAAE,MAAAF,SAAAE,KAAAoc,YAAA,EA+PCX,gBAxQM,SAAUvb,GACR,IAAAd,EAAAC,EAAAqc,EAAAC,EAAmC5W,EACpC7E,EAAAJ,SACD,MAAA,CAAO8b,KACR7W,EAAA8W,iBAAA9W,EAAA8W,iBAAAnJ,gBAAA,IAAAxS,EAAA4b,YAAA5b,EAAA4b,YAAA/W,EAAAgX,gBAAArJ,aAAA,MAAA3N,OAAA,EAAAA,EAAA/E,QAAAZ,OAAAA,EAAA4Z,GAAAzX,cAAAwD,EAAA/E,YAAAZ,EAAAA,EAAAsT,cAAA,OAAArT,EAAA,MAAA0F,OAAA,EAAAA,EAAA/E,WAAA,EAAAX,EAAAqT,aAAA,EAAAsJ,IACFjX,EAAA8W,iBAAA9W,EAAA8W,iBAAAjJ,eAAA1S,IAAAA,EAAA+b,YAAA/b,EAAA+b,aAAAlX,MAAAA,OAAAA,EAAAA,EAAAgX,gBAAAnJ,mBAAA7N,SAAAA,EAAA/E,QAAA,OAAA0b,EAAA1C,GAAAzX,cAAAwD,EAAA/E,YAAA,EAAA0b,EAAA9I,aAAA,OAAA+I,EAAA,MAAA5W,OAAA,EAAAA,EAAA/E,WAAA,EAAA2b,EAAA/I,YAAA,EAEH,EAiQYyJ,eA7PZ,WACA,OAAAtU,OAAMuU,YAAkBxc,SAAAic,iBAAAjc,SAAAic,gBAAAQ,aAAAzc,SAAAE,MAAAF,SAAAE,KAAAuc,WACtB,EA4P0BY,cA/G5B,SAAiB9b,GACf,QAAAA,IACDA,aAAAoW,IAAA,eAAApW,EACMpC,QAAAoC,EAAAS,YAEF7C,QAAK+Z,GAAAlX,WAAAT,IACV,EA0G0BoZ,WA1TtB,SAAAA,EAAWtB,EAAKhc,EAAAud,EAAAC,EAAAza,QAAA,IAAAA,IAAAA,EAAA6H,QAChB,IAAA6S,EAAU1a,EAAAnD,OAASuC,yBAAO6Z,EAAAhc,GAe9B,OAdM+C,EAAAnD,OAAAC,eAAmCmc,EAErChc,EAAOwd,EACRD,EAAA,CACFnU,GAAAA,CAAAnJ,GACGyZ,YAAiB,KACnB6D,EAAAnU,IAAQ3F,KAAK6E,KAAIrI,EAAO,GACzB,GACCwd,GAAcA,EAAQrU,KACvBqU,EAAArU,IAAA3F,UAAAxD,EAEF,IAGC,IAAMqd,EAAWtB,EAAOhc,EAAAyd,GAAA,CAAA,GAAA,EACtB,EA2SFsD,MAtBA,SAAI7c,GACJ,IAAI0D,EAAA1D,EAAOyW,cACX,QAAA/S,IACEiU,GAAIvX,SAASsD,EAAM1D,IAAM0c,GAAA1c,GACvB,EAkBwBub,kBAxNzB,SACFA,EAAAzD,EAAAzM,GACD,GAplDIlK,GADcnB,EAqlDX8X,IAjlDD,SAAU9X,GAAM,SAAUA,GAAMF,GAAQS,KAAKP,IAAO,KAErDpC,QACLuD,GAAU,eAAgBA,GAAUrB,GAAQW,WAAWU,KAAYnB,GA+kDpE,OAAA,EAtlDH,IAAsBA,EACdmB,EAulDA8C,EAAAoH,EAAQrH,MAAU8T,GACtB,IAAAzM,EAAUxG,IAAAZ,GACV,OAAI,EACS,IACZoO,EAAAsF,GAAA1X,WAAA6X,GACD,QAAAzF,GAAOA,EAAArR,WAAA8W,EAAA3L,kBAGPkG,GAGGkJ,EAAOlJ,EAAAhH,GACV,EAwMmC+P,UAxPnC,SAAIrW,EAAAuG,EAAAC,EAAAf,GACJ,IAAAzF,EACE,OAAI,EAEF,IAAAkE,EAAIkS,GAAiBpW,GAAoB,IAC1CkE,EACP,OAAK,EAEF,IACD,GAAiB,iBAAPqC,EAAO,CACf,GAAIrC,EAAAyB,UAAWtK,SAAQkL,GAAkB,OAAA,EACzC,GAAId,GAAA,OAAAvB,EAAA8B,YAAAO,GAAA,OAAA,OAEF,GAAIf,GAAOtB,EAAAqC,EAAAd,GAAA,OAAA,CAET,CAAA,MAAAS,GACE,CACA,GAAAM,EAAQ,CACR,GAAAtC,EAAA+B,QAAQO,GAAuB,OAAA,EAC/B,GAAAf,GAAO,OAAAvB,EAAA8B,QAAAQ,GAAA,OAAA,CAAA,CACR,OACF,CAAA,EAoOqB8P,UA/NxB,SAAIrb,EAAAqL,EAAAgI,GACJ,kBAAArT,EAAAyF,UAAmB4N,EAAAiI,sBA7zCF,IAg0CbjQ,EAAIrH,MAAOhE,EAA8B,EA4NzC8iB,aAnOJ,SAAO9iB,EAAAqL,GACR,OAAA,IAAAA,EAAArH,MAAAhE,EAAA,EAmO0B2b,mBAnJrB,SAAa3b,EAAAqL,GACb,OAAAzN,QAA+B,WAAtBoC,EAAA4b,UAAyBvQ,EAAAnH,QAAAlE,GAChC,EAkJQ6b,uBAjJN,SAAkB7b,EAAOqL,GAAA,OAAAzN,QAEpB,SAALoC,EAAA4b,UAAiB5b,EAAAgB,WAAAhB,EAAAiB,cAAAjB,EAAA2P,cAAA,eAAA3P,EAAA2P,aAAA,QAAAtE,EAAAnH,QAAAlE,GAClB,EAgJP8kC,mBA7JuB,SACzBA,EAAuBC,EAAI5gB,GACzBA,EAAA4gB,EAAKhpC,OACL,IAAKmR,IAAAA,EAAA63B,EAAWC,SAAItiC,OAAA,EAAAwK,GAAA,EAAAA,IACpB43B,EAAeC,EAAAC,SAAA93B,GAAAiX,EAEb,EAwJA3I,oBA9MF,SAAeC,GACb,OAAA7d,QAAK6d,EAAQC,eAAqC,EA8MlD,gBAAAvB,GACA,OAAIA,EACF,EACAvC,GAxYsB,SAC3BlS,EAAAmS,EAAAC,QAAA,IAAAA,IAAAA,EAAArZ,UACD,IAAA0M,EAAgB,CAAA4M,SAAA,EAAAC,SAAA,GAEf,OADCF,EAAK1H,iBAAU1K,EAAAmS,EAAA1M,GAChB2M,IAAAA,EAAA/H,oBAAArK,EAAAmS,EAAA1M,EAAA,QA8EK,SAAAsO,EAAWjQ,EAAKkQ,GAChB,IACE,KAAAlQ,KAAQiQ,GAA2B,MACpC,OAGT,IAAKF,EAAAE,EAAAjQ,GACGmQ,EAAOD,EAAUH,GAWjB,MAVe,mBAATI,IACTA,EAAAlc,UAAiBkc,EAAAlc,WAAA,CAAA,EAChB/B,OAAAke,iBAAsBD,EAAS,CAChCE,mBAAA,CACD5d,YAAO,EACRF,MAAAwd,MAIGE,EAAIjQ,GAAGmQ,EACG,KACRF,EAAIjQ,GAAO+P,CAAU,CAE7B,CAAA,MAAAlC,GACI,MAAO,MAET,CACE,EA8RG4tB,SAjNF,SACFpmC,QAAA,IAAAA,IAAAA,EAAA6H,QACS,aAAO7H,IAAAA,EAAWyjC,SAAA7kC,UAAAkH,UAC1B9F,EAAIyjC,SAAM7kC,UAAakH,QAAA/C,MAAAnE,UAAAkH,SAEnB,iBAAA9F,IAAAA,EAA2B0jC,aAAa9kC,UAAAkH,UAAA9F,EAChD0jC,aAAA9kC,UAAAkH,QAAA/C,MAAAnE,UAAAkH,QAEI,EA2MEugC,oBA3M+B,SAClCC,GACH,IAAAC,EAAA,CAAA,EACIC,EAAgBA,CAAAvjB,EAAAzP,KACpB,IAAAizB,EAAgB,CACZvpC,MAAA+lB,EACJzP,SACM2yB,SAAU,IAGZ,OADJI,EAAAtjB,EAAA5B,KAAAjc,IAAAqhC,EACIA,CAAgB,EAEdC,EAAS,GACf,IAAI,IAAApb,KAAqBgb,EAAA,CACrB,IAAA/lB,OAAAA,EAAAb,SAAAA,GAAiB4L,EACjB,GAAA/K,GAAAA,KAAgBgmB,EAAhB,CACJ,IAASI,EAAYJ,EAAahmB,GAC5B,GAAAomB,EAAanzB,OAAI,CACrB,IAASozB,EAAMD,EAAKnzB,OAAA2yB,SAAAp9B,QAAA49B,GACdA,EAAQnzB,OAAU2yB,SAAA9K,OACpBuL,EACD,EACGJ,EAAOlb,EAAcqb,EAAAnzB,aAErB,CACA,IAAAozB,EAAOF,EAAY39B,QAAS49B,GAC9BD,EAAYrL,OAASuL,EAAIJ,EAAAA,EAAAlb,EAAA,MAC/B,CAEK,MACC,GAAA5L,KAAa6mB,EAAb,CACN,IAAWM,EAAAN,EAAA7mB,GACLmnB,EAAaV,SAAAjiC,KAAYsiC,EAAUlb,EAAMub,GAE1C,MACFH,EAAAxiC,KAAAsiC,EAAAlb,EAAA,MACD,CACF,OAAAob,CACA,EAsKQ7oB,mBAAQtE,SAvWP,SACFC,EAAAC,EAAAnN,QAAA,IAAAA,IAAAA,EAAA,CAAA,GAAA,IACFoN,EAAA,KACDC,EAAW,EACX,OAAqB,WAAA,IAAA,IAAAC,EAAAC,UAAAhW,OAANiW,EAAM/W,IAAAA,MAAA6W,GAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAND,EAAMC,GAAAF,UAAAE,GACrB,IAAAC,EAAOC,KAAAD,MACRL,IAAA,IAAArN,EAAA4N,UACDP,EAAeK,GAEb,IAAMG,EAAMV,GAAOO,EAAAL,GACbS,EAAY7U,KACd4U,GAAW,GAAGA,EAAeV,GAC3BC,IACA7C,aAAU6C,GACRA,EAAQ,MAEVC,EAAOK,EAAAR,EACRa,MAAAD,EAAAN,IACFJ,IAAA,IAAApN,EAAAgO,WACFZ,EAAA/C,YAAA,KACGgD,GAA6B,IAAtBrN,EAAQ4N,QAAuB,EAAAD,KAAAD,MAC1CN,EAAO,KACRF,EAAAa,MAAAD,EAAAN,EAAA,GACDK,GAEE,CACE,EA8UuB2sB,oBA5GzB,SAAwBjnB,GAG1B,IAFE,IAAAknB,MAAOnnB,IACRonB,EAAA,GACD34B,EAAYwR,EAAYhc,OAAIwK,KAAA,CAC1B,IAAIid,EAASzL,EAAWxR,GACnB04B,EAAA/gC,IAAOslB,EAAAlmB,MACZ4hC,EAAO9iC,KAAAonB,GACRyb,EAAA5gC,IAAAmlB,EAAAlmB,IAEC,CACE,OAAA4hC,CACA,GAkGqBC,OAC3BC,YAAA,CAAAhqC,MAAA,YAAAu7B,GAC6B,mEACrBC,GAAa,oBAALtG,kBAA8BA,WAAA,KACvCuG,KAAAA,GAAAF,GAAAE,KACDD,GAAAD,GAASG,WAAKD,KAAAA,GACN,IAeZpqB,GAZF1G,oBADyBA,QACzBA,OAAAu0B,UAAAA,KAAA,CADqBpC,IAAI5H,WAAApvB,KAAAs5B,KAAAtC,IAAA3uB,GAAAA,EAAAutB,WAAA,KACzByD,CAFkB,6jHAElB,CAAAx1B,KAAA,kCAAA,IAEQ,GAAuC,IAAvC9D,MAAAC,KAAW,CAAA,IAAAwM,GAAe,EAAfA,IAAe,GAAa,CAAA,IAC/CqyB,GAAsBjiC,SAAOC,cAAA,UACrBD,SAAAE,KAAMC,YAAK8hC,IAA6B9+B,MACzCC,MAAArG,OAAAA,GAAAklC,GAAA5hC,oBAAAtD,EAAAA,GAAAoG,MAAAC,OAAAD,MAAAC,KACFpD,SAAAE,KAAAK,YAAA0hC,GACD,CACE,CAAA,MAAA95B,WACD+5B,sCAAA/5B,EACD,CA9rDK,IAAIi6B,GAisDT,SAAID,GACJA,EAAAA,EAAAgD,WAAe,GAAM,aAAMhD,EAAWA,EAAAiD,QAAA,GAAA,UAAAjD,EAAAA,EAAAkD,QAAA,GAAA,SAAA,CADtC,CAED12B,KAAAA,GAAA,CAAA,IAAA,MACD44B,GACEllC,WAAAA,CAASjF,GACPD,GAAYwI,KAAgB,YAC7BxI,GAAAwI,KAAA,gBACDxI,GAAcwI,KAAA,cACdxI,GAAOwI,KAAA,gBACRA,KAAA6hC,SAAApqC,EAAAoqC,UAAA,GACD7hC,KAAA8hC,aAAsBrqC,EAAAqqC,cAAA,GACpB9hC,KAAI+hC,WAAatqC,EAAAsqC,WACf/hC,KAAAgiC,aAAevqC,EAAAuqC,YACf,CACAjoC,QAAAA,GACE,IAAAgoC,EAAc/hC,KAAM+hC,YAAA,GAClBC,OAAYA,cAAA,GAAA,OACbhiC,KAAA8hC,aACM9hC,KAAA8hC,aAAmB,KAAA9hC,KAAA6hC,SAAe,IAAAE,EAAA,IAAAC,EAAA,IACvChiC,KAAA6hC,SAAW,IAAAE,EAAA,IAAAC,CAAA,EAE6B,IAAAC,GACzC,eACFC,GAAA,iCACDC,GAAgB,8BACdC,GAAW,CAIhBC,MAAA,SAAArkC,GACD,OAAOA,OAMN,IAAAA,EAAAskC,iBAGC,IADItkC,EACJ,mBACSgC,KAAAuiC,WACLvkC,GAEGA,EAAO0F,OAAU1F,EAAA0F,MAAAjB,MAAoBy/B,IACxCM,KAAAA,YAAAxkC,GACDA,EAAA0F,MACG1D,KAAAyiC,gBAAqBzkC,IAEzBiO,QAAIC,KACF,yDACElO,GAEV,IArBQ,EAsBW,EAEV0kC,gBACQ,SAAOC,GAChB,IAA2B,IAA3BA,EAAMn/B,QAAQ,KACtB,MAAA,CAAAm/B,GAEQ,IACEh/B,EADmB,+BACT8B,KAAAk9B,EAAA5kC,QAAA,QAAA,KACV,IAAA4F,EAAA,MAAW,IAAA4O,iCAA4BowB,GACvC,MAAA,CAAAh/B,EAAA,GAAAA,EAAA,SAAA,EAAAA,EAAA,SAAA,EAAA,EAEF6+B,YAAM,SAAQxkC,GAGC,OAFFA,EAAA0F,MAAArF,MAAA,MAAAwe,QAAA,SAAA+lB,GAAA,QACXA,EAAAngC,MAAAy/B,GACA,GAAAliC,MACV+T,KAAA,SAAA6uB,GACAA,EAAap/B,QAAA,WAAA,IACLo/B,EAAMA,EAAI7kC,6BAAIA,QAAA,+BAAA,KAEjB,IAAA8kC,EAAAD,EAAA7kC,mBAAAA,QAAA,eAAA,KACGwE,EAAYsgC,EAAApgC,MAAA,4BAEfqgC,GADCD,EAAgBtgC,EAAUsgC,EAAa9kC,QAAAwE,EAA0B,GAAA,IAAAsgC,GAClExkC,aAAAoF,MAAA,GACMs/B,EAAAL,KAAAA,gBACRngC,EAAAA,EAAAugC,GAAAA,EAAAj/B,OAEMi+B,EAAiBgB,EAAAjlC,KAAA,WAAA,EACfgkC,GAAa,OAAI,eAAiBr+B,QAAAu/B,EAAmB,KAAA,OAAA,EAAAA,EAAA,GAC3D,OAAA,IAAAnB,GAAA,CACDE,eACDD,WACDE,WAASgB,EAAe,GAClBf,aAAae,EAAW,IAE5B,GAAA/iC,KACE,EAAWyiC,gBACZ,SAAAzkC,GAID,OAHOA,EAAA0F,MAAArF,MAAA,MAAAwe,QAAA,SAAA+lB,GACR,OAAAA,EAAAngC,MAAA0/B,GACI,GAAAniC,MACU+T,KAAM,SAAA6uB,GAOvB,GANIA,EAASp/B,QAAM,YAAiB,IAChCo/B,EAAOA,EAAQ7kC,QAChB,mDACG,SAGN6kC,IAAAA,EAAAp/B,QAAAo/B,OAAA,IAAAA,EAAAp/B,QAAA,KACI,OAAS,IAAAo+B,GAAA,CACJE,aAAUc,IAGjB,IAAYI,EAAU,6BAChBp8B,EAAYg8B,EAAOngC,MAAAugC,GACrBlB,EAAgBl7B,GAAiBA,KAAOA,EAAc,QAAA,EACvDm8B,EAAAL,KAAAA,gBACKE,EAAA7kC,QAAQilC,EAAA,KAEf,OAAA,IAAApB,GAAA,CACGE,eACFD,SAAYkB,EAAoB,GACjChB,WAAAgB,EAAA,GACHf,aAAAe,EAAA,IAGI,GAAA/iC,KACJ,EACEuiC,WAAQ,SAAOU,GACb,OAAIA,EAAAX,YAAKW,EAAA3S,QAAA9sB,QAAAy/B,OAAAA,GAAAA,EAAA3S,QAAAjyB,MAAA,MAAAC,OAAA2kC,EAAAX,WAAAjkC,MAAA,MAAAC,OACA0B,KAAAkjC,YAAAD,GACFA,EAAMv/B,MAGN1D,KAAAmjC,aAAAF,GAFCjjC,KAAAojC,aAAcH,EAI1B,EACAC,YAAI,SAAcD,GAId,IAHF,IAAII,EAAK,oCACLC,EAAKL,EAAA3S,QAAAjyB,MAAA,MACF0mB,EAAM,GACLwe,EAAA,EAAA16B,EAAcy6B,EAAKhlC,OAAWilC,EAAA16B,EAAM06B,GAAA,EAAA,CAC3C,IAAA9gC,EAAA4gC,EAAA59B,KAAA69B,EAAAC,IACM9gC,GACTsiB,EAAApmB,KACI,IAAAijC,GAAgB,CACdC,SAAAp/B,EAAmB,GACnBs/B,WAAYyB,WAAc/gC,EAAA,MAI5B,CACJ,OAASsiB,CACL,EACDqe,aAAA,SAAAH,GAIC,IAHJ,IAAAI,EAAA,6DACIC,EAAAL,EAAAX,WAAwBjkC,MAAY,MACtC0mB,EAAiB,GACNwe,EAAA16B,EAAAA,EAAQy6B,EAAOhlC,OAAAilC,EAAA16B,EAAA06B,GAAA,EAAA,CACxB,IAAK9gC,EAAA4gC,EAAkB59B,KAAA69B,EAAAC,IAClB9gC,GACDsiB,EAAOpmB,KACP,IAAAijC,GAAY,CACXE,aAAgBr/B,EAAW,SAAA,EACzBo/B,SAAUp/B,EAAK,GACrBs/B,WAAAyB,WAAA/gC,EAAA,MAIH,CACE,OAAKsiB,CACH,EAEFoe,aAAY,SAAAnlC,GAIZ,OAHDA,EAAA0F,MAAArF,MAAA,MAAAwe,QAAA,SAAA+lB,GACD,QAAaA,EAAAngC,MAAMw/B,MAAAW,EAAAngC,MAAA,oBACb,GAAAzC,MACU+T,KAAA,SAAA6uB,GACd,IAAIE,EAAaF,EAAAvkC,MAAA,KACb0kC,EAAML,KAAAA,gBAAAI,EAAAj/B,OAERi+B,GADkBgB,EAAS9mB,SAAS,IACLje,QAAO,uCAAwBA,QAAA,aAAA,UAAA,EAC/D,OAAA,IAAA6jC,GAAA,CACGE,eACFD,SAAOkB,EAAkB,GAC1BhB,WAAAgB,EAAA,GACGf,aAAWe,EAAW,IAE3B,GAAA/iC,KACD,GACwE,SACvEyjC,GAAA9iC,GACD,IAAAA,IAAWA,EAAA+iC,UACT,MAAI,GAGN,IADC,IAAAzhC,EAAA,GACDtB,EAAA7E,eAAoB,CAClB,IAAIsJ,EAAAzE,EAAWgjC,UACf,IAAKv+B,EACL,MAEAA,EAAIA,EAAQ1D,cACV,IAAAuM,EAAKtN,EAAa7E,cACnB8nC,EAAA,GACF,GAAA31B,EAAA2yB,UAAA3yB,EAAA2yB,SAAAtiC,OAAA,EACD,IAAS,IAAAilC,EAAM,EAAAA,EAAAt1B,EAAA2yB,SAAAtiC,OAAAilC,IAAA,CACb,IAAKM,EAAO51B,EAAY2yB,SAAI2C,GACxBM,EAAAF,WAAoBE,EAAAF,UAAAjiC,aACjBmiC,EAAUF,UAAAjiC,gBAAA0D,GACfw+B,EAAOjlC,KAAeklC,EAG1B,CAEMD,EAAMtlC,OAAA,IACR8G,GAAWw+B,OAAAA,EAASpgC,QAAU7C,GAAA,KACrBsB,EACfmD,GAAiBnD,EAAO,IAAAA,EAAS,IACzBtB,EAAIsN,CACJ,CACE,OAAAhM,CACA,CACE,SAAA6hC,GAAMrsC,GAAI,MAC0C,oBAD1CH,OACR+B,UAAAU,SAAAoB,KAAA1D,EAA4D,CAC1E,SAAAssC,GACWtsC,EAAAusC,GACD,GAAO,IAAPA,EAAO,OACR,EAED,IAAA7jC,EAAO7I,OAAA6I,KAAA1I,GAAmD,IAClE,IAAAC,KAAiByI,EACT,GAAA2jC,GAAOrsC,EAAKC,KAAAqsC,GAAAtsC,EAAAC,GAAAssC,EAAA,GACb,OAAU,EAGT,OAAM,CAAI,CACoD,SACtEvlC,GAAAhH,EAAAwsC,GAAA,IACOl9B,EAAA,CACPm9B,eAAoB,GACdC,aAAY,GAEZ7sC,OAAAiZ,OAAUxJ,EAAKk9B,GACf,IAAAvgC,EAAU,GACVvD,EAAO,GAAiB,OACzB3B,KAAAC,UACFhH,GACD,SAAUC,EAAAC,GACR,GAAK+L,EAAQpF,OAAO,EAAA,CACpB,IAAO8lC,EAAO1gC,EAAUF,QAAYxD,OACrCokC,EAAA1gC,EAAAoyB,OAAAsO,EAAA,GAAA1gC,EAAA/E,KAAAqB,OACDokC,EAAcjkC,EAAA21B,OAAAsO,EAAAC,IAAA3sC,GAAAyI,EAAAxB,KAAAjH,IACJgM,EAAOF,QAAA7L,KAEnBA,EADG+L,OAAA/L,EACH,eAE0B,eAAAwI,EAAAsD,MAAA,EAAAC,EAAAF,QAAA7L,IAAAkG,KAAA,KAAA,IAGpB,MACA6F,EAAQ/E,KAAAhH,GAEV,GAAqB,OAArBA,EAAqB,OAAAA,EACrB,QAAkB,IAAlBA,EAAkB,MAAA,YAClB,GA8BA,SAAgB2sC,GACd,GAAAR,GAAUQ,IAAAhtC,OAAgB6I,KAAAmkC,GAAUhmC,OAAIyI,EAAAm9B,eACzC,OAAA,EAEF,GAAA,mBAAAI,EACD,OAAM,EAEJ,GAAIR,GAAQQ,IAAOP,GAASO,EAAUv9B,EAAAo9B,cACpC,OAAI,EAEJ,OAAI,CACF,CAzCJI,CAAA5sC,GACA,OAyCI,SAAW2sC,GACX,IAAA1iC,EAAA0iC,EAASvqC,WACjBgN,EAAay9B,mBAAA5iC,EAAAtD,OAAAyI,EAAAy9B,oBACL5iC,EAAOA,EAAA6B,MAAMsD,EAAAA,EAAAy9B,mBAAA,OACE,OAChB5iC,CACD,CA/CF7H,CAAApC,GAEA,GAAwB,iBAAZA,EACd,OAAYA,EAAKoC,WAAW,IAExB,GAAApC,aAAgB8sC,MAAA,CACjB,IAAAC,EAAA,CAAA,EACD,IAAK,IAAU/iB,KAAAhqB,EAAA,CACX,IAAKgtC,EAAWhtC,EAAAgqB,GACbnkB,MAAS+0B,QAAAoS,GACTD,EAAW/iB,GAAW8hB,GACtBkB,EAAArmC,OAAAqmC,EACA,GAAA,MAEHD,EAAW/iB,GAAAgjB,CAEX,CACR,OAAaD,EACA,OAAA/sC,aAAmBW,KACzBX,aAAAitC,YACFjtC,EAAAA,EAAA+rC,UAAA,GAEK/rC,EAAU6f,SACN7f,aAAM4a,MACP5a,EAAM+L,MAAA/L,EAAA+L,MAAA/L,kCAAAA,EAAAyN,KAAAzN,KAAAA,EAAA24B,QAEN34B,IAsBL,CACA,IAAAktC,GAAc,CACdC,MAAgB,CAAA,SAEhB,QACA,QAAgB,aAExB,QACM,MACA,SACA,QACD,QACD,iBACA,WACE,OAAc,MAEZ,QAAsF,OACQ,UACvF,UACA,QACF,QAGPC,gBAAc,IAAAC,OACZ,WAC4C,SAC5CC,GAAYllB,EAAStlB,EAAAsM,GAA6C,IAOtEi+B,EANIE,EAAKn+B,EAAAzP,OAAAiZ,OAAA,CAAA,EAAAs0B,GAAA99B,GAAA89B,GACLM,EAAKD,EAAAF,OAAA,IAAAG,EACA,MACb,OAKQH,EADqB,iBAAnBG,EACF1qC,EAAQ0qC,GAEVA,EAEF,IAAAC,EAAO,EACRC,GAAA,EACDC,EAAmB,GACjB,GAAAJ,EAAIJ,MAAU9qC,SAAA,SAAA,CACd,IAAKigB,EAAK5C,IACR,IAAIiZ,EAAQjZ,EAASiZ,QAAUtyB,EAAAqZ,EAAArZ,MAC/BunC,EAAcnD,GAAgBC,MAAMrkC,GAAA+V,KACpCyxB,GAAgBA,EAAAzrC,aAEd2iB,EAAc,CAAAje,GAAI6xB,EAAA4U,EAAAjB,mBAClBlkB,EAAA,CACD+kB,MAAA,QACDS,QACN7oB,WACM,EAEFjiB,EAAAuR,iBAAuB,QAAAiO,GACvBqrB,EAAU3mC,MAAA,KACVlE,EAAIkR,oBAAoB,QAAAsO,EAAA,IAE5B,IAAAwrB,EAAWpuB,IACL,IAAIrZ,EACA0e,EACJrF,EAAOquB,kBAAWnzB,MAEhBmK,EACEje,CAAAA,GACmBT,0BAHrBA,EAAMqZ,EAAOquB,QAGoBtgC,KAAM,KAAIpH,EAAAsyB,QACzC4U,EAAYjB,oBAIbjmC,MAAAuU,MACFmK,GACFje,GAAA,wBAAAymC,EAAAjB,kBACDxlC,GAAO4Y,EAAAquB,OAAAR,EAAAjB,oBAGX,IAAAsB,EAAAnD,GAAAC,MAAArkC,GAAA+V,KACGyxB,GAAAA,EAAAzrC,aAECgmB,EAAI,CACF+kB,MAAO,QACRS,QACD7oB,WACD,EAECjiB,EAAIuR,iBAAkB,qBAAAy5B,GACtBH,EAAe3mC,MAAK,KACpBlE,EAAIkR,oBAAsC,qBAAA85B,EAAc,GAExD,CACA,IAAA,IAAIE,KAAOT,EAAsBJ,MAC/BQ,EAAK3mC,KAASZ,EAAAinC,EAAoBW,IAEpC,MAAI,KACJL,EAAI/kC,SAAkBuiB,GAAMA,KAAG,EACU,SAClC/kB,EAAA6nC,EAAAd,GAAA,IAAA7M,EAAAj4B,KACL,OAAA4lC,EAAUd,GAIXhF,GAAA1qB,MACDwwB,EACEd,GACA3vB,GACS,WAAS,IAAA,IAAA8kB,EAAA3lB,UAAAhW,OAATiW,EAAM/W,IAAAA,MAAAy8B,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAN3lB,EAAM2lB,GAAA5lB,UAAA4lB,GAEf,GADA/kB,EAAWL,MAAAmjB,EAAA1jB,KACY,WAAlBuwB,GAAkBvwB,EAAA,IAGnB8wB,GAAA,CAGFA,GAAU,EACX,IACF,IAAAE,EAAAnD,GAAAC,UAAA9vB,OAAAwB,KAAAyxB,GAAAA,EAAAzrC,aAAA+7B,OAAA,GAEWpZ,GAD2B,WAAjBooB,EAA4BvwB,EAAA9Q,MAAA,GAAA8Q,GACrBR,KACtBvU,GAAAf,GAAAe,EAAA0lC,EAAAjB,sBAEAmB,EACIF,EAAAH,gBACFhlB,EAAA,CACE+kB,QACFS,QACN7oB,YAEW0oB,IAAAF,EAAAH,iBACPhlB,EAAM,CACF+kB,MAAI,OACNS,MAAI,GACV7oB,SACFje,GAAA,sDAIM,OAAKT,GACbmX,EAAA,sBAAAnX,KAAAuW,EACH,CAAA,QACI8wB,GAAU,CACJ,CA5BV,CA6BI,IA3CS,MA8Cb,CACA,CACA,IACIQ,GAAY9+B,IAAA,CAChB3B,KAFsC,kBAGtCob,SAAIykB,GACJl+B,YC16EMtM,GAAkE,oBAAX6H,OAAyBA,YAASwjC,EAmFzFC,GAA8D,oBAAf/sC,WAA6BA,WAAayB,GAGlFurC,GADaxoC,MAAMnE,UACQkH,QAG3B0lC,GAAYF,MAAAA,QAAAA,EAAAA,GAAQE,UACTF,MAAAA,IAAAA,GAAQ1rC,SACR0rC,MAAAA,IAAAA,GAAQxjC,SACXwjC,MAAAA,IAAAA,GAAQG,YAEzBH,IAAAA,GAAQI,gBAAkB,oBAAqB,IAAIJ,GAAOI,gBAAmBJ,GAAOI,eACzDJ,MAAAA,IAAAA,GAAQK,gBACdH,MAAAA,IAAAA,GAAWI,UAC7B,IAAMC,GAAqC7rC,SAAAA,GAAQ,CAAU,EC5G9D8rC,GAAgB/oC,MAAM+0B,QACtBiU,GAAWlvC,OAAO+B,UACXotC,GAAiBD,GAASC,eACjC1sC,GAAWysC,GAASzsC,SAEbw4B,GACTgU,IACA,SAAU9uC,GACN,MAA8B,mBAAvBsC,GAASoB,KAAK1D,EACzB,EAKSivC,GAAcjkB,GAEH,mBAANA,EAYLqhB,GAAYrhB,GAEdA,IAAMnrB,OAAOmrB,KAAO8P,GAAQ9P,GAa1BkkB,GAAelkB,QAAqC,IAANA,EAE9CmkB,GAAYnkB,GAEM,mBAApB1oB,GAASoB,KAAKsnB,GAKZokB,GAAUpkB,GAEN,OAANA,EAOEqkB,GAAarkB,GAAsCkkB,GAAYlkB,IAAMokB,GAAOpkB,GAM5EskB,GAAatkB,GAEM,qBAArB1oB,GAASoB,KAAKsnB,GAGZukB,GAAcvkB,GAEhBA,aAAaxa,SAGXg/B,GAAcxkB,GAEhBA,aAAaykB,SCtElBC,GAAiBC,IACnB,IAAMpC,EAAiB,CACnBqC,EAAM,SAACvC,GACH,GACIxiC,IACiBgkC,GAA8B,gBAC9CK,GAAYrkC,GAAO2J,UACpB3J,GAAO2J,QACT,CAME,IALA,IAAMq7B,GACF,uBAAwBhlC,GAAO2J,QAAQ64B,GAChCxiC,GAAO2J,QAAQ64B,GAAmC,mBACnDxiC,GAAO2J,QAAQ64B,IAEzBzwB,EAAAC,UAAAhW,OAZmCiW,MAAI/W,MAAA6W,EAAAA,EAAAA,OAAAG,EAAA,EAAAA,EAAAH,EAAAG,IAAJD,EAAIC,EAAAF,GAAAA,UAAAE,GAavC8yB,EAAWF,KAAW7yB,EAC1B,CACH,EAEDgzB,KAAM,WAAoB,IAAA,IAAAtN,EAAA3lB,UAAAhW,OAAhBiW,EAAI/W,IAAAA,MAAAy8B,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ3lB,EAAI2lB,GAAA5lB,UAAA4lB,GACV8K,EAAOqC,EAAK,SAAU9yB,EACzB,EAEDrI,KAAM,WAAoB,IAAA,IAAAspB,EAAAlhB,UAAAhW,OAAhBiW,EAAI/W,IAAAA,MAAAg4B,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJlhB,EAAIkhB,GAAAnhB,UAAAmhB,GACVuP,EAAOqC,EAAK,UAAW9yB,EAC1B,EAEDvW,MAAO,WAAoB,IAAA,IAAAo4B,EAAA9hB,UAAAhW,OAAhBiW,EAAI/W,IAAAA,MAAA44B,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ9hB,EAAI8hB,GAAA/hB,UAAA+hB,GACX2O,EAAOqC,EAAK,WAAY9yB,EAC3B,EAEDizB,SAAU,WAAoB,IAAA,IAAAC,EAAAnzB,UAAAhW,OAAhBiW,EAAI/W,IAAAA,MAAAiqC,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJnzB,EAAImzB,GAAApzB,UAAAozB,GAGdz7B,QAAQjO,MAAMopC,KAAW7yB,EAC5B,EAEDozB,qBAAuBC,IACnB5C,EAAOhnC,MAAoD4pC,8CAAAA,EAAa,EAG5EC,aAAeC,GAA6BX,GAAiBC,MAAUU,IAE3E,OAAO9C,CAAM,EAKJ6C,GAFSV,GAAc,gBAEDU,aCzD7BE,GAAmB,CAAE,EAyBpB,SAASC,GAAKvwC,EAAUwwC,EAAoDvd,GAC/E,IAAIoc,GAAUrvC,GAAd,CAGA,GAAI86B,GAAQ96B,GACR,OA5BD,SACHA,EACAwwC,EACAvd,GAEA,GAAI6H,GAAQ96B,GACR,GAAIuuC,IAAiBvuC,EAAI8I,UAAYylC,GACjCvuC,EAAI8I,QAAQ0nC,EAAUvd,QACnB,GAAI,WAAYjzB,GAAOA,EAAI6G,UAAY7G,EAAI6G,OAC9C,IAAK,IAAIilC,EAAI,EAAG2E,EAAIzwC,EAAI6G,OAAQilC,EAAI2E,EAAG3E,IACnC,GAAIA,KAAK9rC,GAAOwwC,EAAS9sC,KAAKuvB,EAASjzB,EAAI8rC,GAAIA,KAAOwE,GAClD,MAKpB,CAYeI,CAAU1wC,EAAKwwC,EAAUvd,GAEpC,GAAIuc,GAAWxvC,IACX,IAAK,IAAM2wC,KAAQ3wC,EAAI6zB,UACnB,GAAI2c,EAAS9sC,KAAKuvB,EAAS0d,EAAK,GAAIA,EAAK,MAAQL,GAC7C,YAKZ,IAAK,IAAMrwC,KAAOD,EACd,GAAIgvC,GAAetrC,KAAK1D,EAAKC,IACrBuwC,EAAS9sC,KAAKuvB,EAASjzB,EAAIC,GAAMA,KAASqwC,GAC1C,MAfZ,CAmBJ,CC5BO,IAAMM,GAAkB,SAAUC,EAA0CC,GAC/E,IAAIC,EACAC,OAFwF,IAAbF,IAAAA,EAAgB,KAG/F,IAAMG,EAAoB,GAa1B,OAXAV,GAAKM,GAAU,SAAUK,EAAgCjxC,GAEjDivC,GAAYgC,IAAQhC,GAAYjvC,IAAgB,cAARA,IAI5C8wC,EAAUI,mBAA0BD,aHsDpBE,KGtD2BF,EAAIvjC,KAAOujC,EAAI5uC,YAC1D0uC,EAAUG,mBAAmBlxC,GAC7BgxC,EAAQA,EAAQpqC,QAAUmqC,EAAU,IAAMD,EAC9C,IAEOE,EAAQ7qC,KAAK0qC,EACxB,ECpCO,SAASnzB,GACZC,EACAjQ,EACAkQ,GAEA,IACI,KAAMlQ,KAAQiQ,GACV,MAAO,OAKX,IAAMF,EAAWE,EAAOjQ,GAClBmQ,EAAUD,EAAYH,GAiB5B,OAbIuxB,GAAWnxB,KAEXA,EAAQlc,UAAYkc,EAAQlc,WAAa,CAAE,EAC3C/B,OAAOke,iBAAiBD,EAAS,CAC7BuzB,oBAAqB,CACjBjxC,YAAY,EACZF,OAAO,MAKnB0d,EAAOjQ,GAAQmQ,EAER,KACHF,EAAOjQ,GAAQ+P,CAAQ,CAE9B,CAAC,MAAAta,GACE,MAAO,MAKX,CACJ,CC7BO,SAASkuC,GAAiB3mC,EAA6B2E,GAA+B,IAAAiiC,EACnFC,EAfV,SAAyB7mC,GACrB,IACI,MAAmB,iBAARA,EACA,IAAIC,IAAID,GAAK6mC,SAEpB,QAAS7mC,EACF,IAAIC,IAAID,EAAIA,KAAK6mC,SAErB7mC,EAAI6mC,QACd,CAAC,MAAApuC,GACE,OAAO,IACX,CACJ,CAGqBquC,CAAgB9mC,GAC3B+mC,EAAmB,CAAEF,WAAUG,cAAc,GAEnD,GAAgC,OAA5BJ,EAACjiC,EAAQsiC,uBAARL,EAA6B1qC,cAAW2qC,IAAAA,EAAUvkC,OAAOpG,OAC1D,OAAO6qC,EAGX,IAAK,IAAMG,KAAQviC,EAAQsiC,oBACvB,GAAIJ,EAASvnB,SAAS4nB,GAClB,MAAO,CAAEL,WAAUG,cAAc,GAIzC,OAAOD,CACX,CCpBO,IAAMI,GAAwD,CACjEC,eAAgB,CACZ,QACA,SACA,OACA,MACA,aACA,QACA,QACA,QACA,SACA,OACA,QACA,MACA,QACA,OACA,aACA,SACA,OACA,SACA,QACA,QACA,kBAEJC,cAAgB/+B,GAAiCA,EACjDg/B,eAAe,EACfC,YAAY,EACZC,uBAAuB,EACvBC,mBAAmB,EACnBC,8BAA+B,CAE3B,cAGA,aACA,QACA,YAEJC,sBAAuB,IACvBV,oBAAqB,CACjB,gBACA,oBACA,cAEA,uBACA,oBC3BFrE,GAAS6C,GAAa,cAStBmC,GAAsBC,GACJ,eAApBA,EAAMC,UACJC,GAAoBF,GAAoF,aAApBA,EAAMC,UAgBhG,SAASE,GAAwBrqB,EAAqBtlB,EAAcsM,GAOhE,GAAIA,EAAQ6iC,sBAAuB,CAC/B,IAAMS,EAA4B5vC,EAAI6vC,YACjCC,aACA1tB,QACIotB,GACGD,GAAmBC,IAClBE,GAAiBF,IAAUljC,EAAQyiC,eAAexvC,SAASiwC,EAAMO,iBAE9EzqB,EAAG,CACC0qB,SAAUJ,EAA0BK,SAAST,GACzCU,GAAe,CAAEV,QAAO9vC,YAAQ2rC,EAAW8E,YAAQ9E,EAAW+E,eAAgB,CAAE,EAAEC,WAAW,MAEjGA,WAAW,GAEnB,CACA,IAAMtqB,EAAW,IAAI/lB,EAAIswC,qBAAqBzf,IAI1C,IAKM0f,EAAqB1f,EAAQif,aAAa1tB,QAC3CotB,GACGD,GAAmBC,IAClBE,GAAiBF,IACdljC,EAAQyiC,eAAexvC,SAASiwC,EAAMO,gBATlBP,KAC5BljC,EAAQ4iC,aAAc5iC,EAAQ2iC,eACA,mBAAxBO,EAAMO,eAA8D,UAAxBP,EAAMO,cAShDS,CAAuBhB,KAGnClqB,EAAG,CACC0qB,SAAUO,EAAmBN,SAAST,GAClCU,GAAe,CAAEV,QAAO9vC,YAAQ2rC,EAAW8E,YAAQ9E,EAAW+E,eAAgB,CAAA,OAEpF,IAIAK,EAAaH,oBAAoBI,oBAAoBtuB,QAAQ4F,GAC/D1b,EAAQ+iC,8BAA8B9vC,SAASyoB,KAInD,OADAjC,EAASE,QAAQ,CAAEwqB,eACZ,KACH1qB,EAASoO,YAAY,CAE7B,CAEA,SAASwc,GAAoB9pC,EAA8BooC,GACvD,QAASA,IAAkB3C,GAAU2C,IAAkBA,EAAcpoC,GACzE,CAEO,SAAS+pC,GAAgBnqC,GAU7B,IAV8BI,KAC7BA,EAAIqoC,WACJA,EAAU2B,QACVA,EAAOlpC,IACPA,GAMHlB,EACG,SAASqqC,EAAmBC,GACxB,IAAMC,EAAoBn0C,OAAO6I,KAAKmrC,GAAS/rC,MAAM7H,GAA8B,iBAAtBA,EAAIgK,gBAC3DgqC,EAAcD,GAAqBH,EAAQG,GACjD,OAAOD,EAAa5gC,MAAM+gC,GAAOD,MAAAA,OAAAA,EAAAA,EAAa1xC,SAAS2xC,IAC3D,CAwBA,IAAKhC,EAAY,OAAO,EACxB,GAjBA,SAASiC,EAAUxpC,GACf,IACI,MAAmB,iBAARA,EACAA,EAAI6C,WAAW,SAEtB7C,aAAeC,IACS,UAAjBD,EAAIypC,SAEXzpC,aAAe0pC,SACRF,EAAUxpC,EAAIA,IAG5B,CAAC,MAAAvH,GACE,OAAO,CACX,CACJ,CAEI+wC,CAAUxpC,GAAM,OAAO,EAC3B,GAAI2kC,GAAU4C,GAAa,OAAO,EAClC,GAAIpX,GAAQoX,GAAa,OAAO4B,EAAmB5B,GACnD,IAAMoC,EAAiBpC,EAAWroC,GAClC,OAAIylC,GAAUgF,GAAwBA,EAC/BR,EAAmBQ,EAC9B,CAAC,SAEcC,GAA0BvS,EAAAwS,EAAAC,EAAAC,EAAAC,EAAAC,GAAA,OAAAC,GAAAx3B,MAAA9U,KAAAsU,UAAA,CA4BzC,SAAAg4B,KAFC,OAEDA,GAAArT,GA5BA,UACIx+B,EACA+vC,EACApoC,EACA4rB,EACAC,EACAse,GAEA,QAFO,IAAPA,IAAAA,EAAU,GAENA,EAAU,GAEV,OADAvH,GAAO94B,KAAK,8CAA+C,CAAE9J,MAAKooC,kBAC3D,KAEX,IACMgC,EArIH,SAAqBC,EAAiBC,GAEzC,IADA,IACSnJ,EADMkJ,EAAMnuC,OACC,EAAGilC,GAAK,EAAGA,GAAK,EAClC,GAAImJ,EAAUD,EAAMlJ,IAChB,OAAOkJ,EAAMlJ,EAIzB,CA6H6BoJ,CADKlyC,EAAI6vC,YAAYsC,iBAAiBxqC,IAG1D6nC,GACGE,GAAiBF,IACjBA,EAAMO,gBAAkBA,IACvB7D,GAAY3Y,IAAUic,EAAM4C,WAAa7e,KACzC2Y,GAAY1Y,IAAQgc,EAAM4C,WAAa5e,KAEhD,OAAKue,UACK,IAAIM,SAASC,GAAY37B,WAAW27B,EAAS,GAAKR,KACjDP,GAA2BvxC,EAAK+vC,EAAepoC,EAAK4rB,EAAOC,EAAKse,EAAU,OAGxFD,GAAAx3B,MAAA9U,KAAAsU,UAAA,CASD,SAAS04B,GAAe7qC,GAQN,IARO5H,KACrBA,EAAIwM,QACJA,EAAO3E,IACPA,GAKHD,EACG,GAAI2kC,GAAUvsC,GACV,OAAO,KAGX,IAAM0uC,SAAEA,EAAQG,aAAEA,GAAiBL,GAAiB3mC,EAAK2E,GACzD,GAAIqiC,EACA,OAAOH,EAAW,mBAGtB,GAAIrC,GAASrsC,GACT,OAAOA,EAGX,GAAIysC,GAAWzsC,GACX,OAAOA,EAAKwB,YAGhB,GAAIkrC,GAAW1sC,GACX,OAAO8tC,GAAgB9tC,GAG3B,GAAIupC,GAASvpC,GACT,IACI,OAAOiE,KAAKC,UAAUlE,EACzB,CAAC,MAAAoP,GACE,MAAO,qDACX,CAGJ,MAAO,4CAA8C5P,SAASoB,KAAKZ,EACvE,CA+HA,IAAM0yC,GAAuB51B,IACxBwvB,GAAOxvB,KAA+B,eAApBA,EAAM6yB,WAAkD,aAApB7yB,EAAM6yB,WAEjE,SAASS,GAAc9jB,GAsBM,IAtBLojB,MACpBA,EAAK9vC,OACLA,EAAMywC,OACNA,EAAMC,eACNA,EAAcC,UACdA,EAAS9c,MACTA,EAAKC,IACLA,EAAG7rB,IACHA,EAAGooC,cACHA,GAaH3jB,EACGmH,EAAQic,EAAQA,EAAM4C,UAAY7e,EAClCC,EAAMgc,EAAQA,EAAMiD,YAAcjf,EAMlC,IAAMkf,EAAa3iC,KAAK4iC,MAAM14B,KAAKD,MAAQ61B,YAAY71B,OAIjDuc,EAAYxmB,KAAK4iC,MAAMD,GAAcnf,GAAS,IAI9Cyc,EAAqC,CAAAjoB,KAFzBynB,EAAQA,EAAMoD,SAAW,CAAEjoC,KAAMhD,GAI/B,CACZyqC,UAAWlG,GAAY3Y,QAAS8X,EAAYt7B,KAAK8iC,MAAMtf,GACvDuf,QAAS5G,GAAY1Y,QAAO6X,EAAYt7B,KAAK8iC,MAAMrf,GACnDkf,aACAnc,YACA72B,OAAQA,EACRqwC,cAAeA,IAAgCP,EAASA,EAAMO,mBAAkC1E,GAChG8E,SACA4C,eAAgB3C,EAAe2C,eAC/BC,YAAa5C,EAAe4C,YAC5BC,gBAAiB7C,EAAe6C,gBAChCC,aAAc9C,EAAe8C,aAC7B7C,eAIR,GAAImC,GAAoBhD,GACpB,IAAK,IAAM2D,KAAU3D,EAAM4D,cAAgB,GACvCpD,EAAS9rC,KAAK,CACVwuC,aACAnc,YACA6b,UAAWriC,KAAK8iC,MAAMrD,EAAM4C,WAC5BznC,KAAMwoC,EAAOxoC,KACb0oC,SAAUF,EAAOE,SAMjB5D,UAAW,iBAKvB,OAAOO,CACX,CAEA,IAAMsD,GAA4B,CAAC,SAAU,UA+B7C,SAASC,GAAaC,GAGlB,OAAO,IAAInB,SAAQ,CAACC,EAASmB,KACzB,IAAM/5B,EAAU/C,YAAW,IAAM27B,EAAQ,sDAAsD,KAC/F,IACIkB,EAAEE,QACG3sC,OACA4sC,MACIC,GAAQtB,EAAQsB,KAChB3I,GAAWwI,EAAOxI,KAEtB4I,SAAQ,IAAMh9B,aAAa6C,IACnC,CAAC,MAAAlB,GACE3B,aAAa6C,GACb44B,EAAQ,sCACZ,IAER,CAEkC,SAAAwB,KAejC,OAfiCA,GAAAtV,GAAlC,UAAAjW,GAQoB,IAReirB,EAC/BA,EAAClnC,QACDA,EAAO3E,IACPA,GAKH4gB,GACSimB,SAAEA,EAAQG,aAAEA,GAAiBL,GAAiB3mC,EAAK2E,GACzD,OAAIqiC,EACO0D,QAAQC,QAAQ9D,EAAW,oBAG/B+E,GAAaC,OACvBn5B,MAAA9U,KAAAsU,UAAA,CAEkC,SAAAk6B,KAelC,OAfkCA,GAAAvV,GAAnC,UAAAhR,GAQoB,IARgBgmB,EAChCA,EAAClnC,QACDA,EAAO3E,IACPA,GAKH6lB,EACSwmB,EA3EV,SAAwCxtB,GAQtB,IAAAytB,GARuBT,EACrCA,EAAClnC,QACDA,EAAO3E,IACPA,GAKH6e,EACG,GAA2C,YAAvCgtB,EAAE3C,QAAQxxC,IAAI,qBACd,MAAO,6CAKX,IAAM4xC,EAA2C,OAAhCgD,EAAGT,EAAE3C,QAAQxxC,IAAI,sBAAe,EAA7B40C,EAA+BhtC,cAC7CitC,EAAsBZ,GAA0BnjC,MAAMw8B,GAAsB,MAAXsE,OAAW,EAAXA,EAAazmC,WAAWmiC,KAC/F,GAAIsE,GAAeiD,EACf,MAAA,gBAAuBjD,EAAW,oBAGtC,IAAMzC,SAAEA,EAAQG,aAAEA,GAAiBL,GAAiB3mC,EAAK2E,GACzD,OAAIqiC,EACOH,EAAW,mBAGf,IACX,CAgDgD2F,CAAgC,CAAEX,IAAGlnC,UAAS3E,QAC1F,OAAKykC,GAAO4H,GAILT,GAAaC,GAHTnB,QAAQC,QAAQ0B,OAI9B35B,MAAA9U,KAAAsU,UAAA,CAED,SAASu6B,GACL9uB,EACAtlB,EACAsM,GAEA,IAAKA,EAAQyiC,eAAexvC,SAAS,SACjC,MAAO,OAIX,IAAM80C,EAAuB1D,GAAoB,UAAWrkC,EAAQ2iC,eAC9DqF,EAAwB3D,GAAoB,WAAYrkC,EAAQ2iC,eAIhEsF,EAAe55B,GAAM3a,EAAK,SAAUw0C,GACtC,WAAA,IAAA/mB,EAAA+Q,GAAO,UAAgB72B,EAAwB2c,GAG3C,IACImwB,EAEAlhB,EACAC,EAJEkhB,EAAM,IAAIrD,QAAQ1pC,EAAK2c,GAEvB8rB,EAAkD,CAAE,EAI1D,IACI,IAAM2C,EAA0B,CAAE,EAClC2B,EAAI7D,QAAQ/qC,SAAQ,CAAC5I,EAAOy3C,KACxB5B,EAAe4B,GAAUz3C,CAAK,IAE9Bm3C,IACAjE,EAAe2C,eAAiBA,GAGhCnC,GAAiB,CACb/pC,KAAM,UACNgqC,QAASkC,EACTprC,MACAunC,WAAY5iC,EAAQ4iC,eAGxBkB,EAAe4C,kBA7ElC,SAEiC4B,GAAA,OAAAd,GAAAz5B,MAAA9U,KAAAsU,UAAA,CA2EqBg7B,CAAoB,CAAErB,EAAGkB,EAAKpoC,UAAS3E,SAG9E4rB,EAAQvzB,EAAI6vC,YAAY71B,MACxBy6B,QAAYD,EAAcE,GAC1BlhB,EAAMxzB,EAAI6vC,YAAY71B,MAEtB,IAAMi5B,EAA2B,CAAE,EAkBnC,OAjBAwB,EAAI5D,QAAQ/qC,SAAQ,CAAC5I,EAAOy3C,KACxB1B,EAAgB0B,GAAUz3C,CAAK,IAE/Bo3C,IACAlE,EAAe6C,gBAAkBA,GAGjCrC,GAAiB,CACb/pC,KAAM,WACNgqC,QAASoC,EACTtrC,MACAunC,WAAY5iC,EAAQ4iC,eAGxBkB,EAAe8C,mBAlFlC,SAEkC4B,GAAA,OAAAf,GAAA15B,MAAA9U,KAAAsU,UAAA,CAgFqBk7B,CAAqB,CAAEvB,EAAGiB,EAAKnoC,UAAS3E,SAGzE8sC,CACX,CAAU,QACNlD,GAA2BvxC,EAAK,QAAS00C,EAAI/sC,IAAK4rB,EAAOC,GACpDmgB,MAAMnE,IAAU,IAAAwF,EACPhF,EAAWE,GAAe,CAC5BV,QACA9vC,OAAQg1C,EAAIh1C,OACZywC,OAAQ6E,OAAFA,EAAEP,QAAAO,EAAAA,EAAK7E,OACbC,iBACA7c,QACAC,MACA7rB,IAAK+sC,EAAI/sC,IACTooC,cAAe,UAEnBzqB,EAAG,CAAE0qB,YAAW,IAEnBiF,OAAM,QAGf,KACH,OAAAC,SAAAA,EAAAC,GAAA,OAAA1nB,EAAApT,MAAA9U,KAAAsU,UAAA,CAAA,CAtED,KAwEJ,MAAO,KACH06B,GAAc,CAEtB,CAEA,IAAIa,GAA6C,KAEjD,SAASC,GACLphB,EACAj0B,EACAsM,GAEA,KAAM,gBAAiBtM,GACnB,MAAO,OAKX,GAAIo1C,GAEA,OADA7K,GAAO94B,KAAK,uDACL,OAKX,IAAM6jC,EACFhpC,EAAUzP,OAAOiZ,OAAO,CAAA,EAAIg5B,GAAuBxiC,GAAWwiC,GAG5DxpB,EAAuBrV,IACzB,IAAM+/B,EAAqC,GAC3C//B,EAAK+/B,SAASlqC,SAASyvC,IACnB,IAAMC,EAAgBF,EAAetG,cAAcuG,GAC/CC,GACAxF,EAAS9rC,KAAKsxC,EAClB,IAGAxF,EAASnsC,OAAS,GAClBowB,EAAQlM,EAAA,CAAA,EAAM9X,EAAI,CAAE+/B,aACxB,EAEEyF,EAAsB9F,GAAwBrqB,EAAItlB,EAAKs1C,GAGzDI,EAA+BA,OAC/BC,EAAiCA,OAWrC,OAVIL,EAAerG,eAAiBqG,EAAepG,cAC/CwG,EA1aR,SAAyBpwB,EAAqBtlB,EAAcsM,GACxD,IAAKA,EAAQyiC,eAAexvC,SAAS,kBACjC,MAAO,OAIX,IAAM80C,EAAuB1D,GAAoB,UAAWrkC,EAAQ2iC,eAC9DqF,EAAwB3D,GAAoB,WAAYrkC,EAAQ2iC,eAEhEsF,EAAe55B,GACjB3a,EAAI0rC,eAAe9sC,UACnB,QAGCg3C,GACU,SACHl2C,EACAiI,EACAkuC,EACAC,EACAtS,QAFK,IAALqS,IAAAA,GAAQ,GAOR,IAMItiB,EACAC,EAPEuiB,EAAMxwC,KAINmvC,EAAM,IAAIrD,QAAQ1pC,GAClByoC,EAAkD,CAAE,EAIpD2C,EAA0B,CAAE,EAC5BiD,EAA2BD,EAAIE,iBAAiBl1C,KAAKg1C,GAC3DA,EAAIE,iBAAmB,CAACtB,EAAgBz3C,KACpC61C,EAAe4B,GAAUz3C,EAClB84C,EAAyBrB,EAAQz3C,IAExCm3C,IACAjE,EAAe2C,eAAiBA,GAGpC,IAAMmD,EAAeH,EAAII,KAAKp1C,KAAKg1C,GACnCA,EAAII,KAAQr2C,IAEJ8wC,GAAiB,CACb/pC,KAAM,UACNgqC,QAASkC,EACTprC,MACAunC,WAAY5iC,EAAQ4iC,eAGxBkB,EAAe4C,YAAcT,GAAgB,CAAEzyC,OAAMwM,UAAS3E,SAElE4rB,EAAQvzB,EAAI6vC,YAAY71B,MACjBk8B,EAAap2C,IAMxBi2C,EAAIxkC,iBAAiB,oBAAoB,KACrC,GAAIwkC,EAAIv/B,aAAeu/B,EAAIK,KAA3B,CAGA5iB,EAAMxzB,EAAI6vC,YAAY71B,MACtB,IAAMi5B,EAA2B,CAAE,EAChB8C,EAAIM,wBACIpsC,OAAOrG,MAAM,WAChCkC,SAASqiC,IACb,IAAMj/B,EAAQi/B,EAAKvkC,MAAM,MACnB+wC,EAASzrC,EAAMqY,QACfrkB,EAAQgM,EAAM9F,KAAK,MACrBuxC,IACA1B,EAAgB0B,GAAUz3C,EAC9B,IAEAo3C,IACAlE,EAAe6C,gBAAkBA,GAGjCrC,GAAiB,CACb/pC,KAAM,WACNgqC,QAASoC,EACTtrC,MACAunC,WAAY5iC,EAAQ4iC,eAGxBkB,EAAe8C,aAAeX,GAAgB,CAAEzyC,KAAMi2C,EAAIO,SAAUhqC,UAAS3E,SAEjF4pC,GAA2BvxC,EAAK,iBAAkB00C,EAAI/sC,IAAK4rB,EAAOC,GAC7DmgB,MAAMnE,IACH,IAAMQ,EAAWE,GAAe,CAC5BV,QACA9vC,OAAQA,EACRywC,OAAQ4F,MAAAA,OAAAA,EAAAA,EAAK5F,OACbC,iBACA7c,QACAC,MACA7rB,IAAKA,EAAIrI,WACTywC,cAAe,mBAEnBzqB,EAAG,CAAE0qB,YAAW,IAEnBiF,OAAM,QAxCX,CA0CM,IAGVW,EAAal1C,KAAKq1C,EAAKr2C,EAAQiI,EAAKkuC,EAAOC,EAAUtS,EACxD,IAGT,MAAO,KACH+Q,GAAc,CAEtB,CAoTsBgC,CAAgBjxB,EAAItlB,EAAKs1C,GACvCK,EAAgBvB,GAAkB9uB,EAAItlB,EAAKs1C,IAG/CF,GAAqBA,KACjBK,IACAC,IACAC,GAAe,CAGvB,CAIO,IAKMa,GAA4ElqC,IAC9E,CACH3B,KAP2B,kBAQ3Bob,SAAUsvB,GACV/oC,QAASA,IC9qBjBu/B,GAAiB4K,sBAAwB5K,GAAiB4K,uBAAyB,CAAE,EACrF5K,GAAiB4K,sBAAsBC,aAAe,CAAEtL,0BAAwBoL,2BAChF3K,GAAiB4K,sBAAsBE,MAAQ,CAAE1U,OAAQ2U,GAAaC,QAAS,MAO/EhL,GAAiB8K,MAAQ,CAAE1U,OAAQ2U,GAAaC,QAAS,MACzDhL,GAAiBiL,mBAAqB,CAAE1L,2BACxCS,GAAiB2K,uBAAyBA", "x_google_ignoreList": [0, 1]}