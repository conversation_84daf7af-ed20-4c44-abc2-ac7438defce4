<!DOCTYPE html>
<html>
<head>
    <title>测试插件配置</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #4285f4; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        .status { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h2>测试插件配置</h2>
    
    <div class="form-group">
        <label for="emailDomain">邮箱后缀:</label>
        <input type="text" id="emailDomain" placeholder="example.com">
    </div>

    <div class="form-group">
        <label for="emailService">邮箱服务:</label>
        <select id="emailService">
            <option value="">选择邮箱服务</option>
            <option value="tempmailplus" selected>TempMail.Plus (需要PIN码) - 推荐</option>
            <option value="1secmail">1secmail (免费)</option>
        </select>
    </div>

    <div class="form-group" id="tempMailGroup" style="display: none;">
        <label for="tempMailAddress">TempMail.Plus邮箱地址:</label>
        <input type="email" id="tempMailAddress" placeholder="例如: <EMAIL>">
    </div>

    <div class="form-group" id="pinCodeGroup" style="display: none;">
        <label for="pinCode">PIN码:</label>
        <input type="password" id="pinCode" placeholder="输入PIN码">
    </div>

    <button id="testSave">测试保存</button>
    <button id="testLoad">测试加载</button>
    <button id="clearData">清除数据</button>

    <div id="status" class="status" style="display: none;"></div>

    <script>
        const emailServiceSelect = document.getElementById('emailService');
        const tempMailGroup = document.getElementById('tempMailGroup');
        const pinCodeGroup = document.getElementById('pinCodeGroup');
        const status = document.getElementById('status');

        // 邮箱服务变化处理
        emailServiceSelect.addEventListener('change', function() {
            const selectedService = this.value;
            const needsTempMailPlus = selectedService === 'tempmailplus';
            
            if (needsTempMailPlus) {
                tempMailGroup.style.display = 'block';
                pinCodeGroup.style.display = 'block';
            } else {
                tempMailGroup.style.display = 'none';
                pinCodeGroup.style.display = 'none';
            }
        });

        // 初始化显示
        emailServiceSelect.dispatchEvent(new Event('change'));

        // 测试保存
        document.getElementById('testSave').addEventListener('click', function() {
            const data = {
                emailDomain: document.getElementById('emailDomain').value,
                emailService: document.getElementById('emailService').value,
                tempMailAddress: document.getElementById('tempMailAddress').value,
                pinCode: document.getElementById('pinCode').value
            };

            console.log('保存数据:', data);
            
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.set(data, function() {
                    showStatus('数据保存成功！', 'success');
                });
            } else {
                localStorage.setItem('testData', JSON.stringify(data));
                showStatus('数据保存到localStorage成功！', 'success');
            }
        });

        // 测试加载
        document.getElementById('testLoad').addEventListener('click', function() {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.get(['emailDomain', 'emailService', 'tempMailAddress', 'pinCode'], function(result) {
                    loadData(result);
                    showStatus('数据加载成功！', 'success');
                });
            } else {
                const data = JSON.parse(localStorage.getItem('testData') || '{}');
                loadData(data);
                showStatus('数据从localStorage加载成功！', 'success');
            }
        });

        // 清除数据
        document.getElementById('clearData').addEventListener('click', function() {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.clear(function() {
                    showStatus('Chrome存储已清除！', 'success');
                });
            } else {
                localStorage.removeItem('testData');
                showStatus('localStorage已清除！', 'success');
            }
            
            // 清除表单
            document.getElementById('emailDomain').value = '';
            document.getElementById('emailService').value = '';
            document.getElementById('tempMailAddress').value = '';
            document.getElementById('pinCode').value = '';
        });

        function loadData(data) {
            if (data.emailDomain) document.getElementById('emailDomain').value = data.emailDomain;
            if (data.emailService) {
                document.getElementById('emailService').value = data.emailService;
                emailServiceSelect.dispatchEvent(new Event('change'));
            }
            if (data.tempMailAddress) document.getElementById('tempMailAddress').value = data.tempMailAddress;
            if (data.pinCode) document.getElementById('pinCode').value = data.pinCode;
        }

        function showStatus(message, type) {
            status.textContent = message;
            status.className = 'status ' + type;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }
    </script>
</body>
</html>
