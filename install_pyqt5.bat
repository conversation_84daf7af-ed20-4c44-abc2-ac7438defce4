@echo off
echo ========================================
echo     Augment续杯工具 - PyQt5版本安装
echo ========================================
echo.
echo 正在安装PyQt5版本依赖...
echo.

echo 1. 安装PyQt5 GUI库...
pip install PyQt5

echo.
echo 2. 安装requests库...
pip install requests>=2.25.0

echo.
echo 3. 安装selenium库...
pip install selenium>=4.0.0

echo.
echo 4. 安装webdriver-manager库...
pip install webdriver-manager>=3.8.0

echo.
echo ========================================
echo 安装完成！
echo.
echo 现在可以运行以下命令启动程序：
echo python augment_tool_qt.py
echo.
echo 或者双击运行 "启动PyQt5版本.bat"
echo ========================================
echo.
pause
