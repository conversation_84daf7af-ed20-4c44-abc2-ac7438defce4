#!/usr/bin/env python3
"""
测试Augment网站Sign In按钮选择器
"""

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from webdriver_manager.chrome import ChromeDriverManager
    import time
    
    print("🚀 启动测试...")
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 创建WebDriver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        print("🌐 正在打开Augment网站...")
        driver.get("https://www.augmentcode.com/")
        
        # 等待页面加载
        wait = WebDriverWait(driver, 15)
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(3)
        
        print("🔍 测试选择器...")
        
        # 测试选择器
        selectors = [
            ("XPath - 精确href", "//a[@href='https://app.augmentcode.com' and contains(text(), 'Sign in')]"),
            ("XPath - data-slot", "//a[@data-slot='button' and contains(text(), 'Sign in')]"),
            ("XPath - class", "//a[contains(@class, 'cursor-pointer') and contains(text(), 'Sign in')]"),
            ("XPath - href包含", "//a[contains(@href, 'app.augmentcode.com')]"),
            ("XPath - 文本包含", "//a[contains(text(), 'Sign in')]"),
            ("CSS - href", "a[href='https://app.augmentcode.com']"),
            ("CSS - data-slot", "a[data-slot='button']")
        ]
        
        found_elements = []
        
        for name, selector in selectors:
            try:
                if selector.startswith("//"):
                    elements = driver.find_elements(By.XPATH, selector)
                else:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                
                if elements:
                    element = elements[0]
                    print(f"✅ {name}: 找到元素")
                    print(f"   文本: '{element.text}'")
                    print(f"   href: {element.get_attribute('href')}")
                    print(f"   class: {element.get_attribute('class')[:100]}...")
                    found_elements.append((name, element))
                else:
                    print(f"❌ {name}: 未找到")
            except Exception as e:
                print(f"❌ {name}: 错误 - {e}")
        
        if found_elements:
            print(f"\n🎯 总共找到 {len(found_elements)} 个匹配的元素")
            print("尝试点击第一个找到的元素...")

            try:
                name, element = found_elements[0]
                driver.execute_script("arguments[0].scrollIntoView(true);", element)
                time.sleep(1)
                element.click()
                print(f"✅ 成功点击: {name}")
                time.sleep(3)
                print(f"当前URL: {driver.current_url}")

                # 测试邮箱输入框
                print("\n🔍 测试邮箱输入框...")
                email_selectors = [
                    "//input[@name='username' and @type='text']",
                    "//input[@id='username']",
                    "//input[@inputmode='email']",
                    "input[name='username']",
                    "input[id='username']"
                ]

                email_input = None
                for selector in email_selectors:
                    try:
                        if selector.startswith("//"):
                            email_input = driver.find_element(By.XPATH, selector)
                        else:
                            email_input = driver.find_element(By.CSS_SELECTOR, selector)
                        if email_input.is_displayed():
                            print(f"✅ 找到邮箱输入框: {selector}")
                            break
                    except:
                        continue

                if email_input:
                    test_email = "<EMAIL>"
                    email_input.clear()
                    email_input.send_keys(test_email)
                    print(f"✅ 成功输入测试邮箱: {test_email}")

                    # 测试人机验证复选框
                    print("\n🤖 测试人机验证复选框...")
                    time.sleep(2)

                    checkboxes = driver.find_elements(By.XPATH, "//input[@type='checkbox']")
                    if checkboxes:
                        checkbox = None
                        for cb in checkboxes:
                            if cb.is_displayed() and cb.is_enabled():
                                checkbox = cb
                                break

                        if checkbox:
                            print("✅ 找到人机验证复选框")
                            print("🎯 测试偏移点击...")

                            from selenium.webdriver.common.action_chains import ActionChains
                            location = checkbox.location
                            size = checkbox.size
                            offset_x = size['width'] * 0.3
                            offset_y = size['height'] * 0.3

                            actions = ActionChains(driver)
                            actions.move_to_element_with_offset(checkbox, offset_x, offset_y).click().perform()
                            print("✅ 成功执行偏移点击")
                        else:
                            print("❌ 未找到可用的复选框")
                    else:
                        print("❌ 未找到任何复选框")
                else:
                    print("❌ 未找到邮箱输入框")

            except Exception as e:
                print(f"❌ 测试失败: {e}")
        else:
            print("❌ 没有找到任何匹配的元素")
            
            # 保存调试信息
            driver.save_screenshot("test_screenshot.png")
            with open("test_page_source.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print("📸 调试信息已保存")
        
        print("\n按回车键关闭浏览器...")
        input()
        
    finally:
        driver.quit()
        print("🔒 浏览器已关闭")
        
except ImportError:
    print("❌ 请先安装selenium和webdriver-manager:")
    print("pip install selenium webdriver-manager")
except Exception as e:
    print(f"❌ 测试失败: {e}")
