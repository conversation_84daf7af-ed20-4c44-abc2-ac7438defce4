🚀 Augment续杯工具 - 使用指南

========================================
📦 文件说明
========================================

完整版（需要Chrome浏览器）：
- augment_refill_app.py      # 完整版主程序
- requirements.txt           # 完整版依赖
- install.bat               # 完整版安装脚本
- run.bat                   # 完整版启动脚本

简化版（推荐新手使用）：
- augment_refill_simple.py   # 简化版主程序
- requirements_simple.txt    # 简化版依赖
- install_simple.bat        # 简化版安装脚本
- run_simple.bat            # 简化版启动脚本

其他文件：
- build_exe.bat             # 打包成exe文件
- README_DESKTOP.md         # 详细说明文档

========================================
🚀 快速开始（推荐简化版）
========================================

1. 双击运行 "install_simple.bat" 安装依赖
2. 双击运行 "run_simple.bat" 启动程序
3. 配置您的域名和TempMail.Plus信息
4. 点击"保存配置"
5. 点击"生成邮箱"获取随机邮箱
6. 点击"打开登录页"在浏览器中手动登录
7. 点击"获取验证码"自动获取验证码

========================================
⚙️ 配置说明
========================================

基础配置：
- 邮箱后缀：您自己的域名（如：example.com）
- 随机字符串位数：默认12位

TempMail.Plus配置：
- 邮箱地址：您在TempMail.Plus创建的邮箱
- PIN码：在TempMail.Plus网站设置的PIN码

========================================
🔄 工作流程
========================================

1. 程序生成随机邮箱：abc123@您的域名.com
2. 您在浏览器中输入这个邮箱登录
3. Augment发送验证码到这个邮箱
4. Cloudflare转发邮件到您的TempMail.Plus邮箱
5. 程序自动获取验证码并显示给您
6. 您手动输入验证码完成登录

========================================
🛠️ 故障排除
========================================

问题1：Python环境错误
解决：安装Python 3.8+，下载地址：https://www.python.org/downloads/

问题2：邮箱连接失败
解决：检查TempMail.Plus邮箱地址和PIN码是否正确

问题3：获取不到验证码
解决：
- 检查Cloudflare邮件转发配置
- 确认邮箱服务正常工作
- 等待几分钟后重试

问题4：程序启动失败
解决：
- 确保已运行安装脚本
- 检查网络连接
- 尝试重新安装依赖

========================================
💡 使用技巧
========================================

1. 建议使用简化版，更稳定易用
2. 配置一次后会自动保存，下次直接使用
3. 可以同时打开多个程序实例
4. 验证码会自动复制到剪贴板
5. 日志区域显示详细的操作信息

========================================
⚠️ 注意事项
========================================

1. 请使用您自己控制的域名
2. 妥善保管TempMail.Plus的PIN码
3. 建议在安全的网络环境下使用
4. 本工具仅供学习和合法用途使用

========================================
📞 技术支持
========================================

如遇到问题：
1. 查看程序日志获取错误信息
2. 检查网络连接和配置
3. 尝试重新安装依赖
4. 参考README_DESKTOP.md获取更多信息

祝您使用愉快！ 🎉
