#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯 - 桌面版
自动化Augment登录和验证码获取工具
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import customtkinter as ctk
import threading
import time
import json
import os
import requests
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
import random
import string

# 设置customtkinter主题
ctk.set_appearance_mode("dark")  # 可选: "light", "dark", "system"
ctk.set_default_color_theme("blue")  # 可选: "blue", "green", "dark-blue"

class EmailAPI:
    """邮箱API基类"""
    def __init__(self, config):
        self.config = config
        self.email_address = None
    
    def set_email_address(self, email):
        self.email_address = email
    
    def extract_verification_code(self, content):
        """从邮件内容中提取验证码"""
        patterns = [
            r'验证码[：:\s]*([A-Z0-9]{4,8})',
            r'verification code[：:\s]*([A-Z0-9]{4,8})',
            r'code[：:\s]*([A-Z0-9]{4,8})',
            r'([A-Z0-9]{6})',
            r'([0-9]{4,8})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)
        return None

class TempMailPlusAPI(EmailAPI):
    """TempMail.Plus API实现"""
    
    def get_emails(self):
        """获取邮件列表"""
        if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
            raise Exception('TempMail.Plus需要邮箱地址和PIN码')
        
        temp_mail_address = self.config['temp_mail_address']
        username, domain = temp_mail_address.split('@')
        
        # 尝试多个可能的API端点
        endpoints = [
            f'https://tempmail.plus/api/v1/inbox/{username}',
            f'https://tempmail.plus/api/inbox/{username}',
            f'https://tempmail.plus/inbox/{username}',
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, headers={
                    'Content-Type': 'application/json',
                    'X-Pin-Code': self.config['pin_code'],
                    'Authorization': f'PIN {self.config["pin_code"]}',
                }, timeout=10)
                
                if response.ok:
                    data = response.json()
                    emails = data.get('emails', data.get('messages', data.get('inbox', [])))
                    
                    if isinstance(emails, list):
                        results = []
                        for email in emails[:5]:
                            content = email.get('body', email.get('content', email.get('text', '')))
                            code = self.extract_verification_code(content)
                            if code:
                                results.append({
                                    'id': email.get('id'),
                                    'subject': email.get('subject'),
                                    'content': content,
                                    'verification_code': code,
                                    'date': email.get('date', email.get('timestamp'))
                                })
                        return results
            except Exception as e:
                print(f"端点 {endpoint} 失败: {e}")
                continue
        
        raise Exception('无法通过API获取邮件')

class OneSecMailAPI(EmailAPI):
    """1SecMail API实现"""
    
    def get_emails(self):
        if not self.email_address:
            raise Exception('邮箱地址未设置')
        
        login, domain = self.email_address.split('@')
        url = f'https://www.1secmail.com/api/v1/?action=getMessages&login={login}&domain={domain}'
        
        try:
            response = requests.get(url, timeout=10)
            emails = response.json()
            
            if not isinstance(emails, list):
                return []
            
            results = []
            for email in emails[:5]:
                detail_url = f'https://www.1secmail.com/api/v1/?action=readMessage&login={login}&domain={domain}&id={email["id"]}'
                detail_response = requests.get(detail_url, timeout=10)
                detail = detail_response.json()
                
                content = detail.get('textBody', detail.get('body', ''))
                code = self.extract_verification_code(content)
                if code:
                    results.append({
                        'id': email['id'],
                        'subject': email.get('subject', ''),
                        'content': content,
                        'verification_code': code,
                        'date': email.get('date', '')
                    })
            
            return results
        except Exception as e:
            raise Exception(f'1SecMail API错误: {e}')

class AugmentRefillApp:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("Augment续杯 - 桌面版")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 配置数据
        self.config_file = "augment_config.json"
        self.config = self.load_config()
        
        # 浏览器驱动
        self.driver = None
        
        # 创建界面
        self.create_widgets()
        self.load_saved_config()
        
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_label = ctk.CTkLabel(
            self.root, 
            text="🚀 Augment续杯工具", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # 创建主框架
        main_frame = ctk.CTkScrollableFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # 基础配置区域
        self.create_basic_config_section(main_frame)
        
        # 邮箱服务配置区域
        self.create_email_service_section(main_frame)
        
        # 操作按钮区域
        self.create_action_buttons(main_frame)
        
        # 日志显示区域
        self.create_log_section(main_frame)
        
    def create_basic_config_section(self, parent):
        """创建基础配置区域"""
        config_frame = ctk.CTkFrame(parent)
        config_frame.pack(fill="x", pady=10)
        
        # 标题
        ctk.CTkLabel(
            config_frame, 
            text="📧 基础配置", 
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=10)
        
        # 邮箱后缀
        domain_frame = ctk.CTkFrame(config_frame)
        domain_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(domain_frame, text="邮箱后缀:").pack(side="left", padx=10)
        self.domain_entry = ctk.CTkEntry(
            domain_frame, 
            placeholder_text="例如: example.com",
            width=300
        )
        self.domain_entry.pack(side="right", padx=10, pady=10)
        
        # 随机字符串位数
        length_frame = ctk.CTkFrame(config_frame)
        length_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(length_frame, text="随机字符串位数:").pack(side="left", padx=10)
        self.length_entry = ctk.CTkEntry(
            length_frame, 
            placeholder_text="默认: 12",
            width=100
        )
        self.length_entry.pack(side="right", padx=10, pady=10)
        
    def create_email_service_section(self, parent):
        """创建邮箱服务配置区域"""
        email_frame = ctk.CTkFrame(parent)
        email_frame.pack(fill="x", pady=10)
        
        # 标题
        ctk.CTkLabel(
            email_frame, 
            text="📮 邮箱服务配置", 
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=10)
        
        # 邮箱服务选择
        service_frame = ctk.CTkFrame(email_frame)
        service_frame.pack(fill="x", padx=20, pady=5)
        
        ctk.CTkLabel(service_frame, text="邮箱服务:").pack(side="left", padx=10)
        self.service_var = tk.StringVar(value="tempmailplus")
        self.service_combo = ctk.CTkComboBox(
            service_frame,
            values=["tempmailplus", "1secmail"],
            variable=self.service_var,
            command=self.on_service_change,
            width=200
        )
        self.service_combo.pack(side="right", padx=10, pady=10)
        
        # TempMail.Plus配置
        self.tempmail_frame = ctk.CTkFrame(email_frame)
        self.tempmail_frame.pack(fill="x", padx=20, pady=5)
        
        # TempMail.Plus邮箱地址
        tempmail_addr_frame = ctk.CTkFrame(self.tempmail_frame)
        tempmail_addr_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(tempmail_addr_frame, text="TempMail.Plus邮箱:").pack(side="left", padx=10)
        self.tempmail_entry = ctk.CTkEntry(
            tempmail_addr_frame, 
            placeholder_text="例如: <EMAIL>",
            width=300
        )
        self.tempmail_entry.pack(side="right", padx=10, pady=10)
        
        # PIN码
        pin_frame = ctk.CTkFrame(self.tempmail_frame)
        pin_frame.pack(fill="x", padx=10, pady=5)
        
        ctk.CTkLabel(pin_frame, text="PIN码:").pack(side="left", padx=10)
        self.pin_entry = ctk.CTkEntry(
            pin_frame, 
            placeholder_text="输入PIN码",
            show="*",
            width=200
        )
        self.pin_entry.pack(side="right", padx=10, pady=10)
        
    def create_action_buttons(self, parent):
        """创建操作按钮区域"""
        button_frame = ctk.CTkFrame(parent)
        button_frame.pack(fill="x", pady=20)
        
        # 按钮容器
        btn_container = ctk.CTkFrame(button_frame)
        btn_container.pack(pady=20)
        
        # 保存配置按钮
        self.save_btn = ctk.CTkButton(
            btn_container,
            text="💾 保存配置",
            command=self.save_config,
            width=120,
            height=40
        )
        self.save_btn.pack(side="left", padx=10)
        
        # 开始续杯按钮
        self.start_btn = ctk.CTkButton(
            btn_container,
            text="🚀 开始续杯",
            command=self.start_refill,
            width=120,
            height=40,
            fg_color="green"
        )
        self.start_btn.pack(side="left", padx=10)
        
        # 停止按钮
        self.stop_btn = ctk.CTkButton(
            btn_container,
            text="⏹️ 停止",
            command=self.stop_refill,
            width=120,
            height=40,
            fg_color="red",
            state="disabled"
        )
        self.stop_btn.pack(side="left", padx=10)
        
        # 测试邮箱按钮
        self.test_btn = ctk.CTkButton(
            btn_container,
            text="📧 测试邮箱",
            command=self.test_email,
            width=120,
            height=40
        )
        self.test_btn.pack(side="left", padx=10)

    def create_log_section(self, parent):
        """创建日志显示区域"""
        log_frame = ctk.CTkFrame(parent)
        log_frame.pack(fill="both", expand=True, pady=10)

        # 标题
        ctk.CTkLabel(
            log_frame,
            text="📋 运行日志",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=10)

        # 日志文本框
        self.log_text = ctk.CTkTextbox(log_frame, height=200)
        self.log_text.pack(fill="both", expand=True, padx=20, pady=10)

        # 清除日志按钮
        clear_btn = ctk.CTkButton(
            log_frame,
            text="🗑️ 清除日志",
            command=self.clear_log,
            width=100,
            height=30
        )
        clear_btn.pack(pady=10)

    def on_service_change(self, value):
        """邮箱服务选择变化处理"""
        if value == "tempmailplus":
            self.tempmail_frame.pack(fill="x", padx=20, pady=5)
        else:
            self.tempmail_frame.pack_forget()

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
        self.root.update()

    def clear_log(self):
        """清除日志"""
        self.log_text.delete("1.0", "end")

    def load_config(self):
        """加载配置文件"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载配置失败: {e}")
        return {}

    def save_config_to_file(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False

    def load_saved_config(self):
        """加载已保存的配置到界面"""
        if self.config.get('email_domain'):
            self.domain_entry.insert(0, self.config['email_domain'])
        if self.config.get('random_length'):
            self.length_entry.insert(0, str(self.config['random_length']))
        if self.config.get('email_service'):
            self.service_var.set(self.config['email_service'])
            self.on_service_change(self.config['email_service'])
        if self.config.get('temp_mail_address'):
            self.tempmail_entry.insert(0, self.config['temp_mail_address'])
        if self.config.get('pin_code'):
            self.pin_entry.insert(0, self.config['pin_code'])

    def save_config(self):
        """保存配置"""
        try:
            # 验证输入
            domain = self.domain_entry.get().strip()
            if not domain:
                messagebox.showerror("错误", "请输入邮箱后缀")
                return

            if '@' in domain or not '.' in domain:
                messagebox.showerror("错误", "请输入正确的域名格式，如 example.com")
                return

            length = self.length_entry.get().strip()
            if length and (not length.isdigit() or int(length) < 1 or int(length) > 32):
                messagebox.showerror("错误", "随机字符串位数必须是1-32之间的数字")
                return

            service = self.service_var.get()
            if not service:
                messagebox.showerror("错误", "请选择邮箱服务")
                return

            # TempMail.Plus验证
            if service == "tempmailplus":
                temp_mail = self.tempmail_entry.get().strip()
                pin_code = self.pin_entry.get().strip()

                if not temp_mail:
                    messagebox.showerror("错误", "请输入TempMail.Plus邮箱地址")
                    return

                if not pin_code:
                    messagebox.showerror("错误", "请输入PIN码")
                    return

                # 验证邮箱格式
                email_pattern = r'^[^\s@]+@[^\s@]+\.[^\s@]+$'
                if not re.match(email_pattern, temp_mail):
                    messagebox.showerror("错误", "请输入有效的邮箱地址格式")
                    return

                self.config['temp_mail_address'] = temp_mail
                self.config['pin_code'] = pin_code

            # 保存配置
            self.config['email_domain'] = domain
            self.config['random_length'] = int(length) if length else 12
            self.config['email_service'] = service

            if self.save_config_to_file():
                messagebox.showinfo("成功", "配置保存成功！")
                self.log_message("✅ 配置保存成功")
            else:
                messagebox.showerror("错误", "配置保存失败")

        except Exception as e:
            messagebox.showerror("错误", f"保存配置时出错: {e}")

    def generate_random_email(self):
        """生成随机邮箱"""
        length = self.config.get('random_length', 12)
        domain = self.config.get('email_domain', '')

        if not domain:
            raise Exception("未设置邮箱域名")

        characters = string.ascii_letters + string.digits
        random_str = ''.join(random.choice(characters) for _ in range(length))
        return f"{random_str}@{domain}"

    def create_email_api(self):
        """创建邮箱API实例"""
        service = self.config.get('email_service', '')

        if service == 'tempmailplus':
            return TempMailPlusAPI(self.config)
        elif service == '1secmail':
            return OneSecMailAPI(self.config)
        else:
            raise Exception(f"不支持的邮箱服务: {service}")

    def test_email(self):
        """测试邮箱连接"""
        def test_thread():
            try:
                self.log_message("🔍 开始测试邮箱连接...")

                # 创建邮箱API
                email_api = self.create_email_api()

                # 生成测试邮箱
                test_email = self.generate_random_email()
                email_api.set_email_address(test_email)

                self.log_message(f"📧 测试邮箱: {test_email}")

                # 测试获取邮件
                emails = email_api.get_emails()

                if emails:
                    self.log_message(f"✅ 邮箱连接成功，找到 {len(emails)} 封邮件")
                    for email in emails[:3]:  # 显示前3封
                        self.log_message(f"  📨 {email.get('subject', '无主题')}")
                else:
                    self.log_message("✅ 邮箱连接成功，暂无邮件")

                messagebox.showinfo("测试成功", "邮箱连接测试成功！")

            except Exception as e:
                error_msg = f"❌ 邮箱测试失败: {e}"
                self.log_message(error_msg)
                messagebox.showerror("测试失败", str(e))

        threading.Thread(target=test_thread, daemon=True).start()

    def setup_webdriver(self):
        """设置浏览器驱动"""
        try:
            self.log_message("🔧 正在设置浏览器驱动...")

            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1200,800')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 自动下载和管理ChromeDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)

            # 隐藏自动化特征
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.log_message("🌐 浏览器启动成功")
            return True

        except Exception as e:
            self.log_message(f"❌ 浏览器启动失败: {e}")
            messagebox.showerror("错误", f"浏览器启动失败，请确保已安装Chrome浏览器\n\n错误: {e}")
            return False

    def start_refill(self):
        """开始续杯流程"""
        def refill_thread():
            try:
                # 验证配置
                if not self.config.get('email_domain'):
                    messagebox.showerror("错误", "请先保存配置")
                    return

                # 更新按钮状态
                self.start_btn.configure(state="disabled")
                self.stop_btn.configure(state="normal")

                self.log_message("🚀 开始Augment续杯流程...")

                # 启动浏览器
                if not self.setup_webdriver():
                    return

                # 生成随机邮箱
                random_email = self.generate_random_email()
                self.log_message(f"📧 生成邮箱: {random_email}")

                # 打开Augment登录页面
                self.log_message("🌐 打开Augment登录页面...")
                self.driver.get("https://login.augmentcode.com/u/login/identifier")

                # 等待页面加载
                wait = WebDriverWait(self.driver, 10)

                # 查找邮箱输入框
                self.log_message("🔍 查找邮箱输入框...")
                email_input = wait.until(
                    EC.presence_of_element_located((By.NAME, "identifier"))
                )

                # 输入邮箱
                email_input.clear()
                email_input.send_keys(random_email)
                self.log_message(f"✅ 邮箱已输入: {random_email}")

                # 查找并点击继续按钮
                continue_btn = wait.until(
                    EC.element_to_be_clickable((By.CSS_SELECTOR, "button[type='submit'], button[name='action']"))
                )
                continue_btn.click()
                self.log_message("✅ 点击继续按钮")

                # 等待跳转到验证码页面
                self.log_message("⏳ 等待跳转到验证码页面...")
                time.sleep(3)

                # 检查是否到达验证码页面
                current_url = self.driver.current_url
                if "challenge" in current_url:
                    self.log_message("✅ 已到达验证码页面")

                    # 开始获取验证码
                    self.get_verification_code(random_email)
                else:
                    self.log_message("❌ 未能到达验证码页面")

            except TimeoutException:
                self.log_message("❌ 页面加载超时")
                messagebox.showerror("错误", "页面加载超时，请检查网络连接")
            except Exception as e:
                self.log_message(f"❌ 续杯流程出错: {e}")
                messagebox.showerror("错误", f"续杯流程出错: {e}")
            finally:
                # 恢复按钮状态
                self.start_btn.configure(state="normal")
                self.stop_btn.configure(state="disabled")

        threading.Thread(target=refill_thread, daemon=True).start()

    def get_verification_code(self, generated_email):
        """获取验证码"""
        try:
            self.log_message("📧 开始获取验证码...")

            # 创建邮箱API
            email_api = self.create_email_api()
            email_api.set_email_address(generated_email)

            # 查找验证码输入框
            wait = WebDriverWait(self.driver, 10)
            code_input = wait.until(
                EC.presence_of_element_located((
                    By.CSS_SELECTOR,
                    "input[name='passcode'], input[type='text'][placeholder*='code'], input[type='text'][placeholder*='验证码']"
                ))
            )

            self.log_message("✅ 找到验证码输入框")

            # 轮询获取验证码
            max_attempts = 30  # 最多尝试30次，每次10秒
            for attempt in range(1, max_attempts + 1):
                try:
                    self.log_message(f"🔍 第 {attempt}/{max_attempts} 次尝试获取验证码...")

                    emails = email_api.get_emails()

                    # 查找包含验证码的邮件
                    for email in emails:
                        if email.get('verification_code'):
                            code = email['verification_code']
                            self.log_message(f"✅ 找到验证码: {code}")

                            # 输入验证码
                            code_input.clear()
                            code_input.send_keys(code)
                            self.log_message("✅ 验证码已输入")

                            # 查找并点击提交按钮
                            submit_btn = wait.until(
                                EC.element_to_be_clickable((
                                    By.CSS_SELECTOR,
                                    "button[type='submit'], button[name='action'], input[type='submit']"
                                ))
                            )
                            submit_btn.click()
                            self.log_message("✅ 验证码已提交")

                            # 等待登录完成
                            time.sleep(3)
                            self.log_message("🎉 续杯流程完成！")
                            messagebox.showinfo("成功", "续杯流程完成！")
                            return

                    # 如果没有找到验证码，等待10秒后重试
                    if attempt < max_attempts:
                        time.sleep(10)

                except Exception as e:
                    self.log_message(f"⚠️ 获取验证码时出错: {e}")
                    if attempt < max_attempts:
                        time.sleep(10)
                        continue
                    else:
                        raise e

            # 如果所有尝试都失败了
            self.log_message("❌ 获取验证码超时，请手动输入")
            messagebox.showwarning("超时", "自动获取验证码超时，请手动输入验证码")

        except Exception as e:
            self.log_message(f"❌ 验证码获取失败: {e}")
            messagebox.showerror("错误", f"验证码获取失败: {e}")

    def stop_refill(self):
        """停止续杯流程"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.log_message("🛑 浏览器已关闭")

            self.start_btn.configure(state="normal")
            self.stop_btn.configure(state="disabled")
            self.log_message("⏹️ 续杯流程已停止")

        except Exception as e:
            self.log_message(f"❌ 停止流程时出错: {e}")

    def run(self):
        """运行应用"""
        self.log_message("🎉 Augment续杯工具启动成功！")
        self.log_message("💡 使用说明：")
        self.log_message("   1. 配置您的域名和邮箱服务")
        self.log_message("   2. 点击'保存配置'")
        self.log_message("   3. 可选：点击'测试邮箱'验证配置")
        self.log_message("   4. 点击'开始续杯'启动自动化流程")
        self.log_message("")

        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.log_message("👋 程序退出")
        finally:
            if self.driver:
                self.driver.quit()

def main():
    """主函数"""
    try:
        app = AugmentRefillApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        messagebox.showerror("错误", f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
