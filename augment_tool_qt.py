#!/usr/bin/env python3
"""
Augment续杯工具 - PyQt5版本
解决tkinter兼容性问题
"""

import sys
import os
import json
import time
import string
import random
import threading
import webbrowser

# 检查PyQt5是否可用
try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                                QHBoxLayout, QLabel, QPushButton, QLineEdit, 
                                QTextEdit, QFrame, QScrollArea, QMessageBox,
                                QTabWidget, QGridLayout, QSizePolicy)
    from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
    from PyQt5.QtGui import QFont, QPalette, QColor
    PYQT_AVAILABLE = True
except ImportError:
    PYQT_AVAILABLE = False
    print("PyQt5未安装，请运行: pip install PyQt5")
    sys.exit(1)

# 检查其他依赖
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.action_chains import ActionChains
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

class EmailAPI:
    """邮箱API类"""
    def __init__(self, config):
        self.config = config
        
    def get_emails(self):
        """获取邮件列表"""
        if not REQUESTS_AVAILABLE:
            raise Exception("requests库未安装")
            
        try:
            # 这里实现TempMail.Plus API调用
            # 返回示例数据
            return [{
                'verification_code': '123456',
                'subject': 'Augment验证码',
                'from': '<EMAIL>'
            }]
        except Exception as e:
            raise Exception(f"获取邮件失败: {e}")

class AutoRegisterThread(QThread):
    """自动注册线程"""
    log_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(str, str)  # success/error, message
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.driver = None
        
    def run(self):
        try:
            self.log_signal.emit("🚀 开始自动注册流程...")
            
            # 检查配置
            if not self.config.get('email_domain'):
                self.finished_signal.emit("error", "请先在配置页面设置邮箱域名")
                return
                
            # 生成随机邮箱
            self.log_signal.emit("📧 正在生成随机邮箱...")
            chars = string.ascii_letters + string.digits
            random_str = ''.join(random.choice(chars) for _ in range(12))
            email = f"{random_str}@{self.config['email_domain']}"
            
            # 复制到剪贴板
            QApplication.clipboard().setText(email)
            
            self.log_signal.emit(f"✅ 生成邮箱: {email}")
            self.log_signal.emit("📋 邮箱已复制到剪贴板")
            
            if SELENIUM_AVAILABLE:
                self.log_signal.emit("🤖 启动自动化浏览器...")
                self._run_automation(email)
            else:
                self.log_signal.emit("⚠️ 未安装Selenium，使用手动模式...")
                webbrowser.open("https://www.augmentcode.com/")
                self.finished_signal.emit("success", f"已打开网站，请手动操作\n邮箱: {email}")
                
        except Exception as e:
            self.log_signal.emit(f"❌ 自动注册失败: {e}")
            self.finished_signal.emit("error", str(e))
            
    def _run_automation(self, email):
        """运行自动化流程"""
        try:
            # 设置Chrome选项
            chrome_options = Options()
            chrome_options.add_argument("--start-maximized")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 清洁环境
            temp_profile = os.path.join(os.getcwd(), "temp_chrome_profile")
            if os.path.exists(temp_profile):
                import shutil
                shutil.rmtree(temp_profile, ignore_errors=True)
            chrome_options.add_argument(f"--user-data-dir={temp_profile}")
            
            # 创建WebDriver
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # 隐藏自动化标识
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.log_signal.emit("🌐 正在打开Augment Code网站...")
            self.driver.get("https://www.augmentcode.com/")
            
            # 等待页面加载
            wait = WebDriverWait(self.driver, 15)
            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(3)
            
            # 查找Sign In按钮
            self.log_signal.emit("🔍 正在查找Sign In按钮...")
            sign_in_button = self._find_sign_in_button(wait)
            
            if sign_in_button:
                sign_in_button.click()
                self.log_signal.emit("🎯 已点击Sign In按钮！")
                time.sleep(3)
                
                # 填入邮箱
                if self._fill_email(wait, email):
                    # 处理人机验证
                    if self._handle_captcha(wait):
                        self.finished_signal.emit("success", f"🎉 全自动化成功！\n邮箱: {email}\n请手动完成最后步骤")
                    else:
                        self.finished_signal.emit("success", f"⚠️ 部分成功\n邮箱: {email}\n请手动完成人机验证")
                else:
                    self.finished_signal.emit("success", f"⚠️ 基础成功\n邮箱: {email}\n请手动填入邮箱")
            else:
                self.finished_signal.emit("success", f"⚠️ 页面已打开\n邮箱: {email}\n请手动操作")
                
        except Exception as e:
            self.log_signal.emit(f"❌ 自动化失败: {e}")
            webbrowser.open("https://www.augmentcode.com/")
            self.finished_signal.emit("success", f"已切换到手动模式\n邮箱: {email}")
            
    def _find_sign_in_button(self, wait):
        """查找Sign In按钮"""
        selectors = [
            "//a[@href='https://app.augmentcode.com' and contains(text(), 'Sign in')]",
            "//a[@data-slot='button' and contains(text(), 'Sign in')]",
            "//a[contains(@class, 'cursor-pointer') and contains(text(), 'Sign in')]",
            "//a[contains(@href, 'app.augmentcode.com')]"
        ]
        
        for selector in selectors:
            try:
                button = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                self.log_signal.emit(f"✅ 找到Sign In按钮")
                return button
            except:
                continue
        return None
        
    def _fill_email(self, wait, email):
        """填入邮箱"""
        self.log_signal.emit("📧 正在查找邮箱输入框...")
        selectors = [
            "//input[@name='username' and @type='text']",
            "//input[@id='username']",
            "//input[@inputmode='email']"
        ]
        
        for selector in selectors:
            try:
                email_input = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                email_input.clear()
                email_input.send_keys(email)
                self.log_signal.emit(f"✅ 已输入邮箱: {email}")
                return True
            except:
                continue
        return False
        
    def _handle_captcha(self, wait):
        """处理人机验证"""
        self.log_signal.emit("🤖 正在查找人机验证复选框...")
        try:
            checkboxes = self.driver.find_elements(By.XPATH, "//input[@type='checkbox']")
            for checkbox in checkboxes:
                if checkbox.is_displayed() and checkbox.is_enabled():
                    # 偏移点击
                    size = checkbox.size
                    offset_x = size['width'] * 0.3
                    offset_y = size['height'] * 0.3
                    
                    actions = ActionChains(self.driver)
                    actions.move_to_element_with_offset(checkbox, offset_x, offset_y).click().perform()
                    self.log_signal.emit("✅ 已完成人机验证（偏移点击）")
                    return True
        except:
            pass
        return False

class AugmentToolQt(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()
        self.config_file = "config.json"
        self.config = self.load_config()
        self.driver = None
        
        self.init_ui()
        self.load_saved_config()
        
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("Augment续杯工具 - PyQt5版本")
        self.setGeometry(100, 100, 1000, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 创建各个页面
        self.create_home_tab()
        self.create_config_tab()
        self.create_email_tab()
        self.create_log_tab()
        
        # 设置样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QTabWidget::pane {
                border: 1px solid #c0c0c0;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
    def create_home_tab(self):
        """创建主页标签"""
        home_widget = QWidget()
        layout = QVBoxLayout(home_widget)
        
        # 标题
        title = QLabel("🚀 Augment续杯工具")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 24, QFont.Bold))
        layout.addWidget(title)
        
        subtitle = QLabel("自动化Augment账号注册和登录助手 - PyQt5版本")
        subtitle.setAlignment(Qt.AlignCenter)
        subtitle.setFont(QFont("Arial", 12))
        layout.addWidget(subtitle)
        
        # 按钮区域
        button_layout = QGridLayout()
        
        # 自动注册按钮
        self.auto_register_btn = QPushButton("🚀 开始自动注册")
        self.auto_register_btn.clicked.connect(self.auto_register)
        self.auto_register_btn.setMinimumHeight(60)
        button_layout.addWidget(self.auto_register_btn, 0, 0)
        
        # 手动登录按钮
        manual_login_btn = QPushButton("🌐 打开登录页")
        manual_login_btn.clicked.connect(self.open_login_page)
        manual_login_btn.setMinimumHeight(60)
        button_layout.addWidget(manual_login_btn, 0, 1)
        
        # 关闭浏览器按钮
        close_browser_btn = QPushButton("🔒 关闭浏览器")
        close_browser_btn.clicked.connect(self.close_browser)
        close_browser_btn.setMinimumHeight(60)
        button_layout.addWidget(close_browser_btn, 1, 0, 1, 2)
        
        layout.addLayout(button_layout)
        
        # 状态显示
        status_label = QLabel("📊 系统状态")
        status_label.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(status_label)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(120)
        self.status_text.setReadOnly(True)
        layout.addWidget(self.status_text)
        
        # 更新状态
        self.update_status_display()
        
        layout.addStretch()
        self.tab_widget.addTab(home_widget, "🏠 主页")
        
    def create_config_tab(self):
        """创建配置标签"""
        config_widget = QWidget()
        layout = QVBoxLayout(config_widget)
        
        title = QLabel("⚙️ 配置设置")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        layout.addWidget(title)
        
        # 配置表单
        form_layout = QVBoxLayout()
        
        # 域名配置
        form_layout.addWidget(QLabel("邮箱域名:"))
        self.domain_entry = QLineEdit()
        self.domain_entry.setPlaceholderText("例如: example.com")
        form_layout.addWidget(self.domain_entry)
        
        # TempMail配置
        form_layout.addWidget(QLabel("TempMail.Plus邮箱:"))
        self.tempmail_entry = QLineEdit()
        self.tempmail_entry.setPlaceholderText("例如: <EMAIL>")
        form_layout.addWidget(self.tempmail_entry)
        
        # PIN码配置
        form_layout.addWidget(QLabel("PIN码:"))
        self.pin_entry = QLineEdit()
        self.pin_entry.setEchoMode(QLineEdit.Password)
        form_layout.addWidget(self.pin_entry)
        
        # 保存按钮
        save_btn = QPushButton("💾 保存配置")
        save_btn.clicked.connect(self.save_config)
        save_btn.setMinimumHeight(40)
        form_layout.addWidget(save_btn)
        
        layout.addLayout(form_layout)
        layout.addStretch()
        
        self.tab_widget.addTab(config_widget, "⚙️ 配置")
        
    def create_email_tab(self):
        """创建邮箱管理标签"""
        email_widget = QWidget()
        layout = QVBoxLayout(email_widget)
        
        title = QLabel("📧 邮箱管理")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        layout.addWidget(title)
        
        # 生成邮箱
        gen_btn = QPushButton("📧 生成随机邮箱")
        gen_btn.clicked.connect(self.generate_email)
        gen_btn.setMinimumHeight(40)
        layout.addWidget(gen_btn)
        
        self.email_display = QTextEdit()
        self.email_display.setMaximumHeight(80)
        self.email_display.setReadOnly(True)
        layout.addWidget(self.email_display)
        
        # 获取验证码
        code_btn = QPushButton("🔍 获取验证码")
        code_btn.clicked.connect(self.get_code)
        code_btn.setMinimumHeight(40)
        layout.addWidget(code_btn)
        
        self.code_display = QTextEdit()
        self.code_display.setMaximumHeight(80)
        self.code_display.setReadOnly(True)
        layout.addWidget(self.code_display)
        
        layout.addStretch()
        
        self.tab_widget.addTab(email_widget, "📧 邮箱")
        
    def create_log_tab(self):
        """创建日志标签"""
        log_widget = QWidget()
        layout = QVBoxLayout(log_widget)
        
        title = QLabel("📋 日志查看")
        title.setAlignment(Qt.AlignCenter)
        title.setFont(QFont("Arial", 18, QFont.Bold))
        layout.addWidget(title)
        
        self.log_text = QTextEdit()
        self.log_text.setFont(QFont("Consolas", 10))
        layout.addWidget(self.log_text)
        
        clear_btn = QPushButton("🗑️ 清除日志")
        clear_btn.clicked.connect(self.clear_log)
        layout.addWidget(clear_btn)
        
        self.tab_widget.addTab(log_widget, "📋 日志")
        
        # 初始日志
        self.log("🎉 Augment续杯工具启动成功 (PyQt5版本)")
        self.log("💡 这是tkinter兼容性问题的解决方案")
        
    def log(self, message):
        """添加日志"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
        
    def update_status_display(self):
        """更新状态显示"""
        status_info = []
        
        if self.config.get('email_domain'):
            status_info.append(f"✅ 邮箱域名: {self.config['email_domain']}")
        else:
            status_info.append("❌ 邮箱域名: 未配置")
            
        if REQUESTS_AVAILABLE:
            status_info.append("✅ 网络功能: 可用")
        else:
            status_info.append("❌ 网络功能: 需要安装requests库")
            
        if SELENIUM_AVAILABLE:
            status_info.append("✅ 自动化功能: 可用")
        else:
            status_info.append("❌ 自动化功能: 需要安装selenium库")
            
        self.status_text.setPlainText('\n'.join(status_info))
        
    def load_config(self):
        """加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
        
    def save_config_to_file(self):
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
            
    def load_saved_config(self):
        """加载已保存的配置"""
        if self.config.get('email_domain'):
            self.domain_entry.setText(self.config['email_domain'])
        if self.config.get('temp_mail_address'):
            self.tempmail_entry.setText(self.config['temp_mail_address'])
        if self.config.get('pin_code'):
            self.pin_entry.setText(self.config['pin_code'])
            
    def save_config(self):
        """保存配置"""
        domain = self.domain_entry.text().strip()
        tempmail = self.tempmail_entry.text().strip()
        pin = self.pin_entry.text().strip()
        
        if not domain:
            QMessageBox.warning(self, "错误", "请输入邮箱域名")
            return
            
        self.config['email_domain'] = domain
        self.config['temp_mail_address'] = tempmail
        self.config['pin_code'] = pin
        
        if self.save_config_to_file():
            QMessageBox.information(self, "成功", "配置已保存")
            self.log("✅ 配置保存成功")
            self.update_status_display()
        else:
            QMessageBox.critical(self, "错误", "配置保存失败")
            
    def auto_register(self):
        """自动注册"""
        if not self.config.get('email_domain'):
            QMessageBox.warning(self, "错误", "请先在配置页面设置邮箱域名")
            return
            
        # 创建并启动自动注册线程
        self.register_thread = AutoRegisterThread(self.config)
        self.register_thread.log_signal.connect(self.log)
        self.register_thread.finished_signal.connect(self.on_register_finished)
        self.register_thread.start()
        
        # 禁用按钮防止重复点击
        self.auto_register_btn.setEnabled(False)
        self.auto_register_btn.setText("🔄 正在处理...")
        
    def on_register_finished(self, status, message):
        """注册完成回调"""
        self.auto_register_btn.setEnabled(True)
        self.auto_register_btn.setText("🚀 开始自动注册")
        
        if status == "success":
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.critical(self, "错误", message)
            
    def open_login_page(self):
        """打开登录页面"""
        try:
            self.log("🌐 正在打开Augment登录页面...")
            webbrowser.open("https://www.augmentcode.com/")
            self.log("✅ 已在默认浏览器中打开Augment官网")
            QMessageBox.information(self, "提示", "已打开Augment官网\n\n请点击右上角的 'Sign In' 按钮进行登录")
        except Exception as e:
            self.log(f"❌ 打开登录页面失败: {e}")
            QMessageBox.critical(self, "错误", f"打开登录页面失败: {e}")
            
    def close_browser(self):
        """关闭浏览器"""
        if hasattr(self, 'register_thread') and self.register_thread.driver:
            try:
                self.register_thread.driver.quit()
                self.register_thread.driver = None
                self.log("🔒 已关闭自动化浏览器")
                QMessageBox.information(self, "成功", "已关闭自动化浏览器")
            except Exception as e:
                self.log(f"❌ 关闭浏览器失败: {e}")
                QMessageBox.critical(self, "错误", f"关闭浏览器失败: {e}")
        else:
            QMessageBox.information(self, "提示", "没有运行中的自动化浏览器")
            
    def generate_email(self):
        """生成邮箱"""
        if not self.config.get('email_domain'):
            QMessageBox.warning(self, "错误", "请先在配置页面保存配置")
            return
            
        chars = string.ascii_letters + string.digits
        random_str = ''.join(random.choice(chars) for _ in range(12))
        email = f"{random_str}@{self.config['email_domain']}"
        
        QApplication.clipboard().setText(email)
        
        self.email_display.setPlainText(f"生成的邮箱: {email}\n已复制到剪贴板")
        self.log(f"📧 生成邮箱: {email}")
        QMessageBox.information(self, "成功", f"邮箱已生成并复制:\n{email}")
        
    def get_code(self):
        """获取验证码"""
        def get_code_worker():
            try:
                if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
                    QMessageBox.warning(self, "错误", "请先在配置页面保存配置")
                    return
                    
                self.log("🔍 开始获取验证码...")
                
                email_api = EmailAPI(self.config)
                emails = email_api.get_emails()
                
                if emails:
                    code = emails[0]['verification_code']
                    QApplication.clipboard().setText(code)
                    
                    self.code_display.setPlainText(f"验证码: {code}\n已复制到剪贴板")
                    self.log(f"✅ 找到验证码: {code}")
                    QMessageBox.information(self, "成功", f"验证码: {code}\n已复制到剪贴板")
                else:
                    self.log("❌ 未找到验证码")
                    self.code_display.setPlainText("未找到验证码，请稍后重试")
                    QMessageBox.information(self, "提示", "未找到验证码")
                    
            except Exception as e:
                self.log(f"❌ 获取验证码失败: {e}")
                self.code_display.setPlainText(f"获取失败: {e}")
                QMessageBox.critical(self, "错误", str(e))
                
        # 在新线程中执行
        threading.Thread(target=get_code_worker, daemon=True).start()
        
    def closeEvent(self, event):
        """程序关闭事件"""
        if hasattr(self, 'register_thread') and self.register_thread.driver:
            try:
                self.register_thread.driver.quit()
            except:
                pass
        event.accept()

def main():
    """主函数"""
    if not PYQT_AVAILABLE:
        print("错误: PyQt5未安装")
        print("请运行: pip install PyQt5")
        return
        
    app = QApplication(sys.argv)
    app.setApplicationName("Augment续杯工具")
    app.setApplicationVersion("2.0 PyQt5")
    
    window = AugmentToolQt()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
