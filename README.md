# Augment续杯工具

这是一个Python桌面应用程序，用于自动化Augment账号注册和登录流程。

## 功能特性

### 🎯 自动注册
- **真正的自动化**：使用Selenium自动控制浏览器
- **自动点击Sign In按钮**：无需手动查找和点击
- **清洁浏览器环境**：每次启动都清除所有浏览器数据，如同全新安装
- **智能回退**：如果自动化失败，自动切换到手动模式

### 🔑 手动登录
- 快速打开Augment官网
- 提供详细的操作指南

### 📧 邮箱管理
- 生成随机邮箱地址
- 自动复制到剪贴板
- 从TempMail.Plus获取验证码

### ⚙️ 配置管理
- 自定义邮箱域名
- 配置TempMail.Plus信息
- 设置PIN码

### 🌐 浏览器控制
- 关闭自动化浏览器
- 清理临时数据

## 安装要求

- Python 3.7+
- tkinter (通常随Python安装)
- requests
- selenium (可选，用于自动化功能)
- webdriver-manager (可选，用于自动化功能)

## 快速开始

### 方法一：自动安装依赖（推荐）

1. 下载项目文件
2. 双击运行 `install_dependencies.bat` 安装依赖
3. 运行 `python augment_tool.py` 启动程序

### 方法二：手动安装

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 启动程序：
```bash
python augment_tool.py
```

### 可选：启用自动化功能

如果您想要真正的自动化功能（自动点击Sign In按钮），请安装selenium：

```bash
pip install selenium>=4.0.0 webdriver-manager>=3.8.0
```

安装后程序会自动检测并启用自动化功能。

## 使用方法

### 第一次使用

1. 启动程序后，首先进入"配置设置"页面
2. 设置您的邮箱域名（如：example.com）
3. 配置TempMail.Plus邮箱和PIN码
4. 点击"保存配置"

### 自动注册流程

1. 在主页点击"🚀 开始自动注册"
2. 程序会：
   - 自动生成随机邮箱
   - 启动清洁的Chrome浏览器
   - 自动打开Augment网站
   - 自动点击Sign In按钮
3. 接下来手动完成注册即可

### 获取验证码

1. 完成注册后，切换到"邮箱管理"页面
2. 点击"🔍 获取验证码"
3. 验证码会自动复制到剪贴板

### 浏览器管理

- 使用完毕后，可在主页点击"🔒 关闭浏览器"清理资源
- 程序关闭时会自动清理浏览器进程

## 技术特性

### 自动化技术

- **Selenium WebDriver**：使用最新的Selenium 4.0+进行浏览器自动化
- **智能元素定位**：支持多种选择器策略，提高成功率
- **清洁环境**：每次启动都使用全新的浏览器配置文件
- **反检测**：隐藏自动化标识，模拟真实用户操作

### 用户界面

- **现代化GUI**：基于tkinter的美观界面设计
- **左侧导航**：清晰的功能分类和导航
- **实时状态**：显示配置状态和系统状态
- **日志系统**：详细的操作日志和错误信息

### 安全性

- **本地运行**：所有数据都在本地处理，不上传到任何服务器
- **临时数据**：浏览器数据在使用后自动清理
- **配置加密**：敏感配置信息安全存储

### 兼容性

- **跨平台**：支持Windows、macOS、Linux
- **Python版本**：兼容Python 3.7+
- **浏览器支持**：自动下载和管理Chrome驱动

## 项目结构

```
augment-tool/
├── augment_tool.py           # 主程序文件
├── requirements.txt          # Python依赖列表
├── install_dependencies.bat  # 自动安装脚本
├── config.json              # 配置文件（运行后生成）
├── temp_chrome_profile/      # 临时浏览器配置（自动生成）
└── README.md                # 项目说明
```

## 常见问题

### Q: 自动化功能不工作？
A: 请确保已安装selenium和webdriver-manager：
```bash
pip install selenium webdriver-manager
```

### Q: Chrome浏览器启动失败？
A: webdriver-manager会自动下载Chrome驱动，请确保网络连接正常。

### Q: 验证码获取失败？
A: 请检查TempMail.Plus配置是否正确，确保邮箱地址和PIN码有效。

### Q: 程序界面显示异常？
A: 请确保Python版本为3.7+，并且tkinter已正确安装。

## 更新日志

### v2.0.0 (最新)
- ✨ 新增真正的浏览器自动化功能
- 🎯 自动点击Sign In按钮
- 🧹 清洁浏览器环境，每次如同全新安装
- 🌐 新增浏览器控制功能
- 📊 改进状态显示和日志系统
- 🎨 优化用户界面设计

### v1.0.0
- 📧 基础邮箱生成功能
- 🔍 验证码获取功能
- ⚙️ 配置管理功能
- 📋 日志查看功能
