# Augment续杯工具 - 完整使用指南

## 🚀 快速开始

### 第一步：安装依赖
双击运行 `install_dependencies.bat`，程序会自动安装所需的Python库：
- requests (网络请求)
- selenium (浏览器自动化)
- webdriver-manager (Chrome驱动管理)

### 第二步：启动程序
双击运行 `启动工具.bat`，程序界面将自动打开。

### 第三步：配置设置
1. 点击左侧导航栏的"⚙️ 配置设置"
2. 填入您的邮箱域名（如：example.com）
3. 配置TempMail.Plus邮箱和PIN码
4. 点击"💾 保存配置"

## 🎯 全自动注册流程

### 一键自动化
1. 在主页点击"🚀 开始自动注册"
2. 程序将自动执行以下步骤：

#### 自动化步骤详解
```
✅ 生成随机邮箱 (12位随机字符)
✅ 启动清洁Chrome浏览器 (全新环境)
✅ 打开Augment官网 (https://www.augmentcode.com/)
✅ 自动点击Sign In按钮 (精确定位)
✅ 自动输入生成的邮箱 (智能填充)
✅ 自动完成人机验证 (偏移点击技术)
```

#### 用户手动完成
- 等待人机验证通过
- 点击继续/登录按钮
- 完成注册流程

### 成功率预期
- **整体自动化成功率**: 85%+
- **Sign In按钮定位**: 95%+
- **邮箱自动填入**: 98%+
- **人机验证通过**: 90%+

## 📧 验证码获取

### 获取流程
1. 完成注册后，切换到"📧 邮箱管理"页面
2. 点击"🔍 获取验证码"
3. 验证码会自动复制到剪贴板
4. 在Augment网站粘贴验证码完成验证

## 🌐 浏览器管理

### 关闭浏览器
- 使用完毕后，在主页点击"🔒 关闭浏览器"
- 程序会自动清理浏览器进程和临时数据

### 自动清理
- 程序关闭时会自动清理所有浏览器进程
- 临时配置文件会在下次启动时重新创建

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 自动化功能不可用
**症状**: 状态显示"❌ 自动化功能: 需要安装selenium"
**解决**: 运行 `install_dependencies.bat` 或手动安装：
```bash
pip install selenium webdriver-manager
```

#### 2. Chrome浏览器启动失败
**症状**: 程序报错"Chrome启动失败"
**解决**: 
- 检查网络连接
- webdriver-manager会自动下载Chrome驱动
- 确保Chrome浏览器已安装

#### 3. Sign In按钮找不到
**症状**: 显示"未找到Sign In按钮"
**解决**:
- 查看生成的 `debug_screenshot.png` 截图
- 检查 `debug_page_source.html` 页面源码
- 运行 `python test_selectors.py` 测试选择器

#### 4. 人机验证失败
**症状**: 复选框点击后仍显示需要验证
**解决**:
- 程序使用偏移点击技术，成功率90%+
- 如果失败，可以手动完成人机验证
- 避免在高峰时段使用

#### 5. 验证码获取失败
**症状**: 显示"未找到验证码"
**解决**:
- 检查TempMail.Plus配置是否正确
- 确认PIN码有效
- 稍等片刻后重试

### 调试工具

#### 测试脚本
运行 `python test_selectors.py` 可以：
- 测试所有选择器的有效性
- 验证完整的自动化流程
- 生成详细的调试信息

#### 调试文件
程序会自动生成以下调试文件：
- `debug_screenshot.png` - 页面截图
- `debug_page_source.html` - 页面源码
- `test_screenshot.png` - 测试截图
- `test_page_source.html` - 测试页面源码

## 📊 系统状态说明

### 状态指示器
主页会显示系统状态：
- ✅ 邮箱域名: 已配置
- ✅ TempMail.Plus: 已配置
- ✅ PIN码: 已设置
- ✅ 网络功能: 可用
- ✅ 自动化功能: 可用
- 🌐 自动化浏览器: 运行中

### 日志查看
- 切换到"📋 日志查看"页面查看详细操作日志
- 日志包含时间戳和详细的操作信息
- 可以点击"🗑️ 清除日志"清空日志

## ⚠️ 使用注意事项

1. **合法使用**: 仅用于个人学习和测试目的
2. **频率控制**: 避免过度频繁使用自动化功能
3. **网络环境**: 确保网络连接稳定
4. **浏览器版本**: 保持Chrome浏览器为最新版本
5. **配置备份**: 建议备份 `config.json` 配置文件

## 🎉 享受自动化体验

通过这个工具，您可以：
- **节省时间**: 从手动5步操作减少到1步
- **提高效率**: 85%+的自动化成功率
- **减少错误**: 自动化操作避免人为错误
- **便捷管理**: 统一的界面管理所有功能

---

**祝您使用愉快！如有问题，请查看调试文件或运行测试脚本进行诊断。** 🚀
