#!/usr/bin/env python3
"""
专门调试复选框问题的脚本
"""

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.action_chains import ActionChains
    from webdriver_manager.chrome import ChromeDriverManager
    import time
    import os
    
    print("🚀 启动复选框调试...")
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--start-maximized")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)
    
    # 清洁环境
    temp_profile = os.path.join(os.getcwd(), "temp_chrome_profile")
    if os.path.exists(temp_profile):
        import shutil
        shutil.rmtree(temp_profile, ignore_errors=True)
    chrome_options.add_argument(f"--user-data-dir={temp_profile}")
    
    # 创建WebDriver
    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    
    try:
        print("🌐 打开Augment网站...")
        driver.get("https://www.augmentcode.com/")
        
        wait = WebDriverWait(driver, 15)
        wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
        time.sleep(3)
        
        print("🔍 查找Sign In按钮...")
        sign_in_button = None
        selectors = [
            "//a[@href='https://app.augmentcode.com']",
            "//a[contains(text(), 'Sign in')]",
            "//a[contains(text(), 'Sign In')]"
        ]
        
        for selector in selectors:
            try:
                sign_in_button = driver.find_element(By.XPATH, selector)
                if sign_in_button.is_displayed():
                    print(f"✅ 找到Sign In按钮: {selector}")
                    break
            except:
                continue
        
        if sign_in_button:
            print("🎯 点击Sign In按钮...")
            sign_in_button.click()
            time.sleep(5)  # 等待页面跳转
            
            print("📧 查找邮箱输入框...")
            email_input = None
            try:
                email_input = driver.find_element(By.XPATH, "//input[@name='username']")
                if email_input.is_displayed():
                    print("✅ 找到邮箱输入框")
                    email_input.clear()
                    email_input.send_keys("<EMAIL>")
                    print("✅ 已输入测试邮箱")
                    time.sleep(2)
            except Exception as e:
                print(f"❌ 邮箱输入框问题: {e}")
            
            print("⏳ 等待8秒让复选框加载...")
            time.sleep(8)
            
            print("🔍 开始详细分析页面上的所有元素...")
            
            # 保存页面截图
            driver.save_screenshot("debug_full_page.png")
            print("📸 已保存页面截图: debug_full_page.png")
            
            # 保存页面源码
            with open("debug_full_source.html", "w", encoding="utf-8") as f:
                f.write(driver.page_source)
            print("📄 已保存页面源码: debug_full_source.html")
            
            # 查找所有input元素
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"\n🔍 页面上共找到 {len(all_inputs)} 个input元素:")
            
            checkbox_found = False
            for i, inp in enumerate(all_inputs):
                try:
                    inp_type = inp.get_attribute("type") or "未知"
                    inp_class = inp.get_attribute("class") or "无"
                    inp_id = inp.get_attribute("id") or "无"
                    inp_name = inp.get_attribute("name") or "无"
                    inp_value = inp.get_attribute("value") or "无"
                    is_displayed = inp.is_displayed()
                    is_enabled = inp.is_enabled()
                    
                    location = inp.location if is_displayed else {"x": 0, "y": 0}
                    size = inp.size if is_displayed else {"width": 0, "height": 0}
                    
                    print(f"\n   Input {i+1}:")
                    print(f"     type: '{inp_type}'")
                    print(f"     class: '{inp_class}'")
                    print(f"     id: '{inp_id}'")
                    print(f"     name: '{inp_name}'")
                    print(f"     value: '{inp_value}'")
                    print(f"     displayed: {is_displayed}")
                    print(f"     enabled: {is_enabled}")
                    print(f"     location: x={location['x']}, y={location['y']}")
                    print(f"     size: width={size['width']}, height={size['height']}")
                    
                    if inp_type.lower() == "checkbox":
                        checkbox_found = True
                        print(f"     ⭐ 这是一个复选框！")
                        
                        if is_displayed and is_enabled:
                            print(f"     🎯 尝试点击这个复选框...")
                            
                            # 尝试多种点击方法
                            methods = [
                                ("直接点击", lambda: inp.click()),
                                ("JavaScript点击", lambda: driver.execute_script("arguments[0].click();", inp)),
                                ("偏移点击", lambda: ActionChains(driver).move_to_element_with_offset(inp, 5, 5).click().perform()),
                                ("滚动后点击", lambda: (driver.execute_script("arguments[0].scrollIntoView(true);", inp), time.sleep(1), inp.click())[2])
                            ]
                            
                            for method_name, method_func in methods:
                                try:
                                    print(f"       尝试 {method_name}...")
                                    method_func()
                                    print(f"       ✅ {method_name} 成功！")
                                    time.sleep(2)
                                    break
                                except Exception as e:
                                    print(f"       ❌ {method_name} 失败: {e}")
                        else:
                            print(f"     ⚠️ 复选框不可见或不可用")
                            
                except Exception as e:
                    print(f"   Input {i+1}: 分析失败 - {e}")
            
            if not checkbox_found:
                print("\n❌ 没有找到任何checkbox类型的input元素")
                
                # 查找其他可能的复选框元素
                print("\n🔍 查找其他可能的复选框元素...")
                
                # 查找所有可能包含复选框的元素
                possible_selectors = [
                    "//div[contains(@class, 'checkbox')]",
                    "//div[contains(@class, 'check')]",
                    "//label[contains(@class, 'checkbox')]",
                    "//span[contains(@class, 'checkbox')]",
                    "//*[contains(@role, 'checkbox')]",
                    "//*[contains(text(), '验证') or contains(text(), 'verify') or contains(text(), 'captcha')]"
                ]
                
                for selector in possible_selectors:
                    try:
                        elements = driver.find_elements(By.XPATH, selector)
                        if elements:
                            print(f"\n   找到 {len(elements)} 个匹配 '{selector}' 的元素:")
                            for j, elem in enumerate(elements):
                                try:
                                    tag_name = elem.tag_name
                                    elem_class = elem.get_attribute("class") or "无"
                                    elem_text = elem.text[:50] if elem.text else "无文本"
                                    is_displayed = elem.is_displayed()
                                    print(f"     元素 {j+1}: <{tag_name}> class='{elem_class}' text='{elem_text}' visible={is_displayed}")
                                except:
                                    pass
                    except:
                        pass
            
            print(f"\n📋 调试完成！请查看以下文件:")
            print(f"   - debug_full_page.png (页面截图)")
            print(f"   - debug_full_source.html (页面源码)")
            
        else:
            print("❌ 未找到Sign In按钮")
        
        print("\n按回车键关闭浏览器...")
        input()
        
    finally:
        driver.quit()
        print("🔒 浏览器已关闭")
        
except ImportError:
    print("❌ 请先安装selenium和webdriver-manager:")
    print("pip install selenium webdriver-manager")
except Exception as e:
    print(f"❌ 调试失败: {e}")
