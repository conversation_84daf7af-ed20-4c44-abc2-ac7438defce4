{"version": 3, "file": "web-vitals.js", "sources": ["../../../node_modules/.pnpm/web-vitals@4.2.4/node_modules/web-vitals/dist/web-vitals.attribution.js", "../src/utils/globals.ts", "../src/entrypoints/web-vitals.ts"], "sourcesContent": ["var t,e,n=function(){var t=self.performance&&performance.getEntriesByType&&performance.getEntriesByType(\"navigation\")[0];if(t&&t.responseStart>0&&t.responseStart<performance.now())return t},r=function(t){if(\"loading\"===document.readyState)return\"loading\";var e=n();if(e){if(t<e.domInteractive)return\"loading\";if(0===e.domContentLoadedEventStart||t<e.domContentLoadedEventStart)return\"dom-interactive\";if(0===e.domComplete||t<e.domComplete)return\"dom-content-loaded\"}return\"complete\"},i=function(t){var e=t.nodeName;return 1===t.nodeType?e.toLowerCase():e.toUpperCase().replace(/^#/,\"\")},a=function(t,e){var n=\"\";try{for(;t&&9!==t.nodeType;){var r=t,a=r.id?\"#\"+r.id:i(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?\".\"+r.classList.value.trim().replace(/\\s+/g,\".\"):\"\");if(n.length+a.length>(e||100)-1)return n||a;if(n=n?a+\">\"+n:a,r.id)break;t=r.parentNode}}catch(t){}return n},o=-1,c=function(){return o},u=function(t){addEventListener(\"pageshow\",(function(e){e.persisted&&(o=e.timeStamp,t(e))}),!0)},s=function(){var t=n();return t&&t.activationStart||0},f=function(t,e){var r=n(),i=\"navigate\";c()>=0?i=\"back-forward-cache\":r&&(document.prerendering||s()>0?i=\"prerender\":document.wasDiscarded?i=\"restore\":r.type&&(i=r.type.replace(/_/g,\"-\")));return{name:t,value:void 0===e?-1:e,rating:\"good\",delta:0,entries:[],id:\"v4-\".concat(Date.now(),\"-\").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:i}},d=function(t,e,n){try{if(PerformanceObserver.supportedEntryTypes.includes(t)){var r=new PerformanceObserver((function(t){Promise.resolve().then((function(){e(t.getEntries())}))}));return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch(t){}},l=function(t,e,n,r){var i,a;return function(o){e.value>=0&&(o||r)&&((a=e.value-(i||0))||void 0===i)&&(i=e.value,e.delta=a,e.rating=function(t,e){return t>e[1]?\"poor\":t>e[0]?\"needs-improvement\":\"good\"}(e.value,n),t(e))}},m=function(t){requestAnimationFrame((function(){return requestAnimationFrame((function(){return t()}))}))},p=function(t){document.addEventListener(\"visibilitychange\",(function(){\"hidden\"===document.visibilityState&&t()}))},v=function(t){var e=!1;return function(){e||(t(),e=!0)}},g=-1,h=function(){return\"hidden\"!==document.visibilityState||document.prerendering?1/0:0},T=function(t){\"hidden\"===document.visibilityState&&g>-1&&(g=\"visibilitychange\"===t.type?t.timeStamp:0,E())},y=function(){addEventListener(\"visibilitychange\",T,!0),addEventListener(\"prerenderingchange\",T,!0)},E=function(){removeEventListener(\"visibilitychange\",T,!0),removeEventListener(\"prerenderingchange\",T,!0)},S=function(){return g<0&&(g=h(),y(),u((function(){setTimeout((function(){g=h(),y()}),0)}))),{get firstHiddenTime(){return g}}},b=function(t){document.prerendering?addEventListener(\"prerenderingchange\",(function(){return t()}),!0):t()},L=[1800,3e3],C=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FCP\"),a=d(\"paint\",(function(t){t.forEach((function(t){\"first-contentful-paint\"===t.name&&(a.disconnect(),t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries.push(t),n(!0)))}))}));a&&(n=l(t,i,L,e.reportAllChanges),u((function(r){i=f(\"FCP\"),n=l(t,i,L,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,n(!0)}))})))}))},M=[.1,.25],D=function(t,e){!function(t,e){e=e||{},C(v((function(){var n,r=f(\"CLS\",0),i=0,a=[],o=function(t){t.forEach((function(t){if(!t.hadRecentInput){var e=a[0],n=a[a.length-1];i&&t.startTime-n.startTime<1e3&&t.startTime-e.startTime<5e3?(i+=t.value,a.push(t)):(i=t.value,a=[t])}})),i>r.value&&(r.value=i,r.entries=a,n())},c=d(\"layout-shift\",o);c&&(n=l(t,r,M,e.reportAllChanges),p((function(){o(c.takeRecords()),n(!0)})),u((function(){i=0,r=f(\"CLS\",0),n=l(t,r,M,e.reportAllChanges),m((function(){return n()}))})),setTimeout(n,0))})))}((function(e){var n=function(t){var e,n={};if(t.entries.length){var i=t.entries.reduce((function(t,e){return t&&t.value>e.value?t:e}));if(i&&i.sources&&i.sources.length){var o=(e=i.sources).find((function(t){return t.node&&1===t.node.nodeType}))||e[0];o&&(n={largestShiftTarget:a(o.node),largestShiftTime:i.startTime,largestShiftValue:i.value,largestShiftSource:o,largestShiftEntry:i,loadState:r(i.startTime)})}}return Object.assign(t,{attribution:n})}(e);t(n)}),e)},w=function(t,e){C((function(e){var i=function(t){var e={timeToFirstByte:0,firstByteToFCP:t.value,loadState:r(c())};if(t.entries.length){var i=n(),a=t.entries[t.entries.length-1];if(i){var o=i.activationStart||0,u=Math.max(0,i.responseStart-o);e={timeToFirstByte:u,firstByteToFCP:t.value-u,loadState:r(t.entries[0].startTime),navigationEntry:i,fcpEntry:a}}}return Object.assign(t,{attribution:e})}(e);t(i)}),e)},x=0,I=1/0,k=0,A=function(t){t.forEach((function(t){t.interactionId&&(I=Math.min(I,t.interactionId),k=Math.max(k,t.interactionId),x=k?(k-I)/7+1:0)}))},F=function(){return t?x:performance.interactionCount||0},P=function(){\"interactionCount\"in performance||t||(t=d(\"event\",A,{type:\"event\",buffered:!0,durationThreshold:0}))},B=[],O=new Map,R=0,j=function(){var t=Math.min(B.length-1,Math.floor((F()-R)/50));return B[t]},q=[],H=function(t){if(q.forEach((function(e){return e(t)})),t.interactionId||\"first-input\"===t.entryType){var e=B[B.length-1],n=O.get(t.interactionId);if(n||B.length<10||t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===n.entries[0].startTime&&n.entries.push(t);else{var r={id:t.interactionId,latency:t.duration,entries:[t]};O.set(r.id,r),B.push(r)}B.sort((function(t,e){return e.latency-t.latency})),B.length>10&&B.splice(10).forEach((function(t){return O.delete(t.id)}))}}},N=function(t){var e=self.requestIdleCallback||self.setTimeout,n=-1;return t=v(t),\"hidden\"===document.visibilityState?t():(n=e(t),p(t)),n},W=[200,500],z=function(t,e){\"PerformanceEventTiming\"in self&&\"interactionId\"in PerformanceEventTiming.prototype&&(e=e||{},b((function(){var n;P();var r,i=f(\"INP\"),a=function(t){N((function(){t.forEach(H);var e=j();e&&e.latency!==i.value&&(i.value=e.latency,i.entries=e.entries,r())}))},o=d(\"event\",a,{durationThreshold:null!==(n=e.durationThreshold)&&void 0!==n?n:40});r=l(t,i,W,e.reportAllChanges),o&&(o.observe({type:\"first-input\",buffered:!0}),p((function(){a(o.takeRecords()),r(!0)})),u((function(){R=F(),B.length=0,O.clear(),i=f(\"INP\"),r=l(t,i,W,e.reportAllChanges)})))})))},U=[],V=[],_=0,G=new WeakMap,J=new Map,K=-1,Q=function(t){U=U.concat(t),X()},X=function(){K<0&&(K=N(Y))},Y=function(){J.size>10&&J.forEach((function(t,e){O.has(e)||J.delete(e)}));var t=B.map((function(t){return G.get(t.entries[0])})),e=V.length-50;V=V.filter((function(n,r){return r>=e||t.includes(n)}));for(var n=new Set,r=0;r<V.length;r++){var i=V[r];nt(i.startTime,i.processingEnd).forEach((function(t){n.add(t)}))}var a=U.length-1-50;U=U.filter((function(t,e){return t.startTime>_&&e>a||n.has(t)})),K=-1};q.push((function(t){t.interactionId&&t.target&&!J.has(t.interactionId)&&J.set(t.interactionId,t.target)}),(function(t){var e,n=t.startTime+t.duration;_=Math.max(_,t.processingEnd);for(var r=V.length-1;r>=0;r--){var i=V[r];if(Math.abs(n-i.renderTime)<=8){(e=i).startTime=Math.min(t.startTime,e.startTime),e.processingStart=Math.min(t.processingStart,e.processingStart),e.processingEnd=Math.max(t.processingEnd,e.processingEnd),e.entries.push(t);break}}e||(e={startTime:t.startTime,processingStart:t.processingStart,processingEnd:t.processingEnd,renderTime:n,entries:[t]},V.push(e)),(t.interactionId||\"first-input\"===t.entryType)&&G.set(t,e),X()}));var Z,$,tt,et,nt=function(t,e){for(var n,r=[],i=0;n=U[i];i++)if(!(n.startTime+n.duration<t)){if(n.startTime>e)break;r.push(n)}return r},rt=function(t,n){e||(e=d(\"long-animation-frame\",Q)),z((function(e){var n=function(t){var e=t.entries[0],n=G.get(e),i=e.processingStart,o=n.processingEnd,c=n.entries.sort((function(t,e){return t.processingStart-e.processingStart})),u=nt(e.startTime,o),s=t.entries.find((function(t){return t.target})),f=s&&s.target||J.get(e.interactionId),d=[e.startTime+e.duration,o].concat(u.map((function(t){return t.startTime+t.duration}))),l=Math.max.apply(Math,d),m={interactionTarget:a(f),interactionTargetElement:f,interactionType:e.name.startsWith(\"key\")?\"keyboard\":\"pointer\",interactionTime:e.startTime,nextPaintTime:l,processedEventEntries:c,longAnimationFrameEntries:u,inputDelay:i-e.startTime,processingDuration:o-i,presentationDelay:Math.max(l-o,0),loadState:r(e.startTime)};return Object.assign(t,{attribution:m})}(e);t(n)}),n)},it=[2500,4e3],at={},ot=function(t,e){!function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"LCP\"),a=function(t){e.reportAllChanges||(t=t.slice(-1)),t.forEach((function(t){t.startTime<r.firstHiddenTime&&(i.value=Math.max(t.startTime-s(),0),i.entries=[t],n())}))},o=d(\"largest-contentful-paint\",a);if(o){n=l(t,i,it,e.reportAllChanges);var c=v((function(){at[i.id]||(a(o.takeRecords()),o.disconnect(),at[i.id]=!0,n(!0))}));[\"keydown\",\"click\"].forEach((function(t){addEventListener(t,(function(){return N(c)}),{once:!0,capture:!0})})),p(c),u((function(r){i=f(\"LCP\"),n=l(t,i,it,e.reportAllChanges),m((function(){i.value=performance.now()-r.timeStamp,at[i.id]=!0,n(!0)}))}))}}))}((function(e){var r=function(t){var e={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){var r=n();if(r){var i=r.activationStart||0,o=t.entries[t.entries.length-1],c=o.url&&performance.getEntriesByType(\"resource\").filter((function(t){return t.name===o.url}))[0],u=Math.max(0,r.responseStart-i),s=Math.max(u,c?(c.requestStart||c.startTime)-i:0),f=Math.max(s,c?c.responseEnd-i:0),d=Math.max(f,o.startTime-i);e={element:a(o.element),timeToFirstByte:u,resourceLoadDelay:s-u,resourceLoadDuration:f-s,elementRenderDelay:d-f,navigationEntry:r,lcpEntry:o},o.url&&(e.url=o.url),c&&(e.lcpResourceEntry=c)}}return Object.assign(t,{attribution:e})}(e);t(r)}),e)},ct=[800,1800],ut=function t(e){document.prerendering?b((function(){return t(e)})):\"complete\"!==document.readyState?addEventListener(\"load\",(function(){return t(e)}),!0):setTimeout(e,0)},st=function(t,e){e=e||{};var r=f(\"TTFB\"),i=l(t,r,ct,e.reportAllChanges);ut((function(){var a=n();a&&(r.value=Math.max(a.responseStart-s(),0),r.entries=[a],i(!0),u((function(){r=f(\"TTFB\",0),(i=l(t,r,ct,e.reportAllChanges))(!0)})))}))},ft=function(t,e){st((function(e){var n=function(t){var e={waitingDuration:0,cacheDuration:0,dnsDuration:0,connectionDuration:0,requestDuration:0};if(t.entries.length){var n=t.entries[0],r=n.activationStart||0,i=Math.max((n.workerStart||n.fetchStart)-r,0),a=Math.max(n.domainLookupStart-r,0),o=Math.max(n.connectStart-r,0),c=Math.max(n.connectEnd-r,0);e={waitingDuration:i,cacheDuration:a-i,dnsDuration:o-a,connectionDuration:c-o,requestDuration:t.value-c,navigationEntry:n}}return Object.assign(t,{attribution:e})}(e);t(n)}),e)},dt={passive:!0,capture:!0},lt=new Date,mt=function(t,e){Z||(Z=e,$=t,tt=new Date,gt(removeEventListener),pt())},pt=function(){if($>=0&&$<tt-lt){var t={entryType:\"first-input\",name:Z.type,target:Z.target,cancelable:Z.cancelable,startTime:Z.timeStamp,processingStart:Z.timeStamp+$};et.forEach((function(e){e(t)})),et=[]}},vt=function(t){if(t.cancelable){var e=(t.timeStamp>1e12?new Date:performance.now())-t.timeStamp;\"pointerdown\"==t.type?function(t,e){var n=function(){mt(t,e),i()},r=function(){i()},i=function(){removeEventListener(\"pointerup\",n,dt),removeEventListener(\"pointercancel\",r,dt)};addEventListener(\"pointerup\",n,dt),addEventListener(\"pointercancel\",r,dt)}(e,t):mt(e,t)}},gt=function(t){[\"mousedown\",\"keydown\",\"touchstart\",\"pointerdown\"].forEach((function(e){return t(e,vt,dt)}))},ht=[100,300],Tt=function(t,e){e=e||{},b((function(){var n,r=S(),i=f(\"FID\"),a=function(t){t.startTime<r.firstHiddenTime&&(i.value=t.processingStart-t.startTime,i.entries.push(t),n(!0))},o=function(t){t.forEach(a)},c=d(\"first-input\",o);n=l(t,i,ht,e.reportAllChanges),c&&(p(v((function(){o(c.takeRecords()),c.disconnect()}))),u((function(){var r;i=f(\"FID\"),n=l(t,i,ht,e.reportAllChanges),et=[],$=-1,Z=null,gt(addEventListener),r=a,et.push(r),pt()})))}))},yt=function(t,e){Tt((function(e){var n=function(t){var e=t.entries[0],n={eventTarget:a(e.target),eventType:e.name,eventTime:e.startTime,eventEntry:e,loadState:r(e.startTime)};return Object.assign(t,{attribution:n})}(e);t(n)}),e)};export{M as CLSThresholds,L as FCPThresholds,ht as FIDThresholds,W as INPThresholds,it as LCPThresholds,ct as TTFBThresholds,D as onCLS,w as onFCP,yt as onFID,rt as onINP,ot as onLCP,ft as onTTFB};\n", "import { ErrorProperties } from '../extensions/exception-autocapture/error-conversion'\nimport type { PostHog } from '../posthog-core'\nimport { SessionIdManager } from '../sessionid'\nimport { DeadClicksAutoCaptureConfig, RemoteConfig, SiteAppLoader } from '../types'\n\n/*\n * Global helpers to protect access to browser globals in a way that is safer for different targets\n * like DOM, SSR, Web workers etc.\n *\n * NOTE: Typically we want the \"window\" but globalThis works for both the typical browser context as\n * well as other contexts such as the web worker context. Window is still exported for any bits that explicitly require it.\n * If in doubt - export the global you need from this file and use that as an optional value. This way the code path is forced\n * to handle the case where the global is not available.\n */\n\n// eslint-disable-next-line no-restricted-globals\nconst win: (Window & typeof globalThis) | undefined = typeof window !== 'undefined' ? window : undefined\n\nexport type AssignableWindow = Window &\n    typeof globalThis & {\n        __PosthogExtensions__?: PostHogExtensions\n\n        _POSTHOG_REMOTE_CONFIG?: Record<\n            string,\n            {\n                config: RemoteConfig\n                siteApps: SiteAppLoader[]\n            }\n        >\n\n        doNotTrack: any\n        posthogCustomizations: any\n        posthogErrorWrappingFunctions: any\n        rrweb: any\n        rrwebConsoleRecord: any\n        getRecordNetworkPlugin: any\n        POSTHOG_DEBUG: any\n        posthog: any\n        ph_load_toolbar: any\n        ph_load_editor: any\n        ph_toolbar_state: any\n        postHogWebVitalsCallbacks: any\n        postHogTracingHeadersPatchFns: any\n        extendPostHogWithSurveys: any\n    } & Record<`__$$ph_site_app_${string}`, any>\n\n/**\n * This is our contract between (potentially) lazily loaded extensions and the SDK\n * changes to this interface can be breaking changes for users of the SDK\n */\n\nexport type PostHogExtensionKind =\n    | 'toolbar'\n    | 'exception-autocapture'\n    | 'web-vitals'\n    | 'recorder'\n    | 'tracing-headers'\n    | 'surveys'\n    | 'dead-clicks-autocapture'\n    | 'remote-config'\n\nexport interface LazyLoadedDeadClicksAutocaptureInterface {\n    start: (observerTarget: Node) => void\n    stop: () => void\n}\n\ninterface PostHogExtensions {\n    loadExternalDependency?: (\n        posthog: PostHog,\n        kind: PostHogExtensionKind,\n        callback: (error?: string | Event, event?: Event) => void\n    ) => void\n\n    loadSiteApp?: (posthog: PostHog, appUrl: string, callback: (error?: string | Event, event?: Event) => void) => void\n\n    errorWrappingFunctions?: {\n        wrapOnError: (captureFn: (props: ErrorProperties) => void) => () => void\n        wrapUnhandledRejection: (captureFn: (props: ErrorProperties) => void) => () => void\n        wrapConsoleError: (captureFn: (props: ErrorProperties) => void) => () => void\n    }\n    rrweb?: { record: any; version: string }\n    rrwebPlugins?: { getRecordConsolePlugin: any; getRecordNetworkPlugin?: any }\n    generateSurveys?: (posthog: PostHog) => any | undefined\n    postHogWebVitalsCallbacks?: {\n        onLCP: (metric: any) => void\n        onCLS: (metric: any) => void\n        onFCP: (metric: any) => void\n        onINP: (metric: any) => void\n    }\n    tracingHeadersPatchFns?: {\n        _patchFetch: (sessionManager?: SessionIdManager) => () => void\n        _patchXHR: (sessionManager?: SessionIdManager) => () => void\n    }\n    initDeadClicksAutocapture?: (\n        ph: PostHog,\n        config: DeadClicksAutoCaptureConfig\n    ) => LazyLoadedDeadClicksAutocaptureInterface\n}\n\nconst global: typeof globalThis | undefined = typeof globalThis !== 'undefined' ? globalThis : win\n\nexport const ArrayProto = Array.prototype\nexport const nativeForEach = ArrayProto.forEach\nexport const nativeIndexOf = ArrayProto.indexOf\n\nexport const navigator = global?.navigator\nexport const document = global?.document\nexport const location = global?.location\nexport const fetch = global?.fetch\nexport const XMLHttpRequest =\n    global?.XMLHttpRequest && 'withCredentials' in new global.XMLHttpRequest() ? global.XMLHttpRequest : undefined\nexport const AbortController = global?.AbortController\nexport const userAgent = navigator?.userAgent\nexport const assignableWindow: AssignableWindow = win ?? ({} as any)\n\nexport { win as window }\n", "import { onINP, onLCP, onCLS, onFCP } from 'web-vitals/attribution'\nimport { assignableWindow } from '../utils/globals'\n\nconst postHogWebVitalsCallbacks = {\n    onLCP,\n    onCLS,\n    onFCP,\n    onINP,\n}\n\nassignableWindow.__PosthogExtensions__ = assignableWindow.__PosthogExtensions__ || {}\nassignableWindow.__PosthogExtensions__.postHogWebVitalsCallbacks = postHogWebVitalsCallbacks\n\n// we used to put posthogWebVitalsCallbacks on window, and now we put it on __PosthogExtensions__\n// but that means that old clients which lazily load this extension are looking in the wrong place\n// yuck,\n// so we also put it directly on the window\n// when 1.161.1 is the oldest version seen in production we can remove this\nassignableWindow.postHogWebVitalsCallbacks = postHogWebVitalsCallbacks\n\nexport default postHogWebVitalsCallbacks\n"], "names": ["t", "e", "n", "self", "performance", "getEntriesByType", "responseStart", "now", "r", "document", "readyState", "domInteractive", "domContentLoadedEventStart", "domComplete", "i", "nodeName", "nodeType", "toLowerCase", "toUpperCase", "replace", "a", "id", "classList", "value", "trim", "length", "parentNode", "o", "c", "u", "addEventListener", "persisted", "timeStamp", "s", "activationStart", "f", "prerendering", "wasDiscarded", "type", "name", "rating", "delta", "entries", "concat", "Date", "Math", "floor", "random", "navigationType", "d", "PerformanceObserver", "supportedEntryTypes", "includes", "Promise", "resolve", "then", "getEntries", "observe", "Object", "assign", "buffered", "l", "m", "requestAnimationFrame", "p", "visibilityState", "v", "g", "h", "T", "E", "y", "removeEventListener", "S", "setTimeout", "firstHiddenTime", "b", "L", "C", "for<PERSON>ach", "disconnect", "startTime", "max", "push", "reportAllChanges", "M", "x", "I", "k", "A", "interactionId", "min", "F", "interactionCount", "P", "durationThreshold", "B", "O", "Map", "R", "q", "H", "entryType", "get", "duration", "latency", "set", "sort", "splice", "delete", "N", "requestIdleCallback", "W", "z", "PerformanceEventTiming", "prototype", "j", "takeRecords", "clear", "U", "V", "_", "G", "WeakMap", "J", "K", "Q", "X", "Y", "size", "has", "map", "filter", "Set", "nt", "processingEnd", "add", "target", "abs", "renderTime", "processingStart", "it", "at", "win", "window", "undefined", "global", "globalThis", "navigator", "location", "fetch", "XMLHttpRequest", "AbortController", "userAgent", "assignableWindow", "postHogWebVitalsCallbacks", "slice", "once", "capture", "timeToFirstByte", "resourceLoadDelay", "resourceLoadDuration", "elementRenderDelay", "url", "requestStart", "responseEnd", "element", "navigationEntry", "lcpEntry", "lcpResourceEntry", "attribution", "hadRecentInput", "reduce", "sources", "find", "node", "largestShiftTarget", "largestShiftTime", "largestShiftValue", "largestShiftSource", "largestShiftEntry", "loadState", "firstByteToFCP", "fcpEntry", "onINP", "apply", "interaction<PERSON>arget", "interactionTargetElement", "interactionType", "startsWith", "interactionTime", "nextPaintTime", "processedEventEntries", "longAnimationFrameEntries", "inputDelay", "processingDuration", "presentationDelay", "__PosthogExtensions__"], "mappings": "yBAAA,IAAIA,EAAEC,EAAEC,EAAE,WAAW,IAAIF,EAAEG,KAAKC,aAAaA,YAAYC,kBAAkBD,YAAYC,iBAAiB,cAAc,GAAG,GAAGL,GAAGA,EAAEM,cAAc,GAAGN,EAAEM,cAAcF,YAAYG,MAAM,OAAOP,CAAE,EAACQ,EAAE,SAASR,GAAG,GAAG,YAAYS,SAASC,WAAW,MAAM,UAAU,IAAIT,EAAEC,IAAI,GAAGD,EAAE,CAAC,GAAGD,EAAEC,EAAEU,eAAe,MAAM,UAAU,GAAG,IAAIV,EAAEW,4BAA4BZ,EAAEC,EAAEW,2BAA2B,MAAM,kBAAkB,GAAG,IAAIX,EAAEY,aAAab,EAAEC,EAAEY,YAAY,MAAM,oBAAoB,CAAC,MAAM,UAAW,EAACC,EAAE,SAASd,GAAG,IAAIC,EAAED,EAAEe,SAAS,OAAO,IAAIf,EAAEgB,SAASf,EAAEgB,cAAchB,EAAEiB,cAAcC,QAAQ,KAAK,GAAI,EAACC,EAAE,SAASpB,EAAEC,GAAG,IAAIC,EAAE,GAAG,IAAI,KAAKF,GAAG,IAAIA,EAAEgB,UAAU,CAAC,IAAIR,EAAER,EAAEoB,EAAEZ,EAAEa,GAAG,IAAIb,EAAEa,GAAGP,EAAEN,IAAIA,EAAEc,WAAWd,EAAEc,UAAUC,OAAOf,EAAEc,UAAUC,MAAMC,QAAQhB,EAAEc,UAAUC,MAAMC,OAAOC,OAAO,IAAIjB,EAAEc,UAAUC,MAAMC,OAAOL,QAAQ,OAAO,KAAK,IAAI,GAAGjB,EAAEuB,OAAOL,EAAEK,QAAQxB,GAAG,KAAK,EAAE,OAAOC,GAAGkB,EAAE,GAAGlB,EAAEA,EAAEkB,EAAE,IAAIlB,EAAEkB,EAAEZ,EAAEa,GAAG,MAAMrB,EAAEQ,EAAEkB,UAAU,CAAE,CAAA,MAAM1B,GAAE,CAAE,OAAOE,CAAE,EAACyB,GAAG,EAAEC,EAAE,WAAW,OAAOD,CAAE,EAACE,EAAE,SAAS7B,GAAG8B,iBAAiB,YAAY,SAAS7B,GAAGA,EAAE8B,YAAYJ,EAAE1B,EAAE+B,UAAUhC,EAAEC,GAAI,IAAE,EAAI,EAACgC,EAAE,WAAW,IAAIjC,EAAEE,IAAI,OAAOF,GAAGA,EAAEkC,iBAAiB,CAAE,EAACC,EAAE,SAASnC,EAAEC,GAAG,IAAIO,EAAEN,IAAIY,EAAE,WAAgK,OAArJc,KAAK,EAAEd,EAAE,qBAAqBN,IAAIC,SAAS2B,cAAcH,IAAI,EAAEnB,EAAE,YAAYL,SAAS4B,aAAavB,EAAE,UAAUN,EAAE8B,OAAOxB,EAAEN,EAAE8B,KAAKnB,QAAQ,KAAK,OAAa,CAACoB,KAAKvC,EAAEuB,WAAM,IAAStB,GAAG,EAAEA,EAAEuC,OAAO,OAAOC,MAAM,EAAEC,QAAQ,GAAGrB,GAAG,MAAMsB,OAAOC,KAAKrC,MAAM,KAAKoC,OAAOE,KAAKC,MAAM,cAAcD,KAAKE,UAAU,MAAMC,eAAelC,EAAG,EAACmC,EAAE,SAASjD,EAAEC,EAAEC,GAAG,IAAI,GAAGgD,oBAAoBC,oBAAoBC,SAASpD,GAAG,CAAC,IAAIQ,EAAE,IAAI0C,qBAAqB,SAASlD,GAAGqD,QAAQC,UAAUC,MAAM,WAAWtD,EAAED,EAAEwD,aAAa,GAAG,IAAI,OAAOhD,EAAEiD,QAAQC,OAAOC,OAAO,CAACrB,KAAKtC,EAAE4D,UAAS,GAAI1D,GAAG,KAAKM,CAAC,CAAE,CAAA,MAAMR,GAAE,CAAG,EAAC6D,EAAE,SAAS7D,EAAEC,EAAEC,EAAEM,GAAG,IAAIM,EAAEM,EAAE,OAAO,SAASO,GAAG1B,EAAEsB,OAAO,IAAII,GAAGnB,MAAMY,EAAEnB,EAAEsB,OAAOT,GAAG,UAAK,IAASA,KAAKA,EAAEb,EAAEsB,MAAMtB,EAAEwC,MAAMrB,EAAEnB,EAAEuC,OAAO,SAASxC,EAAEC,GAAG,OAAOD,EAAEC,EAAE,GAAG,OAAOD,EAAEC,EAAE,GAAG,oBAAoB,MAAM,CAApE,CAAsEA,EAAEsB,MAAMrB,GAAGF,EAAEC,GAAI,CAAC,EAAC6D,EAAE,SAAS9D,GAAG+D,uBAAuB,WAAW,OAAOA,uBAAuB,WAAW,OAAO/D,GAAG,GAAG,GAAI,EAACgE,EAAE,SAAShE,GAAGS,SAASqB,iBAAiB,oBAAoB,WAAW,WAAWrB,SAASwD,iBAAiBjE,GAAG,GAAI,EAACkE,EAAE,SAASlE,GAAG,IAAIC,GAAE,EAAG,OAAO,WAAWA,IAAID,IAAIC,GAAE,EAAI,CAAC,EAACkE,GAAG,EAAEC,EAAE,WAAW,MAAM,WAAW3D,SAASwD,iBAAiBxD,SAAS2B,aAAa,IAAI,CAAE,EAACiC,EAAE,SAASrE,GAAG,WAAWS,SAASwD,iBAAiBE,GAAG,IAAIA,EAAE,qBAAqBnE,EAAEsC,KAAKtC,EAAEgC,UAAU,EAAEsC,IAAK,EAACC,EAAE,WAAWzC,iBAAiB,mBAAmBuC,GAAE,GAAIvC,iBAAiB,qBAAqBuC,GAAE,EAAI,EAACC,EAAE,WAAWE,oBAAoB,mBAAmBH,GAAE,GAAIG,oBAAoB,qBAAqBH,GAAE,EAAI,EAACI,EAAE,WAAW,OAAON,EAAE,IAAIA,EAAEC,IAAIG,IAAI1C,GAAG,WAAW6C,YAAY,WAAWP,EAAEC,IAAIG,GAAI,GAAE,EAAI,KAAG,CAAC,mBAAII,GAAkB,OAAOR,CAAC,EAAG,EAACS,EAAE,SAAS5E,GAAGS,SAAS2B,aAAaN,iBAAiB,sBAAsB,WAAW,OAAO9B,GAAG,IAAG,GAAIA,GAAI,EAAC6E,EAAE,CAAC,KAAK,KAAKC,EAAE,SAAS9E,EAAEC,GAAGA,EAAEA,GAAG,GAAG2E,GAAG,WAAW,IAAI1E,EAAEM,EAAEiE,IAAI3D,EAAEqB,EAAE,OAAOf,EAAE6B,EAAE,SAAS,SAASjD,GAAGA,EAAE+E,SAAS,SAAS/E,GAAG,2BAA2BA,EAAEuC,OAAOnB,EAAE4D,aAAahF,EAAEiF,UAAUzE,EAAEmE,kBAAkB7D,EAAES,MAAMsB,KAAKqC,IAAIlF,EAAEiF,UAAUhD,IAAI,GAAGnB,EAAE4B,QAAQyC,KAAKnF,GAAGE,GAAE,IAAK,GAAG,IAAIkB,IAAIlB,EAAE2D,EAAE7D,EAAEc,EAAE+D,EAAE5E,EAAEmF,kBAAkBvD,GAAG,SAASrB,GAAGM,EAAEqB,EAAE,OAAOjC,EAAE2D,EAAE7D,EAAEc,EAAE+D,EAAE5E,EAAEmF,kBAAkBtB,GAAG,WAAWhD,EAAES,MAAMnB,YAAYG,MAAMC,EAAEwB,UAAU9B,GAAE,EAAG,GAAG,IAAI,GAAI,EAACmF,EAAE,CAAC,GAAG,KAAg4CC,EAAE,EAAEC,EAAE,IAAIC,EAAE,EAAEC,EAAE,SAASzF,GAAGA,EAAE+E,SAAS,SAAS/E,GAAGA,EAAE0F,gBAAgBH,EAAE1C,KAAK8C,IAAIJ,EAAEvF,EAAE0F,eAAeF,EAAE3C,KAAKqC,IAAIM,EAAExF,EAAE0F,eAAeJ,EAAEE,GAAGA,EAAED,GAAG,EAAE,EAAE,EAAE,GAAI,EAACK,EAAE,WAAW,OAAO5F,EAAEsF,EAAElF,YAAYyF,kBAAkB,CAAE,EAACC,EAAE,WAAW,qBAAqB1F,aAAaJ,IAAIA,EAAEiD,EAAE,QAAQwC,EAAE,CAACnD,KAAK,QAAQsB,UAAS,EAAGmC,kBAAkB,IAAK,EAACC,EAAE,GAAGC,EAAE,IAAIC,IAAIC,EAAE,EAA8EC,EAAE,GAAGC,EAAE,SAASrG,GAAG,GAAGoG,EAAErB,SAAS,SAAS9E,GAAG,OAAOA,EAAED,EAAI,IAAEA,EAAE0F,eAAe,gBAAgB1F,EAAEsG,UAAU,CAAC,IAAIrG,EAAE+F,EAAEA,EAAEvE,OAAO,GAAGvB,EAAE+F,EAAEM,IAAIvG,EAAE0F,eAAe,GAAGxF,GAAG8F,EAAEvE,OAAO,IAAIzB,EAAEwG,SAASvG,EAAEwG,QAAQ,CAAC,GAAGvG,EAAEF,EAAEwG,SAAStG,EAAEuG,SAASvG,EAAEwC,QAAQ,CAAC1C,GAAGE,EAAEuG,QAAQzG,EAAEwG,UAAUxG,EAAEwG,WAAWtG,EAAEuG,SAASzG,EAAEiF,YAAY/E,EAAEwC,QAAQ,GAAGuC,WAAW/E,EAAEwC,QAAQyC,KAAKnF,OAAO,CAAC,IAAIQ,EAAE,CAACa,GAAGrB,EAAE0F,cAAce,QAAQzG,EAAEwG,SAAS9D,QAAQ,CAAC1C,IAAIiG,EAAES,IAAIlG,EAAEa,GAAGb,GAAGwF,EAAEb,KAAK3E,EAAE,CAACwF,EAAEW,MAAM,SAAS3G,EAAEC,GAAG,OAAOA,EAAEwG,QAAQzG,EAAEyG,OAAO,IAAIT,EAAEvE,OAAO,IAAIuE,EAAEY,OAAO,IAAI7B,SAAS,SAAS/E,GAAG,OAAOiG,EAAEY,OAAO7G,EAAEqB,GAAG,GAAG,CAAC,CAAE,EAACyF,EAAE,SAAS9G,GAAG,IAAIC,EAAEE,KAAK4G,qBAAqB5G,KAAKuE,WAAWxE,GAAG,EAAE,OAAOF,EAAEkE,EAAElE,GAAG,WAAWS,SAASwD,gBAAgBjE,KAAKE,EAAED,EAAED,GAAGgE,EAAEhE,IAAIE,CAAE,EAAC8G,EAAE,CAAC,IAAI,KAAKC,EAAE,SAASjH,EAAEC,GAAG,2BAA2BE,MAAM,kBAAkB+G,uBAAuBC,YAAYlH,EAAEA,GAAG,GAAG2E,GAAG,WAAW,IAAI1E,EAAE4F,IAAI,IAAItF,EAAEM,EAAEqB,EAAE,OAAOf,EAAE,SAASpB,GAAG8G,GAAG,WAAW9G,EAAE+E,QAAQsB,GAAG,IAAIpG,EAAz8B,WAAW,IAAID,EAAE6C,KAAK8C,IAAIK,EAAEvE,OAAO,EAAEoB,KAAKC,OAAO8C,IAAIO,GAAG,KAAK,OAAOH,EAAEhG,EAAG,CAAk4BoH,GAAInH,GAAGA,EAAEwG,UAAU3F,EAAES,QAAQT,EAAES,MAAMtB,EAAEwG,QAAQ3F,EAAE4B,QAAQzC,EAAEyC,QAAQlC,IAAI,GAAI,EAACmB,EAAEsB,EAAE,QAAQ7B,EAAE,CAAC2E,kBAAkB,QAAQ7F,EAAED,EAAE8F,yBAAoB,IAAS7F,EAAEA,EAAE,KAAKM,EAAEqD,EAAE7D,EAAEc,EAAEkG,EAAE/G,EAAEmF,kBAAkBzD,IAAIA,EAAE8B,QAAQ,CAACnB,KAAK,cAAcsB,UAAS,IAAKI,GAAG,WAAW5C,EAAEO,EAAE0F,eAAe7G,GAAE,EAAG,IAAIqB,GAAG,WAAWsE,EAAEP,IAAII,EAAEvE,OAAO,EAAEwE,EAAEqB,QAAQxG,EAAEqB,EAAE,OAAO3B,EAAEqD,EAAE7D,EAAEc,EAAEkG,EAAE/G,EAAEmF,iBAAiB,IAAI,IAAK,EAACmC,EAAE,GAAGC,EAAE,GAAGC,EAAE,EAAEC,EAAE,IAAIC,QAAQC,EAAE,IAAI1B,IAAI2B,GAAG,EAAEC,EAAE,SAAS9H,GAAGuH,EAAEA,EAAE5E,OAAO3C,GAAG+H,GAAI,EAACA,EAAE,WAAWF,EAAE,IAAIA,EAAEf,EAAEkB,GAAI,EAACA,EAAE,WAAWJ,EAAEK,KAAK,IAAIL,EAAE7C,SAAS,SAAS/E,EAAEC,GAAGgG,EAAEiC,IAAIjI,IAAI2H,EAAEf,OAAO5G,EAAE,IAAI,IAAID,EAAEgG,EAAEmC,KAAK,SAASnI,GAAG,OAAO0H,EAAEnB,IAAIvG,EAAE0C,QAAQ,GAAG,IAAIzC,EAAEuH,EAAE/F,OAAO,GAAG+F,EAAEA,EAAEY,QAAQ,SAASlI,EAAEM,GAAG,OAAOA,GAAGP,GAAGD,EAAEoD,SAASlD,EAAE,IAAI,IAAI,IAAIA,EAAE,IAAImI,IAAI7H,EAAE,EAAEA,EAAEgH,EAAE/F,OAAOjB,IAAI,CAAC,IAAIM,EAAE0G,EAAEhH,GAAG8H,EAAGxH,EAAEmE,UAAUnE,EAAEyH,eAAexD,SAAS,SAAS/E,GAAGE,EAAEsI,IAAIxI,EAAE,GAAG,CAAC,IAAIoB,EAAEmG,EAAE9F,OAAO,EAAE,GAAG8F,EAAEA,EAAEa,QAAQ,SAASpI,EAAEC,GAAG,OAAOD,EAAEiF,UAAUwC,GAAGxH,EAAEmB,GAAGlB,EAAEgI,IAAIlI,EAAE,IAAI6H,GAAG,CAAE,EAACzB,EAAEjB,MAAM,SAASnF,GAAGA,EAAE0F,eAAe1F,EAAEyI,SAASb,EAAEM,IAAIlI,EAAE0F,gBAAgBkC,EAAElB,IAAI1G,EAAE0F,cAAc1F,EAAEyI,OAAO,IAAI,SAASzI,GAAG,IAAIC,EAAEC,EAAEF,EAAEiF,UAAUjF,EAAEwG,SAASiB,EAAE5E,KAAKqC,IAAIuC,EAAEzH,EAAEuI,eAAe,IAAI,IAAI/H,EAAEgH,EAAE/F,OAAO,EAAEjB,GAAG,EAAEA,IAAI,CAAC,IAAIM,EAAE0G,EAAEhH,GAAG,GAAGqC,KAAK6F,IAAIxI,EAAEY,EAAE6H,aAAa,EAAE,EAAE1I,EAAEa,GAAGmE,UAAUpC,KAAK8C,IAAI3F,EAAEiF,UAAUhF,EAAEgF,WAAWhF,EAAE2I,gBAAgB/F,KAAK8C,IAAI3F,EAAE4I,gBAAgB3I,EAAE2I,iBAAiB3I,EAAEsI,cAAc1F,KAAKqC,IAAIlF,EAAEuI,cAActI,EAAEsI,eAAetI,EAAEyC,QAAQyC,KAAKnF,GAAG,KAAK,CAAC,CAACC,IAAIA,EAAE,CAACgF,UAAUjF,EAAEiF,UAAU2D,gBAAgB5I,EAAE4I,gBAAgBL,cAAcvI,EAAEuI,cAAcI,WAAWzI,EAAEwC,QAAQ,CAAC1C,IAAIwH,EAAErC,KAAKlF,KAAKD,EAAE0F,eAAe,gBAAgB1F,EAAEsG,YAAYoB,EAAEhB,IAAI1G,EAAEC,GAAG8H,GAAG,IAAI,IAAcO,EAAG,SAAStI,EAAEC,GAAG,IAAI,IAAIC,EAAEM,EAAE,GAAGM,EAAE,EAAEZ,EAAEqH,EAAEzG,GAAGA,IAAI,KAAKZ,EAAE+E,UAAU/E,EAAEsG,SAASxG,GAAG,CAAC,GAAGE,EAAE+E,UAAUhF,EAAE,MAAMO,EAAE2E,KAAKjF,EAAE,CAAC,OAAOM,CAAE,EAA2zBqI,EAAG,CAAC,KAAK,KAAKC,EAAG,CAAE,ECgBp0QC,EAAkE,oBAAXC,OAAyBA,YAASC,EAmFzFC,EAA8D,oBAAfC,WAA6BA,WAAaJ,EAMlFK,GAAYF,MAAAA,OAAAA,EAAAA,EAAQE,UACTF,MAAAA,GAAAA,EAAQzI,SACRyI,MAAAA,GAAAA,EAAQG,SACXH,MAAAA,GAAAA,EAAQI,YAEzBJ,GAAAA,EAAQK,gBAAkB,oBAAqB,IAAIL,EAAOK,gBAAmBL,EAAOK,eACzDL,MAAAA,GAAAA,EAAQM,gBACdJ,MAAAA,IAAAA,GAAWK,UAC7B,IAAMC,GAAqCX,QAAAA,EAAQ,CAAU,EC9G9DY,GAA4B,OFH4yQ,SAAS3J,EAAEC,IAAI,SAASD,EAAEC,GAAGA,EAAEA,GAAG,GAAG2E,GAAG,WAAW,IAAI1E,EAAEM,EAAEiE,IAAI3D,EAAEqB,EAAE,OAAOf,EAAE,SAASpB,GAAGC,EAAEmF,mBAAmBpF,EAAEA,EAAE4J,OAAO,IAAI5J,EAAE+E,SAAS,SAAS/E,GAAGA,EAAEiF,UAAUzE,EAAEmE,kBAAkB7D,EAAES,MAAMsB,KAAKqC,IAAIlF,EAAEiF,UAAUhD,IAAI,GAAGnB,EAAE4B,QAAQ,CAAC1C,GAAGE,IAAI,GAAI,EAACyB,EAAEsB,EAAE,2BAA2B7B,GAAG,GAAGO,EAAE,CAACzB,EAAE2D,EAAE7D,EAAEc,EAAE+H,EAAG5I,EAAEmF,kBAAkB,IAAIxD,EAAEsC,GAAG,WAAW4E,EAAGhI,EAAEO,MAAMD,EAAEO,EAAE0F,eAAe1F,EAAEqD,aAAa8D,EAAGhI,EAAEO,KAAI,EAAGnB,GAAE,GAAI,IAAI,CAAC,UAAU,SAAS6E,SAAS,SAAS/E,GAAG8B,iBAAiB9B,GAAG,WAAW,OAAO8G,EAAElF,EAAE,GAAG,CAACiI,MAAK,EAAGC,SAAQ,GAAM,IAAE9F,EAAEpC,GAAGC,GAAG,SAASrB,GAAGM,EAAEqB,EAAE,OAAOjC,EAAE2D,EAAE7D,EAAEc,EAAE+H,EAAG5I,EAAEmF,kBAAkBtB,GAAG,WAAWhD,EAAES,MAAMnB,YAAYG,MAAMC,EAAEwB,UAAU8G,EAAGhI,EAAEO,KAAI,EAAGnB,GAAE,EAAG,GAAG,GAAG,CAAC,GAAI,CAA1nB,EAA4nB,SAASD,GAAG,IAAIO,EAAE,SAASR,GAAG,IAAIC,EAAE,CAAC8J,gBAAgB,EAAEC,kBAAkB,EAAEC,qBAAqB,EAAEC,mBAAmBlK,EAAEuB,OAAO,GAAGvB,EAAE0C,QAAQjB,OAAO,CAAC,IAAIjB,EAAEN,IAAI,GAAGM,EAAE,CAAC,IAAIM,EAAEN,EAAE0B,iBAAiB,EAAEP,EAAE3B,EAAE0C,QAAQ1C,EAAE0C,QAAQjB,OAAO,GAAGG,EAAED,EAAEwI,KAAK/J,YAAYC,iBAAiB,YAAY+H,QAAQ,SAASpI,GAAG,OAAOA,EAAEuC,OAAOZ,EAAEwI,GAAK,IAAE,GAAGtI,EAAEgB,KAAKqC,IAAI,EAAE1E,EAAEF,cAAcQ,GAAGmB,EAAEY,KAAKqC,IAAIrD,EAAED,GAAGA,EAAEwI,cAAcxI,EAAEqD,WAAWnE,EAAE,GAAGqB,EAAEU,KAAKqC,IAAIjD,EAAEL,EAAEA,EAAEyI,YAAYvJ,EAAE,GAAGmC,EAAEJ,KAAKqC,IAAI/C,EAAER,EAAEsD,UAAUnE,GAAGb,EAAE,CAACqK,QAAQlJ,EAAEO,EAAE2I,SAASP,gBAAgBlI,EAAEmI,kBAAkB/H,EAAEJ,EAAEoI,qBAAqB9H,EAAEF,EAAEiI,mBAAmBjH,EAAEd,EAAEoI,gBAAgB/J,EAAEgK,SAAS7I,GAAGA,EAAEwI,MAAMlK,EAAEkK,IAAIxI,EAAEwI,KAAKvI,IAAI3B,EAAEwK,iBAAiB7I,EAAE,CAAC,CAAC,OAAO8B,OAAOC,OAAO3D,EAAE,CAAC0K,YAAYzK,GAAI,CAApqB,CAAqqBA,GAAGD,EAAEQ,EAAG,GAAEP,EAAG,QAAx7M,SAASD,EAAEC,IAAI,SAASD,EAAEC,GAAGA,EAAEA,GAAG,CAAA,EAAG6E,EAAEZ,GAAG,WAAW,IAAIhE,EAAEM,EAAE2B,EAAE,MAAM,GAAGrB,EAAE,EAAEM,EAAE,GAAGO,EAAE,SAAS3B,GAAGA,EAAE+E,SAAS,SAAS/E,GAAG,IAAIA,EAAE2K,eAAe,CAAC,IAAI1K,EAAEmB,EAAE,GAAGlB,EAAEkB,EAAEA,EAAEK,OAAO,GAAGX,GAAGd,EAAEiF,UAAU/E,EAAE+E,UAAU,KAAKjF,EAAEiF,UAAUhF,EAAEgF,UAAU,KAAKnE,GAAGd,EAAEuB,MAAMH,EAAE+D,KAAKnF,KAAKc,EAAEd,EAAEuB,MAAMH,EAAE,CAACpB,GAAG,CAAG,IAAEc,EAAEN,EAAEe,QAAQf,EAAEe,MAAMT,EAAEN,EAAEkC,QAAQtB,EAAElB,IAAK,EAAC0B,EAAEqB,EAAE,eAAetB,GAAGC,IAAI1B,EAAE2D,EAAE7D,EAAEQ,EAAE6E,EAAEpF,EAAEmF,kBAAkBpB,GAAG,WAAWrC,EAAEC,EAAEyF,eAAenH,GAAE,EAAG,IAAI2B,GAAG,WAAWf,EAAE,EAAEN,EAAE2B,EAAE,MAAM,GAAGjC,EAAE2D,EAAE7D,EAAEQ,EAAE6E,EAAEpF,EAAEmF,kBAAkBtB,GAAG,WAAW,OAAO5D,GAAG,GAAK,IAAEwE,WAAWxE,EAAE,GAAG,IAAK,CAA5f,EAA8f,SAASD,GAAG,IAAIC,EAAE,SAASF,GAAG,IAAIC,EAAEC,EAAE,CAAE,EAAC,GAAGF,EAAE0C,QAAQjB,OAAO,CAAC,IAAIX,EAAEd,EAAE0C,QAAQkI,QAAQ,SAAS5K,EAAEC,GAAG,OAAOD,GAAGA,EAAEuB,MAAMtB,EAAEsB,MAAMvB,EAAEC,CAAC,IAAI,GAAGa,GAAGA,EAAE+J,SAAS/J,EAAE+J,QAAQpJ,OAAO,CAAC,IAAIE,GAAG1B,EAAEa,EAAE+J,SAASC,MAAM,SAAS9K,GAAG,OAAOA,EAAE+K,MAAM,IAAI/K,EAAE+K,KAAK/J,QAAQ,KAAKf,EAAE,GAAG0B,IAAIzB,EAAE,CAAC8K,mBAAmB5J,EAAEO,EAAEoJ,MAAME,iBAAiBnK,EAAEmE,UAAUiG,kBAAkBpK,EAAES,MAAM4J,mBAAmBxJ,EAAEyJ,kBAAkBtK,EAAEuK,UAAU7K,EAAEM,EAAEmE,YAAY,CAAC,CAAC,OAAOvB,OAAOC,OAAO3D,EAAE,CAAC0K,YAAYxK,GAAI,CAAhb,CAAibD,GAAGD,EAAEE,EAAG,GAAED,EAAG,QAAG,SAASD,EAAEC,GAAG6E,GAAG,SAAS7E,GAAG,IAAIa,EAAE,SAASd,GAAG,IAAIC,EAAE,CAAC8J,gBAAgB,EAAEuB,eAAetL,EAAEuB,MAAM8J,UAAU7K,EAAEoB,MAAM,GAAG5B,EAAE0C,QAAQjB,OAAO,CAAC,IAAIX,EAAEZ,IAAIkB,EAAEpB,EAAE0C,QAAQ1C,EAAE0C,QAAQjB,OAAO,GAAG,GAAGX,EAAE,CAAC,IAAIa,EAAEb,EAAEoB,iBAAiB,EAAEL,EAAEgB,KAAKqC,IAAI,EAAEpE,EAAER,cAAcqB,GAAG1B,EAAE,CAAC8J,gBAAgBlI,EAAEyJ,eAAetL,EAAEuB,MAAMM,EAAEwJ,UAAU7K,EAAER,EAAE0C,QAAQ,GAAGuC,WAAWsF,gBAAgBzJ,EAAEyK,SAASnK,EAAE,CAAC,CAAC,OAAOsC,OAAOC,OAAO3D,EAAE,CAAC0K,YAAYzK,GAAI,CAAvW,CAAwWA,GAAGD,EAAEc,EAAG,GAAEb,EAAG,EEOzlJuL,MFP4/O,SAASxL,EAAEE,GAAGD,IAAIA,EAAEgD,EAAE,uBAAuB6E,IAAIb,GAAG,SAAShH,GAAG,IAAIC,EAAE,SAASF,GAAG,IAAIC,EAAED,EAAE0C,QAAQ,GAAGxC,EAAEwH,EAAEnB,IAAItG,GAAGa,EAAEb,EAAE2I,gBAAgBjH,EAAEzB,EAAEqI,cAAc3G,EAAE1B,EAAEwC,QAAQiE,MAAM,SAAS3G,EAAEC,GAAG,OAAOD,EAAE4I,gBAAgB3I,EAAE2I,eAAe,IAAI/G,EAAEyG,EAAGrI,EAAEgF,UAAUtD,GAAGM,EAAEjC,EAAE0C,QAAQoI,MAAM,SAAS9K,GAAG,OAAOA,EAAEyI,MAAM,IAAItG,EAAEF,GAAGA,EAAEwG,QAAQb,EAAErB,IAAItG,EAAEyF,eAAezC,EAAE,CAAChD,EAAEgF,UAAUhF,EAAEuG,SAAS7E,GAAGgB,OAAOd,EAAEsG,KAAK,SAASnI,GAAG,OAAOA,EAAEiF,UAAUjF,EAAEwG,QAAQ,KAAK3C,EAAEhB,KAAKqC,IAAIuG,MAAM5I,KAAKI,GAAGa,EAAE,CAAC4H,kBAAkBtK,EAAEe,GAAGwJ,yBAAyBxJ,EAAEyJ,gBAAgB3L,EAAEsC,KAAKsJ,WAAW,OAAO,WAAW,UAAUC,gBAAgB7L,EAAEgF,UAAU8G,cAAclI,EAAEmI,sBAAsBpK,EAAEqK,0BAA0BpK,EAAEqK,WAAWpL,EAAEb,EAAEgF,UAAUkH,mBAAmBxK,EAAEb,EAAEsL,kBAAkBvJ,KAAKqC,IAAIrB,EAAElC,EAAE,GAAG0J,UAAU7K,EAAEP,EAAEgF,YAAY,OAAOvB,OAAOC,OAAO3D,EAAE,CAAC0K,YAAY5G,GAAI,CAAluB,CAAmuB7D,GAAGD,EAAEE,EAAG,GAAEA,EAAG,GEUtzQwJ,GAAiB2C,sBAAwB3C,GAAiB2C,uBAAyB,CAAE,EACrF3C,GAAiB2C,sBAAsB1C,0BAA4BA,GAOnED,GAAiBC,0BAA4BA", "x_google_ignoreList": [0]}