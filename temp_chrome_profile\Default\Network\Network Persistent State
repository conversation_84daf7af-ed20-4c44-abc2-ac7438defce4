{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750254914", "host": "app.augmentcode.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 2, "broken_until": "1750254915", "host": "auth.augmentcode.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750254916", "host": "login.augmentcode.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 7, "broken_until": "1750254917", "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750254917", "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750254917", "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "broken_count": 1, "broken_until": "1750254918", "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "broken_count": 5, "broken_until": "1750254918", "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750254922", "host": "analytics.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750254929", "host": "googleads.g.doubleclick.net", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "broken_count": 1, "broken_until": "1750254929", "host": "td.doubleclick.net", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://augment-assets.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://consent.cookiebot.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://consentcdn.cookiebot.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://consentcdn.cookiebot.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://cdn.vector.co", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://api.vector.co", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://a.usbrowserspeed.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://d-code.liadm.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://idx.liadm.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320210557160", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://app.augmentcode.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://cdn.verosint.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320212256574", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://auth.augmentcode.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394814612905589", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://login.augmentcode.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://cdn.auth0.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320213281923", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://storage.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320213307248", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320213733854", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320213891975", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://stats.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394814613896324", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320214256778", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"anonymization": ["KAAAACIAAABodHRwczovL3ByaXZhY3lzYW5kYm94c2VydmljZXMuY29tAAA=", false, 0], "server": "https://publickeyservice.pa.aws.privacysandboxservices.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://evs.grdt.augmentcode.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320214822382", "port": 443, "protocol_str": "quic"}], "anonymization": ["KAAAACIAAABodHRwczovL3ByaXZhY3lzYW5kYm94c2VydmljZXMuY29tAAA=", false, 0], "server": "https://publickeyservice.pa.gcp.privacysandboxservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320215053382", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://us-assets.i.posthog.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://www.augmentcode.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394814618751645", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320225299445", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://td.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320225314697", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://googleads.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320225470767", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320225496427", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320225503920", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397320230303244", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://analytics.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://us.i.posthog.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}