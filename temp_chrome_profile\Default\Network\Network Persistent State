{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750247798", "host": "app.augmentcode.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750247799", "host": "auth.augmentcode.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750247801", "host": "login.augmentcode.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 2, "broken_until": "1750247802", "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750247802", "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750247802", "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "broken_count": 1, "broken_until": "1750247802", "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "broken_count": 1, "broken_until": "1750247803", "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750247807", "host": "analytics.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://consent.cookiebot.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://augment-assets.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://consentcdn.cookiebot.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://consentcdn.cookiebot.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://cdn.vector.co", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://api.vector.co", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://a.usbrowserspeed.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313098848956", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://app.augmentcode.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://d-code.liadm.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://idx.liadm.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://cdn.verosint.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313100783818", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://auth.augmentcode.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394807501500554", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://login.augmentcode.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://cdn.auth0.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313101981819", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://storage.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313102021051", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313102538975", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394807502699007", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313102724364", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://googleads.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313102963512", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313103023304", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313103744962", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://evs.grdt.augmentcode.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://www.augmentcode.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://us-assets.i.posthog.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397313107755500", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://analytics.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394807507831517", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://us.i.posthog.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAISABiAgICA+P////8B": "3G"}}}