{"net": {"http_server_properties": {"broken_alternative_services": [{"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750256010", "host": "app.augmentcode.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 2, "broken_until": "1750256011", "host": "auth.augmentcode.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750256013", "host": "login.augmentcode.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "broken_count": 7, "broken_until": "1750256014", "host": "optimizationguide-pa.googleapis.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750256014", "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "broken_count": 1, "broken_until": "1750256014", "host": "www.googletagmanager.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750256015", "host": "www.google.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "broken_count": 6, "broken_until": "1750256015", "host": "challenges.cloudflare.com", "port": 443, "protocol_str": "quic"}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "broken_count": 1, "broken_until": "1750256019", "host": "analytics.google.com", "port": 443, "protocol_str": "quic"}], "servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "server": "https://accounts.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://augment-assets.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://consent.cookiebot.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://consentcdn.cookiebot.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://consentcdn.cookiebot.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://cdn.vector.co", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://api.vector.co", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://a.usbrowserspeed.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://d-code.liadm.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321307113778", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://app.augmentcode.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://idx.liadm.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://cdn.verosint.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321308946905", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://auth.augmentcode.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394815709664322", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://login.augmentcode.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://cdn.auth0.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321310077052", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://storage.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321310112242", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321310579500", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394815710697713", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321310876195", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://googleads.g.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321310886714", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://td.doubleclick.net", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321311103041", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://www.googletagmanager.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321311199060", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://stats.g.doubleclick.net", "supports_spdy": true}, {"anonymization": ["KAAAACIAAABodHRwczovL3ByaXZhY3lzYW5kYm94c2VydmljZXMuY29tAAA=", false, 0], "server": "https://publickeyservice.pa.aws.privacysandboxservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321311299670", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://www.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://evs.grdt.augmentcode.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321311854124", "port": 443, "protocol_str": "quic"}], "anonymization": ["KAAAACIAAABodHRwczovL3ByaXZhY3lzYW5kYm94c2VydmljZXMuY29tAAA=", false, 0], "server": "https://publickeyservice.pa.gcp.privacysandboxservices.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321312371831", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACsAAABodHRwczovL29wdGltaXphdGlvbmd1aWRlLXBhLmdvb2dsZWFwaXMuY29tAA==", false, 0], "server": "https://optimizationguide-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://us-assets.i.posthog.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://www.augmentcode.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13394815715390742", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", true, 0], "server": "https://challenges.cloudflare.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13397321315681708", "port": 443, "protocol_str": "quic"}], "anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://analytics.google.com", "supports_spdy": true}, {"anonymization": ["HAAAABcAAABodHRwczovL2F1Z21lbnRjb2RlLmNvbQA=", false, 0], "server": "https://us.i.posthog.com", "supports_spdy": true}], "version": 5}, "network_qualities": {"CAASABiAgICA+P////8B": "4G"}}}