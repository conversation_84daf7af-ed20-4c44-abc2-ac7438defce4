{"version": 3, "file": "dead-clicks-autocapture.js", "sources": ["../src/utils/globals.ts", "../src/utils/string-utils.ts", "../src/utils/type-utils.ts", "../src/utils/logger.ts", "../src/utils/index.ts", "../src/utils/element-utils.ts", "../src/autocapture-utils.ts", "../src/autocapture.ts", "../src/utils/request-utils.ts", "../src/utils/prototype-utils.ts", "../src/entrypoints/dead-clicks-autocapture.ts", "../src/constants.ts"], "sourcesContent": ["import { ErrorProperties } from '../extensions/exception-autocapture/error-conversion'\nimport type { PostHog } from '../posthog-core'\nimport { SessionIdManager } from '../sessionid'\nimport { DeadClicksAutoCaptureConfig, RemoteConfig, SiteAppLoader } from '../types'\n\n/*\n * Global helpers to protect access to browser globals in a way that is safer for different targets\n * like DOM, SSR, Web workers etc.\n *\n * NOTE: Typically we want the \"window\" but globalThis works for both the typical browser context as\n * well as other contexts such as the web worker context. Window is still exported for any bits that explicitly require it.\n * If in doubt - export the global you need from this file and use that as an optional value. This way the code path is forced\n * to handle the case where the global is not available.\n */\n\n// eslint-disable-next-line no-restricted-globals\nconst win: (Window & typeof globalThis) | undefined = typeof window !== 'undefined' ? window : undefined\n\nexport type AssignableWindow = Window &\n    typeof globalThis & {\n        __PosthogExtensions__?: PostHogExtensions\n\n        _POSTHOG_REMOTE_CONFIG?: Record<\n            string,\n            {\n                config: RemoteConfig\n                siteApps: SiteAppLoader[]\n            }\n        >\n\n        doNotTrack: any\n        posthogCustomizations: any\n        posthogErrorWrappingFunctions: any\n        rrweb: any\n        rrwebConsoleRecord: any\n        getRecordNetworkPlugin: any\n        POSTHOG_DEBUG: any\n        posthog: any\n        ph_load_toolbar: any\n        ph_load_editor: any\n        ph_toolbar_state: any\n        postHogWebVitalsCallbacks: any\n        postHogTracingHeadersPatchFns: any\n        extendPostHogWithSurveys: any\n    } & Record<`__$$ph_site_app_${string}`, any>\n\n/**\n * This is our contract between (potentially) lazily loaded extensions and the SDK\n * changes to this interface can be breaking changes for users of the SDK\n */\n\nexport type PostHogExtensionKind =\n    | 'toolbar'\n    | 'exception-autocapture'\n    | 'web-vitals'\n    | 'recorder'\n    | 'tracing-headers'\n    | 'surveys'\n    | 'dead-clicks-autocapture'\n    | 'remote-config'\n\nexport interface LazyLoadedDeadClicksAutocaptureInterface {\n    start: (observerTarget: Node) => void\n    stop: () => void\n}\n\ninterface PostHogExtensions {\n    loadExternalDependency?: (\n        posthog: PostHog,\n        kind: PostHogExtensionKind,\n        callback: (error?: string | Event, event?: Event) => void\n    ) => void\n\n    loadSiteApp?: (posthog: PostHog, appUrl: string, callback: (error?: string | Event, event?: Event) => void) => void\n\n    errorWrappingFunctions?: {\n        wrapOnError: (captureFn: (props: ErrorProperties) => void) => () => void\n        wrapUnhandledRejection: (captureFn: (props: ErrorProperties) => void) => () => void\n        wrapConsoleError: (captureFn: (props: ErrorProperties) => void) => () => void\n    }\n    rrweb?: { record: any; version: string }\n    rrwebPlugins?: { getRecordConsolePlugin: any; getRecordNetworkPlugin?: any }\n    generateSurveys?: (posthog: PostHog) => any | undefined\n    postHogWebVitalsCallbacks?: {\n        onLCP: (metric: any) => void\n        onCLS: (metric: any) => void\n        onFCP: (metric: any) => void\n        onINP: (metric: any) => void\n    }\n    tracingHeadersPatchFns?: {\n        _patchFetch: (sessionManager?: SessionIdManager) => () => void\n        _patchXHR: (sessionManager?: SessionIdManager) => () => void\n    }\n    initDeadClicksAutocapture?: (\n        ph: PostHog,\n        config: DeadClicksAutoCaptureConfig\n    ) => LazyLoadedDeadClicksAutocaptureInterface\n}\n\nconst global: typeof globalThis | undefined = typeof globalThis !== 'undefined' ? globalThis : win\n\nexport const ArrayProto = Array.prototype\nexport const nativeForEach = ArrayProto.forEach\nexport const nativeIndexOf = ArrayProto.indexOf\n\nexport const navigator = global?.navigator\nexport const document = global?.document\nexport const location = global?.location\nexport const fetch = global?.fetch\nexport const XMLHttpRequest =\n    global?.XMLHttpRequest && 'withCredentials' in new global.XMLHttpRequest() ? global.XMLHttpRequest : undefined\nexport const AbortController = global?.AbortController\nexport const userAgent = navigator?.userAgent\nexport const assignableWindow: AssignableWindow = win ?? ({} as any)\n\nexport { win as window }\n", "export function includes(str: string, needle: string): boolean\nexport function includes<T>(arr: T[], needle: T): boolean\nexport function includes(str: unknown[] | string, needle: unknown): boolean {\n    return (str as any).indexOf(needle) !== -1\n}\n\nexport const trim = function (str: string): string {\n    // Previous implementation was using underscore's trim function.\n    // When switching to just using the native trim() function, we ran some tests to make sure that it was able to trim both the BOM character \\uFEFF and the NBSP character \\u00A0.\n    // We tested modern Chrome (134.0.6998.118) and Firefox (136.0.2), and IE11 running on Windows 10, and all of them were able to trim both characters.\n    // See https://posthog.slack.com/archives/C0113360FFV/p1742811455647359\n    return str.trim()\n}\n\n// UNDERSCORE\n// Embed part of the Underscore Library\nexport const stripLeadingDollar = function (s: string): string {\n    return s.replace(/^\\$/, '')\n}\n\nexport function isDistinctIdStringLike(value: string): boolean {\n    return ['distinct_id', 'distinctid'].includes(value.toLowerCase())\n}\n", "import { window } from './globals'\nimport { knownUnsafeEditableEvent, KnownUnsafeEditableEvent } from '../types'\nimport { includes } from './string-utils'\n\n// eslint-disable-next-line posthog-js/no-direct-array-check\nconst nativeIsArray = Array.isArray\nconst ObjProto = Object.prototype\nexport const hasOwnProperty = ObjProto.hasOwnProperty\nconst toString = ObjProto.toString\n\nexport const isArray =\n    nativeIsArray ||\n    function (obj: any): obj is any[] {\n        return toString.call(obj) === '[object Array]'\n    }\n\n// from a comment on http://dbj.org/dbj/?p=286\n// fails on only one very rare and deliberate custom object:\n// let bomb = { toString : undefined, valueOf: function(o) { return \"function BOMBA!\"; }};\nexport const isFunction = (x: unknown): x is (...args: any[]) => any => {\n    // eslint-disable-next-line posthog-js/no-direct-function-check\n    return typeof x === 'function'\n}\n\nexport const isNativeFunction = (x: unknown): x is (...args: any[]) => any =>\n    isFunction(x) && x.toString().indexOf('[native code]') !== -1\n\n// When angular patches functions they pass the above `isNativeFunction` check (at least the MutationObserver)\nexport const isAngularZonePresent = (): boolean => {\n    return !!(window as any).Zone\n}\n\n// Underscore Addons\nexport const isObject = (x: unknown): x is Record<string, any> => {\n    // eslint-disable-next-line posthog-js/no-direct-object-check\n    return x === Object(x) && !isArray(x)\n}\nexport const isEmptyObject = (x: unknown) => {\n    if (isObject(x)) {\n        for (const key in x) {\n            if (hasOwnProperty.call(x, key)) {\n                return false\n            }\n        }\n        return true\n    }\n    return false\n}\nexport const isUndefined = (x: unknown): x is undefined => x === void 0\n\nexport const isString = (x: unknown): x is string => {\n    // eslint-disable-next-line posthog-js/no-direct-string-check\n    return toString.call(x) == '[object String]'\n}\n\nexport const isEmptyString = (x: unknown): boolean => isString(x) && x.trim().length === 0\n\nexport const isNull = (x: unknown): x is null => {\n    // eslint-disable-next-line posthog-js/no-direct-null-check\n    return x === null\n}\n\n/*\n    sometimes you want to check if something is null or undefined\n    that's what this is for\n */\nexport const isNullish = (x: unknown): x is null | undefined => isUndefined(x) || isNull(x)\n\nexport const isNumber = (x: unknown): x is number => {\n    // eslint-disable-next-line posthog-js/no-direct-number-check\n    return toString.call(x) == '[object Number]'\n}\nexport const isBoolean = (x: unknown): x is boolean => {\n    // eslint-disable-next-line posthog-js/no-direct-boolean-check\n    return toString.call(x) === '[object Boolean]'\n}\n\nexport const isDocument = (x: unknown): x is Document => {\n    // eslint-disable-next-line posthog-js/no-direct-document-check\n    return x instanceof Document\n}\n\nexport const isFormData = (x: unknown): x is FormData => {\n    // eslint-disable-next-line posthog-js/no-direct-form-data-check\n    return x instanceof FormData\n}\n\nexport const isFile = (x: unknown): x is File => {\n    // eslint-disable-next-line posthog-js/no-direct-file-check\n    return x instanceof File\n}\n\nexport const isError = (x: unknown): x is Error => {\n    return x instanceof Error\n}\n\nexport const isKnownUnsafeEditableEvent = (x: unknown): x is KnownUnsafeEditableEvent => {\n    return includes(knownUnsafeEditableEvent as unknown as string[], x)\n}\n", "import Config from '../config'\nimport { isUndefined } from './type-utils'\nimport { assignableWindow, window } from './globals'\n\nexport type Logger = {\n    _log: (level: 'log' | 'warn' | 'error', ...args: any[]) => void\n    info: (...args: any[]) => void\n    warn: (...args: any[]) => void\n    error: (...args: any[]) => void\n    critical: (...args: any[]) => void\n    uninitializedWarning: (methodName: string) => void\n    createLogger: (prefix: string) => Logger\n}\n\nconst _createLogger = (prefix: string): Logger => {\n    const logger: Logger = {\n        _log: (level: 'log' | 'warn' | 'error', ...args: any[]) => {\n            if (\n                window &&\n                (Config.DEBUG || assignableWindow.POSTHOG_DEBUG) &&\n                !isUndefined(window.console) &&\n                window.console\n            ) {\n                const consoleLog =\n                    '__rrweb_original__' in window.console[level]\n                        ? (window.console[level] as any)['__rrweb_original__']\n                        : window.console[level]\n\n                // eslint-disable-next-line no-console\n                consoleLog(prefix, ...args)\n            }\n        },\n\n        info: (...args: any[]) => {\n            logger._log('log', ...args)\n        },\n\n        warn: (...args: any[]) => {\n            logger._log('warn', ...args)\n        },\n\n        error: (...args: any[]) => {\n            logger._log('error', ...args)\n        },\n\n        critical: (...args: any[]) => {\n            // Critical errors are always logged to the console\n            // eslint-disable-next-line no-console\n            console.error(prefix, ...args)\n        },\n\n        uninitializedWarning: (methodName: string) => {\n            logger.error(`You must initialize PostHog before calling ${methodName}`)\n        },\n\n        createLogger: (additionalPrefix: string) => _createLogger(`${prefix} ${additionalPrefix}`),\n    }\n    return logger\n}\n\nexport const logger = _createLogger('[PostHog.js]')\n\nexport const createLogger = logger.createLogger\n", "import { Breaker, Properties } from '../types'\nimport { nativeForEach, nativeIndexOf } from './globals'\nimport { logger } from './logger'\nimport { hasOwnProperty, isArray, isFormData, isNull, isNullish, isNumber, isString, isUndefined } from './type-utils'\n\nconst breaker: Breaker = {}\n\nexport function eachArray<E = any>(\n    obj: E[] | null | undefined,\n    iterator: (value: E, key: number) => void | Breaker,\n    thisArg?: any\n): void {\n    if (isArray(obj)) {\n        if (nativeForEach && obj.forEach === nativeForEach) {\n            obj.forEach(iterator, thisArg)\n        } else if ('length' in obj && obj.length === +obj.length) {\n            for (let i = 0, l = obj.length; i < l; i++) {\n                if (i in obj && iterator.call(thisArg, obj[i], i) === breaker) {\n                    return\n                }\n            }\n        }\n    }\n}\n\n/**\n * @param {*=} obj\n * @param {function(...*)=} iterator\n * @param {Object=} thisArg\n */\nexport function each(obj: any, iterator: (value: any, key: any) => void | Breaker, thisArg?: any): void {\n    if (isNullish(obj)) {\n        return\n    }\n    if (isArray(obj)) {\n        return eachArray(obj, iterator, thisArg)\n    }\n    if (isFormData(obj)) {\n        for (const pair of obj.entries()) {\n            if (iterator.call(thisArg, pair[1], pair[0]) === breaker) {\n                return\n            }\n        }\n        return\n    }\n    for (const key in obj) {\n        if (hasOwnProperty.call(obj, key)) {\n            if (iterator.call(thisArg, obj[key], key) === breaker) {\n                return\n            }\n        }\n    }\n}\n\nexport const extend = function (obj: Record<string, any>, ...args: Record<string, any>[]): Record<string, any> {\n    eachArray(args, function (source) {\n        for (const prop in source) {\n            if (source[prop] !== void 0) {\n                obj[prop] = source[prop]\n            }\n        }\n    })\n    return obj\n}\n\nexport const extendArray = function <T>(obj: T[], ...args: T[][]): T[] {\n    eachArray(args, function (source) {\n        eachArray(source, function (item) {\n            obj.push(item)\n        })\n    })\n    return obj\n}\n\nexport const include = function (\n    obj: null | string | Array<any> | Record<string, any>,\n    target: any\n): boolean | Breaker {\n    let found = false\n    if (isNull(obj)) {\n        return found\n    }\n    if (nativeIndexOf && obj.indexOf === nativeIndexOf) {\n        return obj.indexOf(target) != -1\n    }\n    each(obj, function (value) {\n        if (found || (found = value === target)) {\n            return breaker\n        }\n        return\n    })\n    return found\n}\n\n/**\n * Object.entries() polyfill\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/entries\n */\nexport function entries<T = any>(obj: Record<string, T>): [string, T][] {\n    const ownProps = Object.keys(obj)\n    let i = ownProps.length\n    const resArray = new Array(i) // preallocate the Array\n\n    while (i--) {\n        resArray[i] = [ownProps[i], obj[ownProps[i]]]\n    }\n    return resArray\n}\n\nexport const trySafe = function <T>(fn: () => T): T | undefined {\n    try {\n        return fn()\n    } catch {\n        return undefined\n    }\n}\n\nexport const safewrap = function <F extends (...args: any[]) => any = (...args: any[]) => any>(f: F): F {\n    return function (...args) {\n        try {\n            // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n            // @ts-ignore\n            return f.apply(this, args)\n        } catch (e) {\n            logger.critical(\n                'Implementation error. Please turn on debug mode and open a ticket on https://app.posthog.com/home#panel=support%3Asupport%3A.'\n            )\n            logger.critical(e)\n        }\n    } as F\n}\n\n// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport const safewrapClass = function (klass: Function, functions: string[]): void {\n    for (let i = 0; i < functions.length; i++) {\n        klass.prototype[functions[i]] = safewrap(klass.prototype[functions[i]])\n    }\n}\n\nexport const stripEmptyProperties = function (p: Properties): Properties {\n    const ret: Properties = {}\n    each(p, function (v, k) {\n        if ((isString(v) && v.length > 0) || isNumber(v)) {\n            ret[k] = v\n        }\n    })\n    return ret\n}\n\n/**\n * Deep copies an object.\n * It handles cycles by replacing all references to them with `undefined`\n * Also supports customizing native values\n *\n * @param value\n * @param customizer\n * @returns {{}|undefined|*}\n */\nfunction deepCircularCopy<T extends Record<string, any> = Record<string, any>>(\n    value: T,\n    customizer?: <K extends keyof T = keyof T>(value: T[K], key?: K) => T[K]\n): T | undefined {\n    const COPY_IN_PROGRESS_SET = new Set()\n\n    function internalDeepCircularCopy(value: T, key?: string): T | undefined {\n        if (value !== Object(value)) return customizer ? customizer(value as any, key) : value // primitive value\n\n        if (COPY_IN_PROGRESS_SET.has(value)) return undefined\n        COPY_IN_PROGRESS_SET.add(value)\n        let result: T\n\n        if (isArray(value)) {\n            result = [] as any as T\n            eachArray(value, (it) => {\n                result.push(internalDeepCircularCopy(it))\n            })\n        } else {\n            result = {} as T\n            each(value, (val, key) => {\n                if (!COPY_IN_PROGRESS_SET.has(val)) {\n                    ;(result as any)[key] = internalDeepCircularCopy(val, key)\n                }\n            })\n        }\n        return result\n    }\n    return internalDeepCircularCopy(value)\n}\n\nexport function _copyAndTruncateStrings<T extends Record<string, any> = Record<string, any>>(\n    object: T,\n    maxStringLength: number | null\n): T {\n    return deepCircularCopy(object, (value: any) => {\n        if (isString(value) && !isNull(maxStringLength)) {\n            return (value as string).slice(0, maxStringLength)\n        }\n        return value\n    }) as T\n}\n\n// NOTE: Update PostHogConfig docs if you change this list\n// We will not try to catch all bullets here, but we should make an effort to catch the most common ones\n// You should be highly against adding more to this list, because ultimately customers can configure\n// their `cross_subdomain_cookie` setting to anything they want.\nconst EXCLUDED_FROM_CROSS_SUBDOMAIN_COOKIE = ['herokuapp.com', 'vercel.app', 'netlify.app']\nexport function isCrossDomainCookie(documentLocation: Location | undefined) {\n    const hostname = documentLocation?.hostname\n\n    if (!isString(hostname)) {\n        return false\n    }\n    // split and slice isn't a great way to match arbitrary domains,\n    // but it's good enough for ensuring we only match herokuapp.com when it is the TLD\n    // for the hostname\n    const lastTwoParts = hostname.split('.').slice(-2).join('.')\n\n    for (const excluded of EXCLUDED_FROM_CROSS_SUBDOMAIN_COOKIE) {\n        if (lastTwoParts === excluded) {\n            return false\n        }\n    }\n\n    return true\n}\n\nexport function find<T>(value: T[], predicate: (value: T) => boolean): T | undefined {\n    for (let i = 0; i < value.length; i++) {\n        if (predicate(value[i])) {\n            return value[i]\n        }\n    }\n    return undefined\n}\n\n// Use this instead of element.addEventListener to avoid eslint errors\n// this properly implements the default options for passive event listeners\nexport function addEventListener(\n    element: Window | Document | Element | undefined,\n    event: string,\n    callback: EventListener,\n    options?: AddEventListenerOptions\n): void {\n    const { capture = false, passive = true } = options ?? {}\n\n    // This is the only place where we are allowed to call this function\n    // because the whole idea is that we should be calling this instead of the built-in one\n    // eslint-disable-next-line posthog-js/no-add-event-listener\n    element?.addEventListener(event, callback, { capture, passive })\n}\n\n/**\n * Helper to migrate deprecated config fields to new field names with appropriate warnings\n * @param config - The config object to check\n * @param newField - The new field name to use\n * @param oldField - The deprecated field name to check for\n * @param defaultValue - The default value if neither field is set\n * @param loggerInstance - Optional logger instance for deprecation warnings\n * @returns The value to use (new field takes precedence over old field)\n */\nexport function migrateConfigField<T>(\n    config: Record<string, any>,\n    newField: string,\n    oldField: string,\n    defaultValue: T,\n    loggerInstance?: { warn: (message: string) => void }\n): T {\n    const hasNewField = newField in config && !isUndefined(config[newField])\n    const hasOldField = oldField in config && !isUndefined(config[oldField])\n\n    if (hasNewField) {\n        return config[newField]\n    }\n\n    if (hasOldField) {\n        if (loggerInstance) {\n            loggerInstance.warn(\n                `Config field '${oldField}' is deprecated. Please use '${newField}' instead. ` +\n                    `The old field will be removed in a future major version.`\n            )\n        }\n        return config[oldField]\n    }\n\n    return defaultValue\n}\n", "import { TOOLBAR_CONTAINER_CLASS, TOOLBAR_ID } from '../constants'\n\nexport function isElementInToolbar(el: EventTarget | null): boolean {\n    if (el instanceof Element) {\n        // closest isn't available in IE11, but we'll polyfill when bundling\n        return el.id === TOOLBAR_ID || !!el.closest?.('.' + TOOLBAR_CONTAINER_CLASS)\n    }\n    return false\n}\n\n/*\n * Check whether an element has nodeType Node.ELEMENT_NODE\n * @param {Element} el - element to check\n * @returns {boolean} whether el is of the correct nodeType\n */\nexport function isElementNode(el: Node | Element | undefined | null): el is Element {\n    return !!el && el.nodeType === 1 // Node.ELEMENT_NODE - use integer constant for browser portability\n}\n\n/*\n * Check whether an element is of a given tag type.\n * Due to potential reference discrepancies (such as the webcomponents.js polyfill),\n * we want to match tagNames instead of specific references because something like\n * element === document.body won't always work because element might not be a native\n * element.\n * @param {Element} el - element to check\n * @param {string} tag - tag name (e.g., \"div\")\n * @returns {boolean} whether el is of the given tag type\n */\nexport function isTag(el: Element | undefined | null, tag: string): el is HTMLElement {\n    return !!el && !!el.tagName && el.tagName.toLowerCase() === tag.toLowerCase()\n}\n\n/*\n * Check whether an element has nodeType Node.TEXT_NODE\n * @param {Element} el - element to check\n * @returns {boolean} whether el is of the correct nodeType\n */\nexport function isTextNode(el: Element | undefined | null): el is HTMLElement {\n    return !!el && el.nodeType === 3 // Node.TEXT_NODE - use integer constant for browser portability\n}\n\n/*\n * Check whether an element has nodeType Node.DOCUMENT_FRAGMENT_NODE\n * @param {Element} el - element to check\n * @returns {boolean} whether el is of the correct nodeType\n */\nexport function isDocumentFragment(el: Element | ParentNode | undefined | null): el is DocumentFragment {\n    return !!el && el.nodeType === 11 // Node.DOCUMENT_FRAGMENT_NODE - use integer constant for browser portability\n}\n", "import { AutocaptureConfig, Properties } from './types'\nimport { each, entries } from './utils'\n\nimport { isArray, isNullish, isString, isUndefined } from './utils/type-utils'\nimport { logger } from './utils/logger'\nimport { window } from './utils/globals'\nimport { isDocumentFragment, isElementNode, isTag, isTextNode } from './utils/element-utils'\nimport { includes, trim } from './utils/string-utils'\n\nexport function splitClassString(s: string): string[] {\n    return s ? trim(s).split(/\\s+/) : []\n}\n\nfunction checkForURLMatches(urlsList: (string | RegExp)[]): boolean {\n    const url = window?.location.href\n    return !!(url && urlsList && urlsList.some((regex) => url.match(regex)))\n}\n\n/*\n * Get the className of an element, accounting for edge cases where element.className is an object\n *\n * Because this is a string it can contain unexpected characters\n * So, this method safely splits the className and returns that array.\n */\nexport function getClassNames(el: Element): string[] {\n    let className = ''\n    switch (typeof el.className) {\n        case 'string':\n            className = el.className\n            break\n        // TODO: when is this ever used?\n        case 'object': // handle cases where className might be SVGAnimatedString or some other type\n            className =\n                (el.className && 'baseVal' in el.className ? (el.className as any).baseVal : null) ||\n                el.getAttribute('class') ||\n                ''\n            break\n        default:\n            className = ''\n    }\n\n    return splitClassString(className)\n}\n\nexport function makeSafeText(s: string | null | undefined): string | null {\n    if (isNullish(s)) {\n        return null\n    }\n\n    return (\n        trim(s)\n            // scrub potentially sensitive values\n            .split(/(\\s+)/)\n            .filter((s) => shouldCaptureValue(s))\n            .join('')\n            // normalize whitespace\n            .replace(/[\\r\\n]/g, ' ')\n            .replace(/[ ]+/g, ' ')\n            // truncate\n            .substring(0, 255)\n    )\n}\n\n/*\n * Get the direct text content of an element, protecting against sensitive data collection.\n * Concats textContent of each of the element's text node children; this avoids potential\n * collection of sensitive data that could happen if we used element.textContent and the\n * element had sensitive child elements, since element.textContent includes child content.\n * Scrubs values that look like they could be sensitive (i.e. cc or ssn number).\n * @param {Element} el - element to get the text of\n * @returns {string} the element's direct text content\n */\nexport function getSafeText(el: Element): string {\n    let elText = ''\n\n    if (shouldCaptureElement(el) && !isSensitiveElement(el) && el.childNodes && el.childNodes.length) {\n        each(el.childNodes, function (child) {\n            if (isTextNode(child) && child.textContent) {\n                elText += makeSafeText(child.textContent) ?? ''\n            }\n        })\n    }\n\n    return trim(elText)\n}\n\nexport function getEventTarget(e: Event): Element | null {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Event/target#Compatibility_notes\n    if (isUndefined(e.target)) {\n        return (e.srcElement as Element) || null\n    } else {\n        if ((e.target as HTMLElement)?.shadowRoot) {\n            return (e.composedPath()[0] as Element) || null\n        }\n        return (e.target as Element) || null\n    }\n}\n\nexport const autocaptureCompatibleElements = ['a', 'button', 'form', 'input', 'select', 'textarea', 'label']\n\n/*\n if there is no config, then all elements are allowed\n if there is a config, and there is an allow list, then only elements in the allow list are allowed\n assumes that some other code is checking this element's parents\n */\nfunction checkIfElementTreePassesElementAllowList(\n    elements: Element[],\n    autocaptureConfig: AutocaptureConfig | undefined\n): boolean {\n    const allowlist = autocaptureConfig?.element_allowlist\n    if (isUndefined(allowlist)) {\n        // everything is allowed, when there is no allow list\n        return true\n    }\n\n    // check each element in the tree\n    // if any of the elements are in the allow list, then the tree is allowed\n    for (const el of elements) {\n        if (allowlist.some((elementType) => el.tagName.toLowerCase() === elementType)) {\n            return true\n        }\n    }\n\n    // otherwise there is an allow list and this element tree didn't match it\n    return false\n}\n\n/*\n if there is no config, then all elements are allowed\n if there is a config, and there is an allow list, then\n only elements that match the css selector in the allow list are allowed\n assumes that some other code is checking this element's parents\n */\nfunction checkIfElementTreePassesCSSSelectorAllowList(\n    elements: Element[],\n    autocaptureConfig: AutocaptureConfig | undefined\n): boolean {\n    const allowlist = autocaptureConfig?.css_selector_allowlist\n    if (isUndefined(allowlist)) {\n        // everything is allowed, when there is no allow list\n        return true\n    }\n\n    // check each element in the tree\n    // if any of the elements are in the allow list, then the tree is allowed\n    for (const el of elements) {\n        if (allowlist.some((selector) => el.matches(selector))) {\n            return true\n        }\n    }\n\n    // otherwise there is an allow list and this element tree didn't match it\n    return false\n}\n\nexport function getParentElement(curEl: Element): Element | false {\n    const parentNode = curEl.parentNode\n    if (!parentNode || !isElementNode(parentNode)) return false\n    return parentNode\n}\n\n/*\n * Check whether a DOM event should be \"captured\" or if it may contain sentitive data\n * using a variety of heuristics.\n * @param {Element} el - element to check\n * @param {Event} event - event to check\n * @param {Object} autocaptureConfig - autocapture config\n * @param {boolean} captureOnAnyElement - whether to capture on any element, clipboard autocapture doesn't restrict to \"clickable\" elements\n * @param {string[]} allowedEventTypes - event types to capture, normally just 'click', but some autocapture types react to different events, some elements have fixed events (e.g., form has \"submit\")\n * @returns {boolean} whether the event should be captured\n */\nexport function shouldCaptureDomEvent(\n    el: Element,\n    event: Event,\n    autocaptureConfig: AutocaptureConfig | undefined = undefined,\n    captureOnAnyElement?: boolean,\n    allowedEventTypes?: string[]\n): boolean {\n    if (!window || !el || isTag(el, 'html') || !isElementNode(el)) {\n        return false\n    }\n\n    if (autocaptureConfig?.url_allowlist) {\n        // if the current URL is not in the allow list, don't capture\n        if (!checkForURLMatches(autocaptureConfig.url_allowlist)) {\n            return false\n        }\n    }\n\n    if (autocaptureConfig?.url_ignorelist) {\n        // if the current URL is in the ignore list, don't capture\n        if (checkForURLMatches(autocaptureConfig.url_ignorelist)) {\n            return false\n        }\n    }\n\n    if (autocaptureConfig?.dom_event_allowlist) {\n        const allowlist = autocaptureConfig.dom_event_allowlist\n        if (allowlist && !allowlist.some((eventType) => event.type === eventType)) {\n            return false\n        }\n    }\n\n    let parentIsUsefulElement = false\n    const targetElementList: Element[] = [el]\n    let parentNode: Element | boolean = true\n    let curEl: Element = el\n    while (curEl.parentNode && !isTag(curEl, 'body')) {\n        // If element is a shadow root, we skip it\n        if (isDocumentFragment(curEl.parentNode)) {\n            targetElementList.push((curEl.parentNode as any).host)\n            curEl = (curEl.parentNode as any).host\n            continue\n        }\n        parentNode = getParentElement(curEl)\n        if (!parentNode) break\n        if (captureOnAnyElement || autocaptureCompatibleElements.indexOf(parentNode.tagName.toLowerCase()) > -1) {\n            parentIsUsefulElement = true\n        } else {\n            const compStyles = window.getComputedStyle(parentNode)\n            if (compStyles && compStyles.getPropertyValue('cursor') === 'pointer') {\n                parentIsUsefulElement = true\n            }\n        }\n\n        targetElementList.push(parentNode)\n        curEl = parentNode\n    }\n\n    if (!checkIfElementTreePassesElementAllowList(targetElementList, autocaptureConfig)) {\n        return false\n    }\n\n    if (!checkIfElementTreePassesCSSSelectorAllowList(targetElementList, autocaptureConfig)) {\n        return false\n    }\n\n    const compStyles = window.getComputedStyle(el)\n    if (compStyles && compStyles.getPropertyValue('cursor') === 'pointer' && event.type === 'click') {\n        return true\n    }\n\n    const tag = el.tagName.toLowerCase()\n    switch (tag) {\n        case 'html':\n            return false\n        case 'form':\n            return (allowedEventTypes || ['submit']).indexOf(event.type) >= 0\n        case 'input':\n        case 'select':\n        case 'textarea':\n            return (allowedEventTypes || ['change', 'click']).indexOf(event.type) >= 0\n        default:\n            if (parentIsUsefulElement) return (allowedEventTypes || ['click']).indexOf(event.type) >= 0\n            return (\n                (allowedEventTypes || ['click']).indexOf(event.type) >= 0 &&\n                (autocaptureCompatibleElements.indexOf(tag) > -1 || el.getAttribute('contenteditable') === 'true')\n            )\n    }\n}\n\n/*\n * Check whether a DOM element should be \"captured\" or if it may contain sentitive data\n * using a variety of heuristics.\n * @param {Element} el - element to check\n * @returns {boolean} whether the element should be captured\n */\nexport function shouldCaptureElement(el: Element): boolean {\n    for (let curEl = el; curEl.parentNode && !isTag(curEl, 'body'); curEl = curEl.parentNode as Element) {\n        const classes = getClassNames(curEl)\n        if (includes(classes, 'ph-sensitive') || includes(classes, 'ph-no-capture')) {\n            return false\n        }\n    }\n\n    if (includes(getClassNames(el), 'ph-include')) {\n        return true\n    }\n\n    // don't include hidden or password fields\n    const type = (el as HTMLInputElement).type || ''\n    if (isString(type)) {\n        // it's possible for el.type to be a DOM element if el is a form with a child input[name=\"type\"]\n        switch (type.toLowerCase()) {\n            case 'hidden':\n                return false\n            case 'password':\n                return false\n        }\n    }\n\n    // filter out data from fields that look like sensitive fields\n    const name = (el as HTMLInputElement).name || el.id || ''\n    // See https://github.com/posthog/posthog-js/issues/165\n    // Under specific circumstances a bug caused .replace to be called on a DOM element\n    // instead of a string, removing the element from the page. Ensure this issue is mitigated.\n    if (isString(name)) {\n        // it's possible for el.name or el.id to be a DOM element if el is a form with a child input[name=\"name\"]\n        const sensitiveNameRegex =\n            /^cc|cardnum|ccnum|creditcard|csc|cvc|cvv|exp|pass|pwd|routing|seccode|securitycode|securitynum|socialsec|socsec|ssn/i\n        if (sensitiveNameRegex.test(name.replace(/[^a-zA-Z0-9]/g, ''))) {\n            return false\n        }\n    }\n\n    return true\n}\n\n/*\n * Check whether a DOM element is 'sensitive' and we should only capture limited data\n * @param {Element} el - element to check\n * @returns {boolean} whether the element should be captured\n */\nexport function isSensitiveElement(el: Element): boolean {\n    // don't send data from inputs or similar elements since there will always be\n    // a risk of clientside javascript placing sensitive data in attributes\n    const allowedInputTypes = ['button', 'checkbox', 'submit', 'reset']\n    if (\n        (isTag(el, 'input') && !allowedInputTypes.includes((el as HTMLInputElement).type)) ||\n        isTag(el, 'select') ||\n        isTag(el, 'textarea') ||\n        el.getAttribute('contenteditable') === 'true'\n    ) {\n        return true\n    }\n    return false\n}\n\n// Define the core pattern for matching credit card numbers\nconst coreCCPattern = `(4[0-9]{12}(?:[0-9]{3})?)|(5[1-5][0-9]{14})|(6(?:011|5[0-9]{2})[0-9]{12})|(3[47][0-9]{13})|(3(?:0[0-5]|[68][0-9])[0-9]{11})|((?:2131|1800|35[0-9]{3})[0-9]{11})`\n// Create the Anchored version of the regex by adding '^' at the start and '$' at the end\nconst anchoredCCRegex = new RegExp(`^(?:${coreCCPattern})$`)\n// The Unanchored version is essentially the core pattern, usable as is for partial matches\nconst unanchoredCCRegex = new RegExp(coreCCPattern)\n\n// Define the core pattern for matching SSNs with optional dashes\nconst coreSSNPattern = `\\\\d{3}-?\\\\d{2}-?\\\\d{4}`\n// Create the Anchored version of the regex by adding '^' at the start and '$' at the end\nconst anchoredSSNRegex = new RegExp(`^(${coreSSNPattern})$`)\n// The Unanchored version is essentially the core pattern itself, usable for partial matches\nconst unanchoredSSNRegex = new RegExp(`(${coreSSNPattern})`)\n\n/*\n * Check whether a string value should be \"captured\" or if it may contain sensitive data\n * using a variety of heuristics.\n * @param {string} value - string value to check\n * @param {boolean} anchorRegexes - whether to anchor the regexes to the start and end of the string\n * @returns {boolean} whether the element should be captured\n */\nexport function shouldCaptureValue(value: string, anchorRegexes = true): boolean {\n    if (isNullish(value)) {\n        return false\n    }\n\n    if (isString(value)) {\n        value = trim(value)\n\n        // check to see if input value looks like a credit card number\n        // see: https://www.safaribooksonline.com/library/view/regular-expressions-cookbook/9781449327453/ch04s20.html\n        const ccRegex = anchorRegexes ? anchoredCCRegex : unanchoredCCRegex\n        if (ccRegex.test((value || '').replace(/[- ]/g, ''))) {\n            return false\n        }\n\n        // check to see if input value looks like a social security number\n        const ssnRegex = anchorRegexes ? anchoredSSNRegex : unanchoredSSNRegex\n        if (ssnRegex.test(value)) {\n            return false\n        }\n    }\n\n    return true\n}\n\n/*\n * Check whether an attribute name is an Angular style attr (either _ngcontent or _nghost)\n * These update on each build and lead to noise in the element chain\n * More details on the attributes here: https://angular.io/guide/view-encapsulation\n * @param {string} attributeName - string value to check\n * @returns {boolean} whether the element is an angular tag\n */\nexport function isAngularStyleAttr(attributeName: string): boolean {\n    if (isString(attributeName)) {\n        return attributeName.substring(0, 10) === '_ngcontent' || attributeName.substring(0, 7) === '_nghost'\n    }\n    return false\n}\n\n/*\n * Iterate through children of a target element looking for span tags\n * and return the text content of the span tags, separated by spaces,\n * along with the direct text content of the target element\n * @param {Element} target - element to check\n * @returns {string} text content of the target element and its child span tags\n */\nexport function getDirectAndNestedSpanText(target: Element): string {\n    let text = getSafeText(target)\n    text = `${text} ${getNestedSpanText(target)}`.trim()\n    return shouldCaptureValue(text) ? text : ''\n}\n\n/*\n * Iterate through children of a target element looking for span tags\n * and return the text content of the span tags, separated by spaces\n * @param {Element} target - element to check\n * @returns {string} text content of span tags\n */\nexport function getNestedSpanText(target: Element): string {\n    let text = ''\n    if (target && target.childNodes && target.childNodes.length) {\n        each(target.childNodes, function (child) {\n            if (child && child.tagName?.toLowerCase() === 'span') {\n                try {\n                    const spanText = getSafeText(child)\n                    text = `${text} ${spanText}`.trim()\n\n                    if (child.childNodes && child.childNodes.length) {\n                        text = `${text} ${getNestedSpanText(child)}`.trim()\n                    }\n                } catch (e) {\n                    logger.error('[AutoCapture]', e)\n                }\n            }\n        })\n    }\n    return text\n}\n\n/*\nBack in the day storing events in Postgres we use Elements for autocapture events.\nNow we're using elements_chain. We used to do this parsing/processing during ingestion.\nThis code is just copied over from ingestion, but we should optimize it\nto create elements_chain string directly.\n*/\nexport function getElementsChainString(elements: Properties[]): string {\n    return elementsToString(extractElements(elements))\n}\n\n// This interface is called 'Element' in plugin-scaffold https://github.com/PostHog/plugin-scaffold/blob/b07d3b879796ecc7e22deb71bf627694ba05386b/src/types.ts#L200\n// However 'Element' is a DOM Element when run in the browser, so we have to rename it\ninterface PHElement {\n    text?: string\n    tag_name?: string\n    href?: string\n    attr_id?: string\n    attr_class?: string[]\n    nth_child?: number\n    nth_of_type?: number\n    attributes?: Record<string, any>\n    event_id?: number\n    order?: number\n    group_id?: number\n}\n\nfunction escapeQuotes(input: string): string {\n    return input.replace(/\"|\\\\\"/g, '\\\\\"')\n}\n\nfunction elementsToString(elements: PHElement[]): string {\n    const ret = elements.map((element) => {\n        let el_string = ''\n        if (element.tag_name) {\n            el_string += element.tag_name\n        }\n        if (element.attr_class) {\n            element.attr_class.sort()\n            for (const single_class of element.attr_class) {\n                el_string += `.${single_class.replace(/\"/g, '')}`\n            }\n        }\n        const attributes: Record<string, any> = {\n            ...(element.text ? { text: element.text } : {}),\n            'nth-child': element.nth_child ?? 0,\n            'nth-of-type': element.nth_of_type ?? 0,\n            ...(element.href ? { href: element.href } : {}),\n            ...(element.attr_id ? { attr_id: element.attr_id } : {}),\n            ...element.attributes,\n        }\n        const sortedAttributes: Record<string, any> = {}\n        entries(attributes)\n            .sort(([a], [b]) => a.localeCompare(b))\n            .forEach(\n                ([key, value]) => (sortedAttributes[escapeQuotes(key.toString())] = escapeQuotes(value.toString()))\n            )\n        el_string += ':'\n        el_string += entries(sortedAttributes)\n            .map(([key, value]) => `${key}=\"${value}\"`)\n            .join('')\n        return el_string\n    })\n    return ret.join(';')\n}\n\nfunction extractElements(elements: Properties[]): PHElement[] {\n    return elements.map((el) => {\n        const response = {\n            text: el['$el_text']?.slice(0, 400),\n            tag_name: el['tag_name'],\n            href: el['attr__href']?.slice(0, 2048),\n            attr_class: extractAttrClass(el),\n            attr_id: el['attr__id'],\n            nth_child: el['nth_child'],\n            nth_of_type: el['nth_of_type'],\n            attributes: {} as { [id: string]: any },\n        }\n\n        entries(el)\n            .filter(([key]) => key.indexOf('attr__') === 0)\n            .forEach(([key, value]) => (response.attributes[key] = value))\n        return response\n    })\n}\n\nfunction extractAttrClass(el: Properties): PHElement['attr_class'] {\n    const attr_class = el['attr__class']\n    if (!attr_class) {\n        return undefined\n    } else if (isArray(attr_class)) {\n        return attr_class\n    } else {\n        return splitClassString(attr_class)\n    }\n}\n", "import { addEventListener, each, extend } from './utils'\nimport {\n    autocaptureCompatibleElements,\n    getClassNames,\n    getDirectAndNestedSpanText,\n    getElementsChainString,\n    getEventTarget,\n    getSafeText,\n    isAngularStyleAttr,\n    isSensitiveElement,\n    makeSafeText,\n    shouldCaptureDomEvent,\n    shouldCaptureElement,\n    shouldCaptureValue,\n    splitClassString,\n} from './autocapture-utils'\nimport RageClick from './extensions/rageclick'\nimport { AutocaptureConfig, COPY_AUTOCAPTURE_EVENT, EventName, Properties, RemoteConfig } from './types'\nimport { PostHog } from './posthog-core'\nimport { AUTOCAPTURE_DISABLED_SERVER_SIDE } from './constants'\n\nimport { isBoolean, isFunction, isNull, isObject } from './utils/type-utils'\nimport { createLogger } from './utils/logger'\nimport { document, window } from './utils/globals'\nimport { convertToURL } from './utils/request-utils'\nimport { isDocumentFragment, isElementNode, isTag, isTextNode } from './utils/element-utils'\nimport { includes } from './utils/string-utils'\n\nconst logger = createLogger('[AutoCapture]')\n\nfunction limitText(length: number, text: string): string {\n    if (text.length > length) {\n        return text.slice(0, length) + '...'\n    }\n    return text\n}\n\nexport function getAugmentPropertiesFromElement(elem: Element): Properties {\n    const shouldCaptureEl = shouldCaptureElement(elem)\n    if (!shouldCaptureEl) {\n        return {}\n    }\n\n    const props: Properties = {}\n\n    each(elem.attributes, function (attr: Attr) {\n        if (attr.name && attr.name.indexOf('data-ph-capture-attribute') === 0) {\n            const propertyKey = attr.name.replace('data-ph-capture-attribute-', '')\n            const propertyValue = attr.value\n            if (propertyKey && propertyValue && shouldCaptureValue(propertyValue)) {\n                props[propertyKey] = propertyValue\n            }\n        }\n    })\n\n    return props\n}\n\nexport function previousElementSibling(el: Element): Element | null {\n    if (el.previousElementSibling) {\n        return el.previousElementSibling\n    }\n    let _el: Element | null = el\n    do {\n        _el = _el.previousSibling as Element | null // resolves to ChildNode->Node, which is Element's parent class\n    } while (_el && !isElementNode(_el))\n    return _el\n}\n\nexport function getDefaultProperties(eventType: string): Properties {\n    return {\n        $event_type: eventType,\n        $ce_version: 1,\n    }\n}\n\nexport function getPropertiesFromElement(\n    elem: Element,\n    maskAllAttributes: boolean,\n    maskText: boolean,\n    elementAttributeIgnorelist: string[] | undefined\n): Properties {\n    const tag_name = elem.tagName.toLowerCase()\n    const props: Properties = {\n        tag_name: tag_name,\n    }\n    if (autocaptureCompatibleElements.indexOf(tag_name) > -1 && !maskText) {\n        if (tag_name.toLowerCase() === 'a' || tag_name.toLowerCase() === 'button') {\n            props['$el_text'] = limitText(1024, getDirectAndNestedSpanText(elem))\n        } else {\n            props['$el_text'] = limitText(1024, getSafeText(elem))\n        }\n    }\n\n    const classes = getClassNames(elem)\n    if (classes.length > 0)\n        props['classes'] = classes.filter(function (c) {\n            return c !== ''\n        })\n\n    // capture the deny list here because this not-a-class class makes it tricky to use this.config in the function below\n    each(elem.attributes, function (attr: Attr) {\n        // Only capture attributes we know are safe\n        if (isSensitiveElement(elem) && ['name', 'id', 'class', 'aria-label'].indexOf(attr.name) === -1) return\n\n        if (elementAttributeIgnorelist?.includes(attr.name)) return\n\n        if (!maskAllAttributes && shouldCaptureValue(attr.value) && !isAngularStyleAttr(attr.name)) {\n            let value = attr.value\n            if (attr.name === 'class') {\n                // html attributes can _technically_ contain linebreaks,\n                // but we're very intolerant of them in the class string,\n                // so we strip them.\n                value = splitClassString(value).join(' ')\n            }\n            props['attr__' + attr.name] = limitText(1024, value)\n        }\n    })\n\n    let nthChild = 1\n    let nthOfType = 1\n    let currentElem: Element | null = elem\n    while ((currentElem = previousElementSibling(currentElem))) {\n        // eslint-disable-line no-cond-assign\n        nthChild++\n        if (currentElem.tagName === elem.tagName) {\n            nthOfType++\n        }\n    }\n    props['nth_child'] = nthChild\n    props['nth_of_type'] = nthOfType\n\n    return props\n}\n\nexport function autocapturePropertiesForElement(\n    target: Element,\n    {\n        e,\n        maskAllElementAttributes,\n        maskAllText,\n        elementAttributeIgnoreList,\n        elementsChainAsString,\n    }: {\n        e: Event\n        maskAllElementAttributes: boolean\n        maskAllText: boolean\n        elementAttributeIgnoreList?: string[] | undefined\n        elementsChainAsString: boolean\n    }\n): { props: Properties; explicitNoCapture?: boolean } {\n    const targetElementList = [target]\n    let curEl = target\n    while (curEl.parentNode && !isTag(curEl, 'body')) {\n        if (isDocumentFragment(curEl.parentNode)) {\n            targetElementList.push((curEl.parentNode as any).host)\n            curEl = (curEl.parentNode as any).host\n            continue\n        }\n        targetElementList.push(curEl.parentNode as Element)\n        curEl = curEl.parentNode as Element\n    }\n\n    const elementsJson: Properties[] = []\n    const autocaptureAugmentProperties: Properties = {}\n    let href: string | false = false\n    let explicitNoCapture = false\n\n    each(targetElementList, (el) => {\n        const shouldCaptureEl = shouldCaptureElement(el)\n\n        // if the element or a parent element is an anchor tag\n        // include the href as a property\n        if (el.tagName.toLowerCase() === 'a') {\n            href = el.getAttribute('href')\n            href = shouldCaptureEl && href && shouldCaptureValue(href) && href\n        }\n\n        // allow users to programmatically prevent capturing of elements by adding class 'ph-no-capture'\n        const classes = getClassNames(el)\n        if (includes(classes, 'ph-no-capture')) {\n            explicitNoCapture = true\n        }\n\n        elementsJson.push(\n            getPropertiesFromElement(el, maskAllElementAttributes, maskAllText, elementAttributeIgnoreList)\n        )\n\n        const augmentProperties = getAugmentPropertiesFromElement(el)\n        extend(autocaptureAugmentProperties, augmentProperties)\n    })\n\n    if (explicitNoCapture) {\n        return { props: {}, explicitNoCapture }\n    }\n\n    if (!maskAllText) {\n        // if the element is a button or anchor tag get the span text from any\n        // children and include it as/with the text property on the parent element\n        if (target.tagName.toLowerCase() === 'a' || target.tagName.toLowerCase() === 'button') {\n            elementsJson[0]['$el_text'] = getDirectAndNestedSpanText(target)\n        } else {\n            elementsJson[0]['$el_text'] = getSafeText(target)\n        }\n    }\n\n    let externalHref: string | undefined\n    if (href) {\n        elementsJson[0]['attr__href'] = href\n        const hrefHost = convertToURL(href)?.host\n        const locationHost = window?.location?.host\n        if (hrefHost && locationHost && hrefHost !== locationHost) {\n            externalHref = href\n        }\n    }\n\n    const props = extend(\n        getDefaultProperties(e.type),\n        // Sending \"$elements\" is deprecated. Only one client on US cloud uses this.\n        !elementsChainAsString ? { $elements: elementsJson } : {},\n        // Always send $elements_chain, as it's needed downstream in site app filtering\n        { $elements_chain: getElementsChainString(elementsJson) },\n        elementsJson[0]?.['$el_text'] ? { $el_text: elementsJson[0]?.['$el_text'] } : {},\n        externalHref && e.type === 'click' ? { $external_click_url: externalHref } : {},\n        autocaptureAugmentProperties\n    )\n\n    return { props }\n}\n\nexport class Autocapture {\n    instance: PostHog\n    _initialized: boolean = false\n    _isDisabledServerSide: boolean | null = null\n    _elementSelectors: Set<string> | null\n    rageclicks = new RageClick()\n    _elementsChainAsString = false\n\n    constructor(instance: PostHog) {\n        this.instance = instance\n        this._elementSelectors = null\n    }\n\n    private get _config(): AutocaptureConfig {\n        const config = isObject(this.instance.config.autocapture) ? this.instance.config.autocapture : {}\n        // precompile the regex\n        config.url_allowlist = config.url_allowlist?.map((url) => new RegExp(url))\n        config.url_ignorelist = config.url_ignorelist?.map((url) => new RegExp(url))\n        return config\n    }\n\n    _addDomEventHandlers(): void {\n        if (!this.isBrowserSupported()) {\n            logger.info('Disabling Automatic Event Collection because this browser is not supported')\n            return\n        }\n\n        if (!window || !document) {\n            return\n        }\n\n        const handler = (e: Event) => {\n            e = e || window?.event\n            try {\n                this._captureEvent(e)\n            } catch (error) {\n                logger.error('Failed to capture event', error)\n            }\n        }\n\n        addEventListener(document, 'submit', handler, { capture: true })\n        addEventListener(document, 'change', handler, { capture: true })\n        addEventListener(document, 'click', handler, { capture: true })\n\n        if (this._config.capture_copied_text) {\n            const copiedTextHandler = (e: Event) => {\n                e = e || window?.event\n                this._captureEvent(e, COPY_AUTOCAPTURE_EVENT)\n            }\n\n            addEventListener(document, 'copy', copiedTextHandler, { capture: true })\n            addEventListener(document, 'cut', copiedTextHandler, { capture: true })\n        }\n    }\n\n    public startIfEnabled() {\n        if (this.isEnabled && !this._initialized) {\n            this._addDomEventHandlers()\n            this._initialized = true\n        }\n    }\n\n    public onRemoteConfig(response: RemoteConfig) {\n        if (response.elementsChainAsString) {\n            this._elementsChainAsString = response.elementsChainAsString\n        }\n\n        if (this.instance.persistence) {\n            this.instance.persistence.register({\n                [AUTOCAPTURE_DISABLED_SERVER_SIDE]: !!response['autocapture_opt_out'],\n            })\n        }\n        // store this in-memory in case persistence is disabled\n        this._isDisabledServerSide = !!response['autocapture_opt_out']\n        this.startIfEnabled()\n    }\n\n    public setElementSelectors(selectors: Set<string>): void {\n        this._elementSelectors = selectors\n    }\n\n    public getElementSelectors(element: Element | null): string[] | null {\n        const elementSelectors: string[] = []\n\n        this._elementSelectors?.forEach((selector) => {\n            const matchedElements = document?.querySelectorAll(selector)\n            matchedElements?.forEach((matchedElement: Element) => {\n                if (element === matchedElement) {\n                    elementSelectors.push(selector)\n                }\n            })\n        })\n\n        return elementSelectors\n    }\n\n    public get isEnabled(): boolean {\n        const persistedServerDisabled = this.instance.persistence?.props[AUTOCAPTURE_DISABLED_SERVER_SIDE]\n        const memoryDisabled = this._isDisabledServerSide\n\n        if (isNull(memoryDisabled) && !isBoolean(persistedServerDisabled) && !this.instance._shouldDisableFlags()) {\n            // We only enable if we know that the server has not disabled it (unless /flags is disabled)\n            return false\n        }\n\n        const disabledServer = this._isDisabledServerSide ?? !!persistedServerDisabled\n        const disabledClient = !this.instance.config.autocapture\n        return !disabledClient && !disabledServer\n    }\n\n    private _captureEvent(e: Event, eventName: EventName = '$autocapture'): boolean | void {\n        if (!this.isEnabled) {\n            return\n        }\n\n        /*** Don't mess with this code without running IE8 tests on it ***/\n        let target = getEventTarget(e)\n        if (isTextNode(target)) {\n            // defeat Safari bug (see: http://www.quirksmode.org/js/events_properties.html)\n            target = (target.parentNode || null) as Element | null\n        }\n\n        if (eventName === '$autocapture' && e.type === 'click' && e instanceof MouseEvent) {\n            if (\n                this.instance.config.rageclick &&\n                this.rageclicks?.isRageClick(e.clientX, e.clientY, new Date().getTime())\n            ) {\n                this._captureEvent(e, '$rageclick')\n            }\n        }\n\n        const isCopyAutocapture = eventName === COPY_AUTOCAPTURE_EVENT\n        if (\n            target &&\n            shouldCaptureDomEvent(\n                target,\n                e,\n                this._config,\n                // mostly this method cares about the target element, but in the case of copy events,\n                // we want some of the work this check does without insisting on the target element's type\n                isCopyAutocapture,\n                // we also don't want to restrict copy checks to clicks,\n                // so we pass that knowledge in here, rather than add the logic inside the check\n                isCopyAutocapture ? ['copy', 'cut'] : undefined\n            )\n        ) {\n            const { props, explicitNoCapture } = autocapturePropertiesForElement(target, {\n                e,\n                maskAllElementAttributes: this.instance.config.mask_all_element_attributes,\n                maskAllText: this.instance.config.mask_all_text,\n                elementAttributeIgnoreList: this._config.element_attribute_ignorelist,\n                elementsChainAsString: this._elementsChainAsString,\n            })\n\n            if (explicitNoCapture) {\n                return false\n            }\n\n            const elementSelectors = this.getElementSelectors(target)\n            if (elementSelectors && elementSelectors.length > 0) {\n                props['$element_selectors'] = elementSelectors\n            }\n\n            if (eventName === COPY_AUTOCAPTURE_EVENT) {\n                // you can't read the data from the clipboard event,\n                // but you can guess that you can read it from the window's current selection\n                const selectedContent = makeSafeText(window?.getSelection()?.toString())\n                const clipType = (e as ClipboardEvent).type || 'clipboard'\n                if (!selectedContent) {\n                    return false\n                }\n                props['$selected_content'] = selectedContent\n                props['$copy_type'] = clipType\n            }\n\n            this.instance.capture(eventName, props)\n            return true\n        }\n    }\n\n    isBrowserSupported(): boolean {\n        return isFunction(document?.querySelectorAll)\n    }\n}\n", "import { each } from './'\n\nimport { isArray, isFile, isUndefined } from './type-utils'\nimport { logger } from './logger'\nimport { document } from './globals'\n\nconst localDomains = ['localhost', '127.0.0.1']\n\n/**\n * IE11 doesn't support `new URL`\n * so we can create an anchor element and use that to parse the URL\n * there's a lot of overlap between HTMLHyperlinkElementUtils and URL\n * meaning useful properties like `pathname` are available on both\n */\nexport const convertToURL = (url: string): HTMLAnchorElement | null => {\n    const location = document?.createElement('a')\n    if (isUndefined(location)) {\n        return null\n    }\n\n    location.href = url\n    return location\n}\n\nexport const formDataToQuery = function (formdata: Record<string, any> | FormData, arg_separator = '&'): string {\n    let use_val: string\n    let use_key: string\n    const tph_arr: string[] = []\n\n    each(formdata, function (val: File | string | undefined, key: string | undefined) {\n        // the key might be literally the string undefined for e.g. if {undefined: 'something'}\n        if (isUndefined(val) || isUndefined(key) || key === 'undefined') {\n            return\n        }\n\n        use_val = encodeURIComponent(isFile(val) ? val.name : val.toString())\n        use_key = encodeURIComponent(key)\n        tph_arr[tph_arr.length] = use_key + '=' + use_val\n    })\n\n    return tph_arr.join(arg_separator)\n}\n\n// NOTE: Once we get rid of IE11/op_mini we can start using URLSearchParams\nexport const getQueryParam = function (url: string, param: string): string {\n    const withoutHash: string = url.split('#')[0] || ''\n\n    // Split only on the first ? to sort problem out for those with multiple ?s\n    // and then remove them\n    const queryParams: string = withoutHash.split(/\\?(.*)/)[1] || ''\n    const cleanedQueryParams = queryParams.replace(/^\\?+/g, '')\n\n    const queryParts = cleanedQueryParams.split('&')\n    let keyValuePair\n\n    for (let i = 0; i < queryParts.length; i++) {\n        const parts = queryParts[i].split('=')\n        if (parts[0] === param) {\n            keyValuePair = parts\n            break\n        }\n    }\n\n    if (!isArray(keyValuePair) || keyValuePair.length < 2) {\n        return ''\n    } else {\n        let result = keyValuePair[1]\n        try {\n            result = decodeURIComponent(result)\n        } catch {\n            logger.error('Skipping decoding for malformed query param: ' + result)\n        }\n        return result.replace(/\\+/g, ' ')\n    }\n}\n\n// replace any query params in the url with the provided mask value. Tries to keep the URL as instant as possible,\n// including preserving malformed text in most cases\nexport const maskQueryParams = function <T extends string | undefined>(\n    url: T,\n    maskedParams: string[] | undefined,\n    mask: string\n): T extends string ? string : undefined {\n    if (!url || !maskedParams || !maskedParams.length) {\n        return url as any\n    }\n\n    const splitHash = url.split('#')\n    const withoutHash: string = splitHash[0] || ''\n    const hash = splitHash[1]\n\n    const splitQuery: string[] = withoutHash.split('?')\n    const queryString: string = splitQuery[1]\n    const urlWithoutQueryAndHash: string = splitQuery[0]\n    const queryParts = (queryString || '').split('&')\n\n    // use an array of strings rather than an object to preserve ordering and duplicates\n    const paramStrings: string[] = []\n\n    for (let i = 0; i < queryParts.length; i++) {\n        const keyValuePair = queryParts[i].split('=')\n        if (!isArray(keyValuePair)) {\n            continue\n        } else if (maskedParams.includes(keyValuePair[0])) {\n            paramStrings.push(keyValuePair[0] + '=' + mask)\n        } else {\n            paramStrings.push(queryParts[i])\n        }\n    }\n\n    let result = urlWithoutQueryAndHash\n    if (queryString != null) {\n        result += '?' + paramStrings.join('&')\n    }\n    if (hash != null) {\n        result += '#' + hash\n    }\n\n    return result as any\n}\n\nexport const _getHashParam = function (hash: string, param: string): string | null {\n    const matches = hash.match(new RegExp(param + '=([^&]*)'))\n    return matches ? matches[1] : null\n}\n\nexport const isLocalhost = (): boolean => {\n    return localDomains.includes(location.hostname)\n}\n", "/**\n * adapted from https://github.com/getsentry/sentry-javascript/blob/72751dacb88c5b970d8bac15052ee8e09b28fd5d/packages/browser-utils/src/getNativeImplementation.ts#L27\n * and https://github.com/PostHog/rrweb/blob/804380afbb1b9bed70b8792cb5a25d827f5c0cb5/packages/utils/src/index.ts#L31\n * after a number of performance reports from Angular users\n */\n\nimport { AssignableWindow } from './globals'\nimport { isAngularZonePresent, isFunction, isNativeFunction } from './type-utils'\nimport { logger } from './logger'\n\ninterface NativeImplementationsCache {\n    MutationObserver: typeof MutationObserver\n}\n\nconst cachedImplementations: Partial<NativeImplementationsCache> = {}\n\nexport function getNativeImplementation<T extends keyof NativeImplementationsCache>(\n    name: T,\n    assignableWindow: AssignableWindow\n): NativeImplementationsCache[T] {\n    const cached = cachedImplementations[name]\n    if (cached) {\n        return cached\n    }\n\n    let impl = assignableWindow[name] as NativeImplementationsCache[T]\n\n    if (isNativeFunction(impl) && !isAngularZonePresent()) {\n        return (cachedImplementations[name] = impl.bind(assignableWindow) as NativeImplementationsCache[T])\n    }\n\n    const document = assignableWindow.document\n    if (document && isFunction(document.createElement)) {\n        try {\n            const sandbox = document.createElement('iframe')\n            sandbox.hidden = true\n            document.head.appendChild(sandbox)\n            const contentWindow = sandbox.contentWindow\n            if (contentWindow && (contentWindow as any)[name]) {\n                impl = (contentWindow as any)[name] as NativeImplementationsCache[T]\n            }\n            document.head.removeChild(sandbox)\n        } catch (e) {\n            // Could not create sandbox iframe, just use assignableWindow.xxx\n            logger.warn(`Could not create sandbox iframe for ${name} check, bailing to assignableWindow.${name}: `, e)\n        }\n    }\n\n    // Sanity check: This _should_ not happen, but if it does, we just skip caching...\n    // This can happen e.g. in tests where fetch may not be available in the env, or similar.\n    if (!impl || !isFunction(impl)) {\n        return impl\n    }\n\n    return (cachedImplementations[name] = impl.bind(assignableWindow) as NativeImplementationsCache[T])\n}\n\nexport function getNativeMutationObserverImplementation(assignableWindow: AssignableWindow): typeof MutationObserver {\n    return getNativeImplementation('MutationObserver', assignableWindow)\n}\n", "import { assignableWindow, LazyLoadedDeadClicksAutocaptureInterface } from '../utils/globals'\nimport { PostHog } from '../posthog-core'\nimport { isNull, isNumber, isUndefined } from '../utils/type-utils'\nimport { autocaptureCompatibleElements, getEventTarget } from '../autocapture-utils'\nimport { DeadClickCandidate, DeadClicksAutoCaptureConfig, Properties } from '../types'\nimport { autocapturePropertiesForElement } from '../autocapture'\nimport { isElementInToolbar, isElementNode, isTag } from '../utils/element-utils'\nimport { getNativeMutationObserverImplementation } from '../utils/prototype-utils'\nimport { addEventListener } from '../utils'\n\nfunction asClick(event: MouseEvent): DeadClickCandidate | null {\n    const eventTarget = getEventTarget(event)\n    if (eventTarget) {\n        return {\n            node: eventTarget,\n            originalEvent: event,\n            timestamp: Date.now(),\n        }\n    }\n    return null\n}\n\nfunction checkTimeout(value: number | undefined, thresholdMs: number) {\n    return isNumber(value) && value >= thresholdMs\n}\n\nclass LazyLoadedDeadClicksAutocapture implements LazyLoadedDeadClicksAutocaptureInterface {\n    private _mutationObserver: MutationObserver | undefined\n    private _lastMutation: number | undefined\n    private _lastSelectionChanged: number | undefined\n    private _clicks: DeadClickCandidate[] = []\n    private _checkClickTimer: number | undefined\n    private _config: Required<DeadClicksAutoCaptureConfig>\n    private _onCapture: (click: DeadClickCandidate, properties: Properties) => void\n\n    private _defaultConfig = (defaultOnCapture: (click: DeadClickCandidate, properties: Properties) => void) => ({\n        element_attribute_ignorelist: [],\n        scroll_threshold_ms: 100,\n        selection_change_threshold_ms: 100,\n        mutation_threshold_ms: 2500,\n        __onCapture: defaultOnCapture,\n    })\n\n    private _asRequiredConfig(providedConfig?: DeadClicksAutoCaptureConfig): Required<DeadClicksAutoCaptureConfig> {\n        const defaultConfig = this._defaultConfig(providedConfig?.__onCapture || this._captureDeadClick.bind(this))\n        return {\n            element_attribute_ignorelist:\n                providedConfig?.element_attribute_ignorelist ?? defaultConfig.element_attribute_ignorelist,\n            scroll_threshold_ms: providedConfig?.scroll_threshold_ms ?? defaultConfig.scroll_threshold_ms,\n            selection_change_threshold_ms:\n                providedConfig?.selection_change_threshold_ms ?? defaultConfig.selection_change_threshold_ms,\n            mutation_threshold_ms: providedConfig?.mutation_threshold_ms ?? defaultConfig.mutation_threshold_ms,\n            __onCapture: defaultConfig.__onCapture,\n        }\n    }\n\n    constructor(\n        readonly instance: PostHog,\n        config?: DeadClicksAutoCaptureConfig\n    ) {\n        this._config = this._asRequiredConfig(config)\n        this._onCapture = this._config.__onCapture\n    }\n\n    start(observerTarget: Node) {\n        this._startClickObserver()\n        this._startScrollObserver()\n        this._startSelectionChangedObserver()\n        this._startMutationObserver(observerTarget)\n    }\n\n    private _startMutationObserver(observerTarget: Node) {\n        if (!this._mutationObserver) {\n            const NativeMutationObserver = getNativeMutationObserverImplementation(assignableWindow)\n            this._mutationObserver = new NativeMutationObserver((mutations) => {\n                this._onMutation(mutations)\n            })\n            this._mutationObserver.observe(observerTarget, {\n                attributes: true,\n                characterData: true,\n                childList: true,\n                subtree: true,\n            })\n        }\n    }\n\n    stop() {\n        this._mutationObserver?.disconnect()\n        this._mutationObserver = undefined\n        assignableWindow.removeEventListener('click', this._onClick)\n        assignableWindow.removeEventListener('scroll', this._onScroll, { capture: true })\n        assignableWindow.removeEventListener('selectionchange', this._onSelectionChange)\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    private _onMutation(_mutations: MutationRecord[]): void {\n        // we don't actually care about the content of the mutations, right now\n        this._lastMutation = Date.now()\n    }\n\n    private _startClickObserver() {\n        addEventListener(assignableWindow, 'click', this._onClick)\n    }\n\n    private _onClick = (event: Event): void => {\n        const click = asClick(event as MouseEvent)\n        if (!isNull(click) && !this._ignoreClick(click)) {\n            this._clicks.push(click)\n        }\n\n        if (this._clicks.length && isUndefined(this._checkClickTimer)) {\n            this._checkClickTimer = assignableWindow.setTimeout(() => {\n                this._checkClicks()\n            }, 1000)\n        }\n    }\n\n    // `capture: true` is required to get scroll events for other scrollable elements\n    // on the page, not just the window\n    // see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#usecapture\n    //\n    // `passive: true` is used to tell the browser that the scroll event handler will not call `preventDefault()`\n    // This allows the browser to optimize scrolling performance by not waiting for our handling of the scroll event\n    // see https://developer.mozilla.org/en-US/docs/Web/API/EventTarget/addEventListener#passive\n    private _startScrollObserver() {\n        addEventListener(assignableWindow, 'scroll', this._onScroll, { capture: true })\n    }\n\n    private _onScroll = (): void => {\n        const candidateNow = Date.now()\n        // very naive throttle\n        if (candidateNow % 50 === 0) {\n            // we can see many scrolls between scheduled checks,\n            // so we update scroll delay as we see them\n            // to avoid false positives\n            this._clicks.forEach((click) => {\n                if (isUndefined(click.scrollDelayMs)) {\n                    click.scrollDelayMs = candidateNow - click.timestamp\n                }\n            })\n        }\n    }\n\n    private _startSelectionChangedObserver() {\n        addEventListener(assignableWindow, 'selectionchange', this._onSelectionChange)\n    }\n\n    private _onSelectionChange = (): void => {\n        this._lastSelectionChanged = Date.now()\n    }\n\n    private _ignoreClick(click: DeadClickCandidate | null): boolean {\n        if (!click) {\n            return true\n        }\n\n        if (isElementInToolbar(click.node)) {\n            return true\n        }\n\n        const alreadyClickedInLastSecond = this._clicks.some((c) => {\n            return c.node === click.node && Math.abs(c.timestamp - click.timestamp) < 1000\n        })\n\n        if (alreadyClickedInLastSecond) {\n            return true\n        }\n\n        if (\n            isTag(click.node, 'html') ||\n            !isElementNode(click.node) ||\n            autocaptureCompatibleElements.includes(click.node.tagName.toLowerCase())\n        ) {\n            return true\n        }\n\n        return false\n    }\n\n    private _checkClicks() {\n        if (!this._clicks.length) {\n            return\n        }\n\n        clearTimeout(this._checkClickTimer)\n        this._checkClickTimer = undefined\n\n        const clicksToCheck = this._clicks\n        this._clicks = []\n\n        for (const click of clicksToCheck) {\n            click.mutationDelayMs =\n                click.mutationDelayMs ??\n                (this._lastMutation && click.timestamp <= this._lastMutation\n                    ? this._lastMutation - click.timestamp\n                    : undefined)\n            click.absoluteDelayMs = Date.now() - click.timestamp\n            click.selectionChangedDelayMs =\n                this._lastSelectionChanged && click.timestamp <= this._lastSelectionChanged\n                    ? this._lastSelectionChanged - click.timestamp\n                    : undefined\n\n            const scrollTimeout = checkTimeout(click.scrollDelayMs, this._config.scroll_threshold_ms)\n            const selectionChangedTimeout = checkTimeout(\n                click.selectionChangedDelayMs,\n                this._config.selection_change_threshold_ms\n            )\n            const mutationTimeout = checkTimeout(click.mutationDelayMs, this._config.mutation_threshold_ms)\n            // we want to timeout eventually even if nothing else catches it...\n            // we leave a little longer than the maximum threshold to give the other checks a chance to catch it\n            const absoluteTimeout = checkTimeout(click.absoluteDelayMs, this._config.mutation_threshold_ms * 1.1)\n\n            const hadScroll = isNumber(click.scrollDelayMs) && click.scrollDelayMs < this._config.scroll_threshold_ms\n            const hadMutation =\n                isNumber(click.mutationDelayMs) && click.mutationDelayMs < this._config.mutation_threshold_ms\n            const hadSelectionChange =\n                isNumber(click.selectionChangedDelayMs) &&\n                click.selectionChangedDelayMs < this._config.selection_change_threshold_ms\n\n            if (hadScroll || hadMutation || hadSelectionChange) {\n                // ignore clicks that had a scroll or mutation\n                continue\n            }\n\n            if (scrollTimeout || mutationTimeout || absoluteTimeout || selectionChangedTimeout) {\n                this._onCapture(click, {\n                    $dead_click_last_mutation_timestamp: this._lastMutation,\n                    $dead_click_event_timestamp: click.timestamp,\n                    $dead_click_scroll_timeout: scrollTimeout,\n                    $dead_click_mutation_timeout: mutationTimeout,\n                    $dead_click_absolute_timeout: absoluteTimeout,\n                    $dead_click_selection_changed_timeout: selectionChangedTimeout,\n                })\n            } else if (click.absoluteDelayMs < this._config.mutation_threshold_ms) {\n                // keep waiting until next check\n                this._clicks.push(click)\n            }\n        }\n\n        if (this._clicks.length && isUndefined(this._checkClickTimer)) {\n            this._checkClickTimer = assignableWindow.setTimeout(() => {\n                this._checkClicks()\n            }, 1000)\n        }\n    }\n\n    private _captureDeadClick(click: DeadClickCandidate, properties: Properties) {\n        // TODO need to check safe and captur-able as with autocapture\n        // TODO autocaputure config\n        this.instance.capture(\n            '$dead_click',\n            {\n                ...properties,\n                ...autocapturePropertiesForElement(click.node, {\n                    e: click.originalEvent,\n                    maskAllElementAttributes: this.instance.config.mask_all_element_attributes,\n                    maskAllText: this.instance.config.mask_all_text,\n                    elementAttributeIgnoreList: this._config.element_attribute_ignorelist,\n                    // TRICKY: it appears that we were moving to elementsChainAsString, but the UI still depends on elements, so :shrug:\n                    elementsChainAsString: false,\n                }).props,\n                $dead_click_scroll_delay_ms: click.scrollDelayMs,\n                $dead_click_mutation_delay_ms: click.mutationDelayMs,\n                $dead_click_absolute_delay_ms: click.absoluteDelayMs,\n                $dead_click_selection_changed_delay_ms: click.selectionChangedDelayMs,\n            },\n            {\n                timestamp: new Date(click.timestamp),\n            }\n        )\n    }\n}\n\nassignableWindow.__PosthogExtensions__ = assignableWindow.__PosthogExtensions__ || {}\nassignableWindow.__PosthogExtensions__.initDeadClicksAutocapture = (ph, config) =>\n    new LazyLoadedDeadClicksAutocapture(ph, config)\n\nexport default LazyLoadedDeadClicksAutocapture\n", "/*\n * Constants\n */\n\n/* PROPERTY KEYS */\n\n// This key is deprecated, but we want to check for it to see whether aliasing is allowed.\nexport const PEOPLE_DISTINCT_ID_KEY = '$people_distinct_id'\nexport const DISTINCT_ID = 'distinct_id'\nexport const ALIAS_ID_KEY = '__alias'\nexport const CAMPAIGN_IDS_KEY = '__cmpns'\nexport const EVENT_TIMERS_KEY = '__timers'\nexport const AUTOCAPTURE_DISABLED_SERVER_SIDE = '$autocapture_disabled_server_side'\nexport const HEATMAPS_ENABLED_SERVER_SIDE = '$heatmaps_enabled_server_side'\nexport const EXCEPTION_CAPTURE_ENABLED_SERVER_SIDE = '$exception_capture_enabled_server_side'\nexport const ERROR_TRACKING_SUPPRESSION_RULES = '$error_tracking_suppression_rules'\nexport const WEB_VITALS_ENABLED_SERVER_SIDE = '$web_vitals_enabled_server_side'\nexport const DEAD_CLICKS_ENABLED_SERVER_SIDE = '$dead_clicks_enabled_server_side'\nexport const WEB_VITALS_ALLOWED_METRICS = '$web_vitals_allowed_metrics'\nexport const SESSION_RECORDING_ENABLED_SERVER_SIDE = '$session_recording_enabled_server_side'\nexport const CONSOLE_LOG_RECORDING_ENABLED_SERVER_SIDE = '$console_log_recording_enabled_server_side'\nexport const SESSION_RECORDING_NETWORK_PAYLOAD_CAPTURE = '$session_recording_network_payload_capture'\nexport const SESSION_RECORDING_MASKING = '$session_recording_masking'\nexport const SESSION_RECORDING_CANVAS_RECORDING = '$session_recording_canvas_recording'\nexport const SESSION_RECORDING_SAMPLE_RATE = '$replay_sample_rate'\nexport const SESSION_RECORDING_MINIMUM_DURATION = '$replay_minimum_duration'\nexport const SESSION_RECORDING_SCRIPT_CONFIG = '$replay_script_config'\nexport const SESSION_ID = '$sesid'\nexport const SESSION_RECORDING_IS_SAMPLED = '$session_is_sampled'\nexport const SESSION_RECORDING_URL_TRIGGER_ACTIVATED_SESSION = '$session_recording_url_trigger_activated_session'\nexport const SESSION_RECORDING_URL_TRIGGER_STATUS = '$session_recording_url_trigger_status'\nexport const SESSION_RECORDING_EVENT_TRIGGER_ACTIVATED_SESSION = '$session_recording_event_trigger_activated_session'\nexport const SESSION_RECORDING_EVENT_TRIGGER_STATUS = '$session_recording_event_trigger_status'\nexport const ENABLED_FEATURE_FLAGS = '$enabled_feature_flags'\nexport const PERSISTENCE_EARLY_ACCESS_FEATURES = '$early_access_features'\nexport const PERSISTENCE_FEATURE_FLAG_DETAILS = '$feature_flag_details'\nexport const STORED_PERSON_PROPERTIES_KEY = '$stored_person_properties'\nexport const STORED_GROUP_PROPERTIES_KEY = '$stored_group_properties'\nexport const SURVEYS = '$surveys'\nexport const SURVEYS_ACTIVATED = '$surveys_activated'\nexport const FLAG_CALL_REPORTED = '$flag_call_reported'\nexport const USER_STATE = '$user_state'\nexport const CLIENT_SESSION_PROPS = '$client_session_props'\nexport const CAPTURE_RATE_LIMIT = '$capture_rate_limit'\n\n/** @deprecated Delete this when INITIAL_PERSON_INFO has been around for long enough to ignore backwards compat */\nexport const INITIAL_CAMPAIGN_PARAMS = '$initial_campaign_params'\n/** @deprecated Delete this when INITIAL_PERSON_INFO has been around for long enough to ignore backwards compat */\nexport const INITIAL_REFERRER_INFO = '$initial_referrer_info'\nexport const INITIAL_PERSON_INFO = '$initial_person_info'\nexport const ENABLE_PERSON_PROCESSING = '$epp'\nexport const TOOLBAR_ID = '__POSTHOG_TOOLBAR__'\nexport const TOOLBAR_CONTAINER_CLASS = 'toolbar-global-fade-container'\n\n/**\n * PREVIEW - MAY CHANGE WITHOUT WARNING - DO NOT USE IN PRODUCTION\n * Sentinel value for distinct id, device id, session id. Signals that the server should generate the value\n * */\nexport const COOKIELESS_SENTINEL_VALUE = '$posthog_cookieless'\nexport const COOKIELESS_MODE_FLAG_PROPERTY = '$cookieless_mode'\n\nexport const WEB_EXPERIMENTS = '$web_experiments'\n\n// These are properties that are reserved and will not be automatically included in events\nexport const PERSISTENCE_RESERVED_PROPERTIES = [\n    PEOPLE_DISTINCT_ID_KEY,\n    ALIAS_ID_KEY,\n    CAMPAIGN_IDS_KEY,\n    EVENT_TIMERS_KEY,\n    SESSION_RECORDING_ENABLED_SERVER_SIDE,\n    HEATMAPS_ENABLED_SERVER_SIDE,\n    SESSION_ID,\n    ENABLED_FEATURE_FLAGS,\n    ERROR_TRACKING_SUPPRESSION_RULES,\n    USER_STATE,\n    PERSISTENCE_EARLY_ACCESS_FEATURES,\n    PERSISTENCE_FEATURE_FLAG_DETAILS,\n    STORED_GROUP_PROPERTIES_KEY,\n    STORED_PERSON_PROPERTIES_KEY,\n    SURVEYS,\n    FLAG_CALL_REPORTED,\n    CLIENT_SESSION_PROPS,\n    CAPTURE_RATE_LIMIT,\n    INITIAL_CAMPAIGN_PARAMS,\n    INITIAL_REFERRER_INFO,\n    ENABLE_PERSON_PROCESSING,\n    INITIAL_PERSON_INFO,\n]\n\nexport const SURVEYS_REQUEST_TIMEOUT_MS = 10000\n"], "names": ["win", "window", "undefined", "global", "globalThis", "nativeForEach", "Array", "prototype", "for<PERSON>ach", "navigator", "document", "location", "fetch", "XMLHttpRequest", "AbortController", "userAgent", "assignableWindow", "includes", "str", "needle", "indexOf", "trim", "nativeIsArray", "isArray", "Obj<PERSON><PERSON><PERSON>", "Object", "hasOwnProperty", "toString", "obj", "call", "isFunction", "x", "isNativeFunction", "isAngularZonePresent", "Zone", "isUndefined", "isString", "isNull", "<PERSON><PERSON><PERSON><PERSON>", "isNumber", "isFormData", "FormData", "_createLogger", "prefix", "logger", "_log", "level", "console", "consoleLog", "_len", "arguments", "length", "args", "_key", "info", "_len2", "_key2", "warn", "_len3", "_key3", "error", "_len4", "_key4", "critical", "_len5", "_key5", "uninitializedWarning", "methodName", "createLogger", "additionalPrefix", "breaker", "eachArray", "iterator", "thisArg", "i", "l", "each", "pair", "entries", "key", "extend", "source", "prop", "ownProps", "keys", "resArray", "addEventListener", "element", "event", "callback", "options", "capture", "passive", "isElementNode", "el", "nodeType", "isTag", "tag", "tagName", "toLowerCase", "splitClassString", "s", "split", "getClassNames", "className", "baseVal", "getAttribute", "getSafeText", "elText", "shouldCaptureElement", "isSensitiveElement", "childNodes", "child", "_makeSafeText", "isTextNode", "textContent", "filter", "shouldCaptureValue", "join", "replace", "substring", "autocaptureCompatibleElements", "curEl", "parentNode", "classes", "type", "name", "id", "test", "coreCCPattern", "anchoredCCRegex", "RegExp", "unanchoredCCRegex", "coreSSNPattern", "anchoredSSNRegex", "unanchoredSSNRegex", "value", "anchorRegexes", "getDirectAndNestedSpanText", "target", "text", "getNestedSpanText", "_child$tagName", "spanText", "e", "getElementsChainString", "elements", "ret", "map", "_element$nth_child", "_element$nth_of_type", "el_string", "tag_name", "attr_class", "single_class", "sort", "attributes", "_extends", "nth_child", "nth_of_type", "href", "attr_id", "sortedAttributes", "_ref", "_ref2", "a", "b", "localeCompare", "_ref3", "escapeQuotes", "_ref4", "elementsToString", "_el$$el_text", "_el$attr__href", "response", "slice", "extractAttrClass", "_ref5", "_ref6", "extractElements", "input", "limitText", "previousElementSibling", "_el", "previousSibling", "getPropertiesFromElement", "elem", "maskAllAttributes", "maskText", "elementAttributeIgnorelist", "props", "c", "attr", "attributeName", "nthChild", "nthOfType", "currentElem", "autocapturePropertiesForElement", "_elementsJson$", "_elementsJson$2", "maskAllElementAttributes", "maskAllText", "elementAttributeIgnoreList", "elementsChainAsString", "targetElementList", "push", "host", "externalHref", "url", "elementsJson", "autocaptureAugmentProperties", "explicitNoCapture", "shouldCaptureEl", "augmentProperties", "propertyKey", "propertyValue", "getAugmentPropertiesFromElement", "_convertToURL", "_window$location", "hrefHost", "createElement", "locationHost", "$event_type", "$ce_version", "$elements", "$elements_chain", "$el_text", "$external_click_url", "cachedImplementations", "getNativeMutationObserverImplementation", "cached", "impl", "bind", "sandbox", "hidden", "head", "append<PERSON><PERSON><PERSON>", "contentWindow", "<PERSON><PERSON><PERSON><PERSON>", "getNativeImplementation", "asClick", "_e$target", "eventTarget", "srcElement", "shadowRoot", "<PERSON><PERSON><PERSON>", "node", "originalEvent", "timestamp", "Date", "now", "checkTimeout", "thresholdMs", "LazyLoadedDeadClicksAutocapture", "_asRequiredConfig", "providedConfig", "_providedConfig$eleme", "_providedConfig$scrol", "_providedConfig$selec", "_providedConfig$mutat", "defaultConfig", "this", "_defaultConfig", "__onCapture", "_captureDeadClick", "element_attribute_ignorelist", "scroll_threshold_ms", "selection_change_threshold_ms", "mutation_threshold_ms", "constructor", "instance", "config", "_clicks", "defaultOnCapture", "_onClick", "click", "_ignoreClick", "_checkClickTimer", "setTimeout", "_checkClicks", "_onScroll", "<PERSON><PERSON><PERSON>", "scrollDelayMs", "_onSelectionChange", "_lastSelectionChanged", "_config", "_onCapture", "start", "<PERSON><PERSON><PERSON><PERSON>", "_startClickObserver", "_startScrollObserver", "_startSelectionChangedObserver", "_startMutationObserver", "_mutationObserver", "NativeMutationObserver", "mutations", "_onMutation", "observe", "characterData", "childList", "subtree", "stop", "_this$_mutationObserv", "disconnect", "removeEventListener", "_mutations", "_lastMutation", "Element", "closest", "some", "Math", "abs", "clearTimeout", "clicksT<PERSON><PERSON><PERSON><PERSON>", "_click$mutationDelayM", "mutationDelayMs", "absoluteDelayMs", "selectionChangedDelayMs", "scrollTimeout", "selectionChangedTimeout", "mutationTimeout", "absoluteTimeout", "hadScroll", "hadMutation", "hadSelectionChange", "$dead_click_last_mutation_timestamp", "$dead_click_event_timestamp", "$dead_click_scroll_timeout", "$dead_click_mutation_timeout", "$dead_click_absolute_timeout", "$dead_click_selection_changed_timeout", "properties", "mask_all_element_attributes", "mask_all_text", "$dead_click_scroll_delay_ms", "$dead_click_mutation_delay_ms", "$dead_click_absolute_delay_ms", "$dead_click_selection_changed_delay_ms", "__PosthogExtensions__", "initDeadClicksAutocapture", "ph"], "mappings": "iPAgBA,IAAMA,EAAkE,oBAAXC,OAAyBA,YAASC,EAmFzFC,EAA8D,oBAAfC,WAA6BA,WAAaJ,EAGlFK,EADaC,MAAMC,UACQC,QAG3BC,EAAYN,MAAAA,OAAAA,EAAAA,EAAQM,UACpBC,EAAWP,MAAAA,OAAAA,EAAAA,EAAQO,SACRP,MAAAA,GAAAA,EAAQQ,SACXR,MAAAA,GAAAA,EAAQS,YAEzBT,GAAAA,EAAQU,gBAAkB,oBAAqB,IAAIV,EAAOU,gBAAmBV,EAAOU,eACzDV,MAAAA,GAAAA,EAAQW,gBACdL,MAAAA,GAAAA,EAAWM,UAC7B,IAAMC,EAAqChB,QAAAA,EAAQ,CAAU,EC/G7D,SAASiB,EAASC,EAAyBC,GAC9C,OAAyC,IAAjCD,EAAYE,QAAQD,EAChC,CAEO,IAAME,EAAO,SAAUH,GAK1B,OAAOA,EAAIG,MACf,ECPMC,EAAgBhB,MAAMiB,QACtBC,EAAWC,OAAOlB,UACXmB,EAAiBF,EAASE,eACjCC,EAAWH,EAASG,SAEbJ,EACTD,GACA,SAAUM,GACN,MAA8B,mBAAvBD,EAASE,KAAKD,EACzB,EAKSE,EAAcC,GAEH,mBAANA,EAGLC,EAAoBD,GAC7BD,EAAWC,KAAiD,IAA3CA,EAAEJ,WAAWP,QAAQ,iBAG7Ba,EAAuBA,MACtBhC,EAAeiC,KAmBhBC,EAAeJ,QAAqC,IAANA,EAE9CK,EAAYL,GAEM,mBAApBJ,EAASE,KAAKE,GAKZM,EAAUN,GAEN,OAANA,EAOEO,EAAaP,GAAsCI,EAAYJ,IAAMM,EAAON,GAE5EQ,EAAYR,GAEM,mBAApBJ,EAASE,KAAKE,GAYZS,EAAcT,GAEhBA,aAAaU,SCtElBC,EAAiBC,IACnB,IAAMC,EAAiB,CACnBC,EAAM,SAACC,GACH,GACI7C,GACiBe,EAA8B,gBAC9CmB,EAAYlC,EAAO8C,UACpB9C,EAAO8C,QACT,CAME,IALA,IAAMC,GACF,uBAAwB/C,EAAO8C,QAAQD,GAChC7C,EAAO8C,QAAQD,GAAmC,mBACnD7C,EAAO8C,QAAQD,IAEzBG,EAAAC,UAAAC,OAZmCC,MAAI9C,MAAA2C,EAAAA,EAAAA,OAAAI,EAAA,EAAAA,EAAAJ,EAAAI,IAAJD,EAAIC,EAAAH,GAAAA,UAAAG,GAavCL,EAAWL,KAAWS,EAC1B,CACH,EAEDE,KAAM,WAAoB,IAAA,IAAAC,EAAAL,UAAAC,OAAhBC,EAAI9C,IAAAA,MAAAiD,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJJ,EAAII,GAAAN,UAAAM,GACVZ,EAAOC,EAAK,SAAUO,EACzB,EAEDK,KAAM,WAAoB,IAAA,IAAAC,EAAAR,UAAAC,OAAhBC,EAAI9C,IAAAA,MAAAoD,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJP,EAAIO,GAAAT,UAAAS,GACVf,EAAOC,EAAK,UAAWO,EAC1B,EAEDQ,MAAO,WAAoB,IAAA,IAAAC,EAAAX,UAAAC,OAAhBC,EAAI9C,IAAAA,MAAAuD,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJV,EAAIU,GAAAZ,UAAAY,GACXlB,EAAOC,EAAK,WAAYO,EAC3B,EAEDW,SAAU,WAAoB,IAAA,IAAAC,EAAAd,UAAAC,OAAhBC,EAAI9C,IAAAA,MAAA0D,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJb,EAAIa,GAAAf,UAAAe,GAGdlB,QAAQa,MAAMjB,KAAWS,EAC5B,EAEDc,qBAAuBC,IACnBvB,EAAOgB,MAAoDO,8CAAAA,EAAa,EAG5EC,aAAeC,GAA6B3B,EAAiBC,MAAU0B,IAE3E,OAAOzB,CAAM,EAGJA,EAASF,EAAc,gBCvD9B4B,EAAmB,CAAE,EAEpB,SAASC,EACZ3C,EACA4C,EACAC,GAEA,GAAIlD,EAAQK,GACR,GAAIvB,GAAiBuB,EAAIpB,UAAYH,EACjCuB,EAAIpB,QAAQgE,EAAUC,QACnB,GAAI,WAAY7C,GAAOA,EAAIuB,UAAYvB,EAAIuB,OAC9C,IAAK,IAAIuB,EAAI,EAAGC,EAAI/C,EAAIuB,OAAQuB,EAAIC,EAAGD,IACnC,GAAIA,KAAK9C,GAAO4C,EAAS3C,KAAK4C,EAAS7C,EAAI8C,GAAIA,KAAOJ,EAClD,MAKpB,CAOO,SAASM,EAAKhD,EAAU4C,EAAoDC,GAC/E,IAAInC,EAAUV,GAAd,CAGA,GAAIL,EAAQK,GACR,OAAO2C,EAAU3C,EAAK4C,EAAUC,GAEpC,GAAIjC,EAAWZ,IACX,IAAK,IAAMiD,KAAQjD,EAAIkD,UACnB,GAAIN,EAAS3C,KAAK4C,EAASI,EAAK,GAAIA,EAAK,MAAQP,EAC7C,YAKZ,IAAK,IAAMS,KAAOnD,EACd,GAAIF,EAAeG,KAAKD,EAAKmD,IACrBP,EAAS3C,KAAK4C,EAAS7C,EAAImD,GAAMA,KAAST,EAC1C,MAfZ,CAmBJ,CAEO,IAAMU,EAAS,SAAUpD,GAA+E,IAAAqB,IAAAA,EAAAC,UAAAC,OAAlDC,MAAI9C,MAAA2C,EAAAA,EAAAA,OAAAI,EAAA,EAAAA,EAAAJ,EAAAI,IAAJD,EAAIC,EAAAH,GAAAA,UAAAG,GAQ7D,OAPAkB,EAAUnB,GAAM,SAAU6B,GACtB,IAAK,IAAMC,KAAQD,OACM,IAAjBA,EAAOC,KACPtD,EAAIsD,GAAQD,EAAOC,GAG/B,IACOtD,CACX,EAmCO,SAASkD,EAAiBlD,GAK7B,IAJA,IAAMuD,EAAW1D,OAAO2D,KAAKxD,GACzB8C,EAAIS,EAAShC,OACXkC,EAAW,IAAI/E,MAAMoE,GAEpBA,KACHW,EAASX,GAAK,CAACS,EAAST,GAAI9C,EAAIuD,EAAST,KAE7C,OAAOW,CACX,CAkIO,SAASC,EACZC,EACAC,EACAC,EACAC,GAEA,IAAMC,QAAEA,GAAU,EAAKC,QAAEA,GAAU,GAASF,QAAAA,EAAW,CAAE,EAKlD,MAAPH,GAAAA,EAASD,iBAAiBE,EAAOC,EAAU,CAAEE,UAASC,WAC1D,CC1OO,SAASC,EAAcC,GAC1B,QAASA,GAAsB,IAAhBA,EAAGC,QACtB,CAYO,SAASC,EAAMF,EAAgCG,GAClD,QAASH,KAAQA,EAAGI,SAAWJ,EAAGI,QAAQC,gBAAkBF,EAAIE,aACpE,CCtBO,SAASC,EAAiBC,GAC7B,OAAOA,EAAIhF,EAAKgF,GAAGC,MAAM,OAAS,EACtC,CAaO,SAASC,EAAcT,GAC1B,IAAIU,EAAY,GAChB,cAAeV,EAAGU,WACd,IAAK,SACDA,EAAYV,EAAGU,UACf,MAEJ,IAAK,SACDA,GACKV,EAAGU,WAAa,YAAaV,EAAGU,UAAaV,EAAGU,UAAkBC,QAAU,OAC7EX,EAAGY,aAAa,UAChB,GACJ,MACJ,QACIF,EAAY,GAGpB,OAAOJ,EAAiBI,EAC5B,CA8BO,SAASG,EAAYb,GACxB,IAAIc,EAAS,GAUb,OARIC,EAAqBf,KAAQgB,EAAmBhB,IAAOA,EAAGiB,YAAcjB,EAAGiB,WAAW5D,QACtFyB,EAAKkB,EAAGiB,YAAY,SAAUC,GACkB,IAAAC,EAjC3BZ,GDNtB,SAAoBP,GACvB,QAASA,GAAsB,IAAhBA,EAAGC,QACtB,ECqCgBmB,CAAWF,IAAUA,EAAMG,cAC3BP,GAAyCK,QAlC5BZ,EAkCUW,EAAMG,YAAvBF,EAjCd3E,EAAU+D,GACH,KAIPhF,EAAKgF,GAEAC,MAAM,SACNc,QAAQf,GAAMgB,EAAmBhB,KACjCiB,KAAK,IAELC,QAAQ,UAAW,KACnBA,QAAQ,QAAS,KAEjBC,UAAU,EAAG,YAmB+BP,IAAAA,EAAAA,EAAI,GAErD,IAGG5F,EAAKuF,EAChB,CAcO,IAAMa,EAAgC,CAAC,IAAK,SAAU,OAAQ,QAAS,SAAU,WAAY,SAyK7F,SAASZ,EAAqBf,GACjC,IAAK,IAAI4B,EAAQ5B,EAAI4B,EAAMC,aAAe3B,EAAM0B,EAAO,QAASA,EAAQA,EAAMC,WAAuB,CACjG,IAAMC,EAAUrB,EAAcmB,GAC9B,GAAIzG,EAAS2G,EAAS,iBAAmB3G,EAAS2G,EAAS,iBACvD,OAAO,CAEf,CAEA,GAAI3G,EAASsF,EAAcT,GAAK,cAC5B,OAAO,EAIX,IAAM+B,EAAQ/B,EAAwB+B,MAAQ,GAC9C,GAAIzF,EAASyF,GAET,OAAQA,EAAK1B,eACT,IAAK,SAEL,IAAK,WACD,OAAO,EAKnB,IAAM2B,EAAQhC,EAAwBgC,MAAQhC,EAAGiC,IAAM,GAIvD,GAAI3F,EAAS0F,GAAO,CAIhB,GADI,uHACmBE,KAAKF,EAAKP,QAAQ,gBAAiB,KACtD,OAAO,CAEf,CAEA,OAAO,CACX,CAOO,SAAST,EAAmBhB,GAI/B,SACKE,EAAMF,EAAI,WAFW,CAAC,SAAU,WAAY,SAAU,SAEb7E,SAAU6E,EAAwB+B,OAC5E7B,EAAMF,EAAI,WACVE,EAAMF,EAAI,aAC6B,SAAvCA,EAAGY,aAAa,mBAKxB,CAGA,IAAMuB,EAAiL,kKAEjLC,EAAkB,IAAIC,OAAcF,OAAAA,QAEpCG,EAAoB,IAAID,OAAOF,GAG/BI,EAAyC,yBAEzCC,EAAmB,IAAIH,OAAYE,KAAAA,QAEnCE,EAAqB,IAAIJ,OAAWE,IAAAA,OASnC,SAAShB,EAAmBmB,EAAeC,GAC9C,QAD2D,IAAbA,IAAAA,GAAgB,GAC1DnG,EAAUkG,GACV,OAAO,EAGX,GAAIpG,EAASoG,GAAQ,CAMjB,GALAA,EAAQnH,EAAKmH,IAIGC,EAAgBP,EAAkBE,GACtCJ,MAAMQ,GAAS,IAAIjB,QAAQ,QAAS,KAC5C,OAAO,EAKX,IADiBkB,EAAgBH,EAAmBC,GACvCP,KAAKQ,GACd,OAAO,CAEf,CAEA,OAAO,CACX,CAuBO,SAASE,EAA2BC,GACvC,IAAIC,EAAOjC,EAAYgC,GAEvB,OAAOtB,EADPuB,GAAUA,EAAQC,IAAAA,EAAkBF,IAAUtH,QACZuH,EAAO,EAC7C,CAQO,SAASC,EAAkBF,GAC9B,IAAIC,EAAO,GAiBX,OAhBID,GAAUA,EAAO5B,YAAc4B,EAAO5B,WAAW5D,QACjDyB,EAAK+D,EAAO5B,YAAY,SAAUC,GAAO,IAAA8B,EACrC,GAAI9B,GAA0C,UAAjC8B,OAAAA,EAAA9B,EAAMd,cAAN4C,EAAAA,EAAe3C,eACxB,IACI,IAAM4C,EAAWpC,EAAYK,GAC7B4B,GAAUA,EAAI,IAAIG,GAAW1H,OAEzB2F,EAAMD,YAAcC,EAAMD,WAAW5D,SACrCyF,GAAUA,EAAQC,IAAAA,EAAkB7B,IAAS3F,OAEpD,CAAC,MAAO2H,GACLpG,EAAOgB,MAAM,gBAAiBoF,EAClC,CAER,IAEGJ,CACX,CAQO,SAASK,EAAuBC,GACnC,OAuBJ,SAA0BA,GACtB,IAAMC,EAAMD,EAASE,KAAK7D,IAAY,IAAA8D,EAAAC,EAC9BC,EAAY,GAIhB,GAHIhE,EAAQiE,WACRD,GAAahE,EAAQiE,UAErBjE,EAAQkE,WAER,IAAK,IAAMC,KADXnE,EAAQkE,WAAWE,OACQpE,EAAQkE,YAC/BF,GAAS,IAAQG,EAAanC,QAAQ,KAAM,IAGpD,IAAMqC,EAA+BC,KAC7BtE,EAAQqD,KAAO,CAAEA,KAAMrD,EAAQqD,MAAS,GAAE,CAC9C,YAA8B,QAAnBS,EAAE9D,EAAQuE,iBAAS,IAAAT,EAAAA,EAAI,EAClC,cAAkCC,QAArBA,EAAE/D,EAAQwE,mBAAWT,IAAAA,EAAAA,EAAI,GAClC/D,EAAQyE,KAAO,CAAEA,KAAMzE,EAAQyE,MAAS,CAAE,EAC1CzE,EAAQ0E,QAAU,CAAEA,QAAS1E,EAAQ0E,SAAY,CAAE,EACpD1E,EAAQqE,YAETM,EAAwC,CAAE,EAUhD,OATApF,EAAQ8E,GACHD,MAAK,CAAAQ,EAAAC,KAAA,IAAEC,GAAEF,GAAGG,GAAEF,EAAA,OAAKC,EAAEE,cAAcD,EAAE,IACrC9J,SACGgK,IAAA,IAAEzF,EAAKyD,GAAMgC,EAAA,OAAMN,EAAiBO,EAAa1F,EAAIpD,aAAe8I,EAAajC,EAAM7G,WAAW,IAE1G4H,GAAa,IACbA,GAAazE,EAAQoF,GAChBd,KAAIsB,IAAA,IAAE3F,EAAKyD,GAAMkC,EAAA,OAAQ3F,OAAQyD,EAAK,GAAA,IACtClB,KAAK,GACM,IAEpB,OAAO6B,EAAI7B,KAAK,IACpB,CAxDWqD,CA0DX,SAAyBzB,GACrB,OAAOA,EAASE,KAAKtD,IAAO,IAAA8E,EAAAC,EAClBC,EAAW,CACblC,KAAMgC,OAAFA,EAAE9E,EAAa,eAAb8E,EAAAA,EAAgBG,MAAM,EAAG,KAC/BvB,SAAU1D,EAAa,SACvBkE,KAAMa,OAAFA,EAAE/E,EAAe,iBAAf+E,EAAAA,EAAkBE,MAAM,EAAG,MACjCtB,WAAYuB,EAAiBlF,GAC7BmE,QAASnE,EAAa,SACtBgE,UAAWhE,EAAc,UACzBiE,YAAajE,EAAgB,YAC7B8D,WAAY,CAAA,GAMhB,OAHA9E,EAAQgB,GACHsB,QAAO6D,IAAA,IAAElG,GAAIkG,EAAA,OAA+B,IAA1BlG,EAAI3D,QAAQ,SAAe,IAC7CZ,SAAQ0K,IAAA,IAAEnG,EAAKyD,GAAM0C,EAAA,OAAMJ,EAASlB,WAAW7E,GAAOyD,CAAK,IACzDsC,CAAQ,GAEvB,CA5E4BK,CAAgBjC,GAC5C,CAkBA,SAASuB,EAAaW,GAClB,OAAOA,EAAM7D,QAAQ,SAAU,MACnC,CAyDA,SAASyD,EAAiBlF,GACtB,IAAM2D,EAAa3D,EAAgB,YACnC,OAAK2D,EAEMlI,EAAQkI,GACRA,EAEArD,EAAiBqD,QAJxB,CAMR,CC5eA,SAAS4B,EAAUlI,EAAgByF,GAC/B,OAAIA,EAAKzF,OAASA,EACPyF,EAAKmC,MAAM,EAAG5H,GAAU,MAE5ByF,CACX,CAuBO,SAAS0C,EAAuBxF,GACnC,GAAIA,EAAGwF,uBACH,OAAOxF,EAAGwF,uBAEd,IAAIC,EAAsBzF,EAC1B,GACIyF,EAAMA,EAAIC,sBACLD,IAAQ1F,EAAc0F,IAC/B,OAAOA,CACX,CASO,SAASE,EACZC,EACAC,EACAC,EACAC,GAEA,IAAMrC,EAAWkC,EAAKxF,QAAQC,cACxB2F,EAAoB,CACtBtC,SAAUA,GAEV/B,EAA8BrG,QAAQoI,IAAa,IAAMoC,IAC1B,MAA3BpC,EAASrD,eAAoD,WAA3BqD,EAASrD,cAC3C2F,EAAgB,SAAIT,EAAU,KAAM3C,EAA2BgD,IAE/DI,EAAgB,SAAIT,EAAU,KAAM1E,EAAY+E,KAIxD,IAAM9D,EAAUrB,EAAcmF,GAC1B9D,EAAQzE,OAAS,IACjB2I,EAAe,QAAIlE,EAAQR,QAAO,SAAU2E,GACxC,MAAa,KAANA,CACX,KAGJnH,EAAK8G,EAAK9B,YAAY,SAAUoC,GDwR7B,IAA4BC,ECtR3B,KAAInF,EAAmB4E,KAAuE,IAA9D,CAAC,OAAQ,KAAM,QAAS,cAActK,QAAQ4K,EAAKlE,UAE/E+D,MAAAA,IAAAA,EAA4B5K,SAAS+K,EAAKlE,SAEzC6D,GAAqBtE,EAAmB2E,EAAKxD,SDkRvByD,EClRqDD,EAAKlE,MDmRrF1F,EAAS6J,IACiC,eAAnCA,EAAczE,UAAU,EAAG,KAA0D,YAAlCyE,EAAczE,UAAU,EAAG,KCpRO,CACxF,IAAIgB,EAAQwD,EAAKxD,MACC,UAAdwD,EAAKlE,OAILU,EAAQpC,EAAiBoC,GAAOlB,KAAK,MAEzCwE,EAAM,SAAWE,EAAKlE,MAAQuD,EAAU,KAAM7C,EAClD,CACJ,IAKA,IAHA,IAAI0D,EAAW,EACXC,EAAY,EACZC,EAA8BV,EAC1BU,EAAcd,EAAuBc,IAEzCF,IACIE,EAAYlG,UAAYwF,EAAKxF,SAC7BiG,IAMR,OAHAL,EAAiB,UAAII,EACrBJ,EAAmB,YAAIK,EAEhBL,CACX,CAEO,SAASO,GACZ1D,EAAewB,GAiBf,IAHkD,IAAAmC,EAAAC,EFvGnBzG,GE0F/BkD,EACIA,EAACwD,yBACDA,EAAwBC,YACxBA,EAAWC,2BACXA,EAA0BC,sBAC1BA,GAOHxC,EAEKyC,EAAoB,CAACjE,GACvBjB,EAAQiB,EACLjB,EAAMC,aAAe3B,EAAM0B,EAAO,UF1GV5B,EE2GJ4B,EAAMC,aF1GF,KAAhB7B,EAAGC,UE2GV6G,EAAkBC,KAAMnF,EAAMC,WAAmBmF,MACjDpF,EAASA,EAAMC,WAAmBmF,OAGtCF,EAAkBC,KAAKnF,EAAMC,YAC7BD,EAAQA,EAAMC,YAGlB,IA2CIoF,EChMqBC,EACnBrM,EDoJAsM,EAA6B,GAC7BC,EAA2C,CAAE,EAC/ClD,GAAuB,EACvBmD,GAAoB,EA0BxB,GAxBAvI,EAAKgI,GAAoB9G,IACrB,IAAMsH,EAAkBvG,EAAqBf,GAIZ,MAA7BA,EAAGI,QAAQC,gBACX6D,EAAOlE,EAAGY,aAAa,QACvBsD,EAAOoD,GAAmBpD,GAAQ3C,EAAmB2C,IAASA,GAK9D/I,EADYsF,EAAcT,GACR,mBAClBqH,GAAoB,GAGxBF,EAAaJ,KACTpB,EAAyB3F,EAAI0G,EAA0BC,EAAaC,IAGxE,IAAMW,EAvJP,SAAyC3B,GAE5C,IADwB7E,EAAqB6E,GAEzC,MAAO,CAAE,EAGb,IAAMI,EAAoB,CAAE,EAY5B,OAVAlH,EAAK8G,EAAK9B,YAAY,SAAUoC,GAC5B,GAAIA,EAAKlE,MAA2D,IAAnDkE,EAAKlE,KAAK1G,QAAQ,6BAAoC,CACnE,IAAMkM,EAActB,EAAKlE,KAAKP,QAAQ,6BAA8B,IAC9DgG,EAAgBvB,EAAKxD,MACvB8E,GAAeC,GAAiBlG,EAAmBkG,KACnDzB,EAAMwB,GAAeC,EAE7B,CACJ,IAEOzB,CACX,CAoIkC0B,CAAgC1H,GAC1Dd,EAAOkI,EAA8BG,EAAkB,IAGvDF,EACA,MAAO,CAAErB,MAAO,CAAE,EAAEqB,qBAcxB,GAXKV,IAGoC,MAAjC9D,EAAOzC,QAAQC,eAA0D,WAAjCwC,EAAOzC,QAAQC,cACvD8G,EAAa,GAAa,SAAIvE,EAA2BC,GAEzDsE,EAAa,GAAa,SAAItG,EAAYgC,IAK9CqB,EAAM,CAAA,IAAAyD,EAAAC,EACNT,EAAa,GAAe,WAAIjD,EAChC,IAAM2D,EAA6B,OCnMdX,EDmMShD,EClM5BrJ,EAAmB,MAARD,OAAQ,EAARA,EAAUkN,cAAc,KDkMvBH,ECjMdtL,EAAYxB,GACL,MAGXA,EAASqJ,KAAOgD,EACTrM,SD4LgC,EAAlB8M,EAAoBX,KAC/Be,EAAqB,MAAN5N,GAAgB,OAAVyN,EAANzN,EAAQU,eAAQ,EAAhB+M,EAAkBZ,KACnCa,GAAYE,GAAgBF,IAAaE,IACzCd,EAAe/C,EAEvB,CAaA,MAAO,CAAE8B,MAXK9G,EAlJP,CACH8I,YAkJqB9E,EAAEnB,KAjJvBkG,YAAa,GAmJZpB,EAAsD,CAAE,EAAhC,CAAEqB,UAAWf,GAEtC,CAAEgB,gBAAiBhF,EAAuBgE,IAC1CX,OAAAA,EAAAW,EAAa,KAAbX,EAA4B,SAAI,CAAE4B,SAAyB,OAAjB3B,EAAEU,EAAa,SAAE,EAAfV,EAA4B,UAAM,CAAE,EAChFQ,GAA2B,UAAX/D,EAAEnB,KAAmB,CAAEsG,oBAAqBpB,GAAiB,CAAA,EAC7EG,GAIR,CEtNA,IAAMkB,GAA6D,CAAE,EA2C9D,SAASC,GAAwCrN,GACpD,OA1CG,SACH8G,EACA9G,GAEA,IAAMsN,EAASF,GAAsBtG,GACrC,GAAIwG,EACA,OAAOA,EAGX,IAAIC,EAAOvN,EAAiB8G,GAE5B,GAAI9F,EAAiBuM,KAAUtM,IAC3B,OAAQmM,GAAsBtG,GAAQyG,EAAKC,KAAKxN,GAGpD,IAAMN,EAAWM,EAAiBN,SAClC,GAAIA,GAAYoB,EAAWpB,EAASkN,eAChC,IACI,IAAMa,EAAU/N,EAASkN,cAAc,UACvCa,EAAQC,QAAS,EACjBhO,EAASiO,KAAKC,YAAYH,GAC1B,IAAMI,EAAgBJ,EAAQI,cAC1BA,GAAkBA,EAAsB/G,KACxCyG,EAAQM,EAAsB/G,IAElCpH,EAASiO,KAAKG,YAAYL,EAC7B,CAAC,MAAOzF,GAELpG,EAAOa,KAA4CqE,uCAAAA,yCAA2CA,EAAI,KAAMkB,EAC5G,CAKJ,OAAKuF,GAASzM,EAAWyM,GAIjBH,GAAsBtG,GAAQyG,EAAKC,KAAKxN,GAHrCuN,CAIf,CAGWQ,CAAwB,mBAAoB/N,EACvD,CCjDA,SAASgO,GAAQxJ,GACb,IJ2E2BwD,EAIpBiG,EI/EDC,EJ6EF/M,GAFuB6G,EI3EQxD,GJ6EjBmD,QACNK,EAAEmG,YAA0B,KAEhCF,OAAJA,EAAKjG,EAAEL,SAAHsG,EAA2BG,WACnBpG,EAAEqG,eAAe,IAAkB,KAEvCrG,EAAEL,QAAsB,KIlFpC,OAAIuG,EACO,CACHI,KAAMJ,EACNK,cAAe/J,EACfgK,UAAWC,KAAKC,OAGjB,IACX,CAEA,SAASC,GAAanH,EAA2BoH,GAC7C,OAAOrN,EAASiG,IAAUA,GAASoH,CACvC,CAEA,MAAMC,GAiBMC,CAAAA,CAAkBC,GAAqF,IAAAC,EAAAC,EAAAC,EAAAC,EACrGC,EAAgBC,KAAKC,GAAeP,MAAAA,OAAAA,EAAAA,EAAgBQ,cAAeF,KAAKG,EAAkBhC,KAAK6B,OACrG,MAAO,CACHI,6BACgD,QADpBT,QACxBD,SAAAA,EAAgBU,oCAA4BT,IAAAA,EAAAA,EAAII,EAAcK,6BAClEC,oBAAwD,QAArCT,QAAEF,SAAAA,EAAgBW,2BAAmBT,IAAAA,EAAAA,EAAIG,EAAcM,oBAC1EC,8BACiD,QADpBT,QACzBH,SAAAA,EAAgBY,qCAA6BT,IAAAA,EAAAA,EAAIE,EAAcO,8BACnEC,sBAA4D,QAAvCT,QAAEJ,SAAAA,EAAgBa,6BAAqBT,IAAAA,EAAAA,EAAIC,EAAcQ,sBAC9EL,YAAaH,EAAcG,YAEnC,CAEAM,WAAAA,CACaC,EACTC,GACFV,KA7BMW,EAAgC,GAAEX,KAKlCC,EAAkBW,IAAmF,CACzGR,6BAA8B,GAC9BC,oBAAqB,IACrBC,8BAA+B,IAC/BC,sBAAuB,KACvBL,YAAaU,IACfZ,KA+DMa,EAAY1L,IAChB,IAAM2L,EAAQnC,GAAQxJ,GACjBnD,EAAO8O,IAAWd,KAAKe,EAAaD,IACrCd,KAAKW,EAAQnE,KAAKsE,GAGlBd,KAAKW,EAAQ7N,QAAUhB,EAAYkO,KAAKgB,KACxChB,KAAKgB,EAAmBrQ,EAAiBsQ,YAAW,KAChDjB,KAAKkB,GAAc,GACpB,KACP,EACHlB,KAaOmB,EAAY,KAChB,IAAMC,EAAehC,KAAKC,MAEtB+B,EAAe,IAAO,GAItBpB,KAAKW,EAAQxQ,SAAS2Q,IACdhP,EAAYgP,EAAMO,iBAClBP,EAAMO,cAAgBD,EAAeN,EAAM3B,UAC/C,GAER,EACHa,KAMOsB,EAAqB,KACzBtB,KAAKuB,EAAwBnC,KAAKC,KAAK,EAC1CW,KA5FYS,SAAAA,EAGTT,KAAKwB,EAAUxB,KAAKP,EAAkBiB,GACtCV,KAAKyB,WAAazB,KAAKwB,EAAQtB,WACnC,CAEAwB,KAAAA,CAAMC,GACF3B,KAAK4B,IACL5B,KAAK6B,IACL7B,KAAK8B,IACL9B,KAAK+B,EAAuBJ,EAChC,CAEQI,CAAAA,CAAuBJ,GAC3B,IAAK3B,KAAKgC,EAAmB,CACzB,IAAMC,EAAyBjE,GAAwCrN,GACvEqP,KAAKgC,EAAoB,IAAIC,GAAwBC,IACjDlC,KAAKmC,EAAYD,EAAU,IAE/BlC,KAAKgC,EAAkBI,QAAQT,EAAgB,CAC3CpI,YAAY,EACZ8I,eAAe,EACfC,WAAW,EACXC,SAAS,GAEjB,CACJ,CAEAC,IAAAA,GAAO,IAAAC,SACHA,OAAKT,IAALS,EAAwBC,aACxB1C,KAAKgC,OAAoBnS,EACzBc,EAAiBgS,oBAAoB,QAAS3C,KAAKa,GACnDlQ,EAAiBgS,oBAAoB,SAAU3C,KAAKmB,EAAW,CAAE7L,SAAS,IAC1E3E,EAAiBgS,oBAAoB,kBAAmB3C,KAAKsB,EACjE,CAGQa,CAAAA,CAAYS,GAEhB5C,KAAK6C,EAAgBzD,KAAKC,KAC9B,CAEQuC,CAAAA,GACJ3M,EAAiBtE,EAAkB,QAASqP,KAAKa,EACrD,CAsBQgB,CAAAA,GACJ5M,EAAiBtE,EAAkB,SAAUqP,KAAKmB,EAAW,CAAE7L,SAAS,GAC5E,CAiBQwM,CAAAA,GACJ7M,EAAiBtE,EAAkB,kBAAmBqP,KAAKsB,EAC/D,CAMQP,CAAAA,CAAaD,GACjB,OAAKA,QLtJsBrL,EK0JJqL,EAAM7B,gBLzJf6D,UMgDI,wBN9CXrN,EAAGiC,IAAiC,MAAVjC,EAAGsN,SAAHtN,EAAGsN,QAAU,wCK2JX/C,KAAKW,EAAQqC,MAAMtH,GAC3CA,EAAEuD,OAAS6B,EAAM7B,MAAQgE,KAAKC,IAAIxH,EAAEyD,UAAY2B,EAAM3B,WAAa,UAQ1ExJ,EAAMmL,EAAM7B,KAAM,SACjBzJ,EAAcsL,EAAM7B,QACrB7H,EAA8BxG,SAASkQ,EAAM7B,KAAKpJ,QAAQC,kBLzK/D,IAA4BL,CK+K/B,CAEQyL,CAAAA,GACJ,GAAKlB,KAAKW,EAAQ7N,OAAlB,CAIAqQ,aAAanD,KAAKgB,GAClBhB,KAAKgB,OAAmBnR,EAExB,IAAMuT,EAAgBpD,KAAKW,EAG3B,IAAK,IAAMG,KAFXd,KAAKW,EAAU,GAEKyC,GAAe,CAAA,IAAAC,EAC/BvC,EAAMwC,gBACmB,QADJD,EACjBvC,EAAMwC,uBAAe,IAAAD,EAAAA,EACpBrD,KAAK6C,GAAiB/B,EAAM3B,WAAaa,KAAK6C,EACzC7C,KAAK6C,EAAgB/B,EAAM3B,eAC3BtP,EACViR,EAAMyC,gBAAkBnE,KAAKC,MAAQyB,EAAM3B,UAC3C2B,EAAM0C,wBACFxD,KAAKuB,GAAyBT,EAAM3B,WAAaa,KAAKuB,EAChDvB,KAAKuB,EAAwBT,EAAM3B,eACnCtP,EAEV,IAAM4T,EAAgBnE,GAAawB,EAAMO,cAAerB,KAAKwB,EAAQnB,qBAC/DqD,EAA0BpE,GAC5BwB,EAAM0C,wBACNxD,KAAKwB,EAAQlB,+BAEXqD,EAAkBrE,GAAawB,EAAMwC,gBAAiBtD,KAAKwB,EAAQjB,uBAGnEqD,EAAkBtE,GAAawB,EAAMyC,gBAAsD,IAArCvD,KAAKwB,EAAQjB,uBAEnEsD,EAAY3R,EAAS4O,EAAMO,gBAAkBP,EAAMO,cAAgBrB,KAAKwB,EAAQnB,oBAChFyD,EACF5R,EAAS4O,EAAMwC,kBAAoBxC,EAAMwC,gBAAkBtD,KAAKwB,EAAQjB,sBACtEwD,EACF7R,EAAS4O,EAAM0C,0BACf1C,EAAM0C,wBAA0BxD,KAAKwB,EAAQlB,8BAE7CuD,GAAaC,GAAeC,IAK5BN,GAAiBE,GAAmBC,GAAmBF,EACvD1D,KAAKyB,WAAWX,EAAO,CACnBkD,oCAAqChE,KAAK6C,EAC1CoB,4BAA6BnD,EAAM3B,UACnC+E,2BAA4BT,EAC5BU,6BAA8BR,EAC9BS,6BAA8BR,EAC9BS,sCAAuCX,IAEpC5C,EAAMyC,gBAAkBvD,KAAKwB,EAAQjB,uBAE5CP,KAAKW,EAAQnE,KAAKsE,GAE1B,CAEId,KAAKW,EAAQ7N,QAAUhB,EAAYkO,KAAKgB,KACxChB,KAAKgB,EAAmBrQ,EAAiBsQ,YAAW,KAChDjB,KAAKkB,GAAc,GACpB,KA5DP,CA8DJ,CAEQf,CAAAA,CAAkBW,EAA2BwD,GAGjDtE,KAAKS,SAASnL,QACV,cAAakE,EAEN8K,CAAAA,EAAAA,EACAtI,GAAgC8E,EAAM7B,KAAM,CAC3CtG,EAAGmI,EAAM5B,cACT/C,yBAA0B6D,KAAKS,SAASC,OAAO6D,4BAC/CnI,YAAa4D,KAAKS,SAASC,OAAO8D,cAClCnI,2BAA4B2D,KAAKwB,EAAQpB,6BAEzC9D,uBAAuB,IACxBb,MAAK,CACRgJ,4BAA6B3D,EAAMO,cACnCqD,8BAA+B5D,EAAMwC,gBACrCqB,8BAA+B7D,EAAMyC,gBACrCqB,uCAAwC9D,EAAM0C,0BAElD,CACIrE,UAAW,IAAIC,KAAK0B,EAAM3B,YAGtC,EAGJxO,EAAiBkU,sBAAwBlU,EAAiBkU,uBAAyB,CAAE,EACrFlU,EAAiBkU,sBAAsBC,0BAA4B,CAACC,EAAIrE,IACpE,IAAIlB,GAAgCuF,EAAIrE"}