// 检查当前URL是否匹配目标页面
function checkUrl() {
  const identifierUrl = 'login.augmentcode.com/u/login/identifier';
  const challengeUrl = 'login.augmentcode.com/u/login/challenge';
  const currentUrl = window.location.href;

  // 检查URL是否匹配登录页面
  if (currentUrl.includes(identifierUrl)) {
    console.log('Augment续杯: 检测到登录页面');
    // 检查续杯按钮是否已存在
    if (!document.querySelector('.refill-button-added')) {
      addRefillButton();
    }
  }
  // 检查URL是否匹配验证码页面
  else if (currentUrl.includes(challengeUrl)) {
    console.log('Augment续杯: 检测到验证码页面');
    // 检查验证码自动填入是否已启动
    if (!document.querySelector('.verification-handler-added')) {
      addVerificationHandler();
    }
  }
}

// 添加续杯按钮
function addRefillButton() {
  // 等待原始按钮加载
  const checkExist = setInterval(() => {
    const originalButton = document.querySelector('button[name="action"][value="default"]');

    if (originalButton && !document.querySelector('.refill-button-added')) {
      clearInterval(checkExist);

      // 创建续杯按钮
      const refillButton = document.createElement('button');
      refillButton.type = 'button';
      refillButton.textContent = '续杯';
      refillButton.className = 'refill-button-added'; // 添加特殊类名用于检测

      // 复制原始按钮的样式类
      originalButton.classList.forEach(className => {
        refillButton.classList.add(className);
      });

      // 添加点击事件
      refillButton.addEventListener('click', handleRefill);

      // 将按钮插入到原始按钮后面
      originalButton.parentNode.insertBefore(refillButton, originalButton.nextSibling);
      // 设置标志，表示按钮已添加
      buttonAdded = true;
      // 停止观察DOM变化
      observer.disconnect();
      console.log('Augment续杯: 续杯按钮已添加');
    }
  }, 500);
}

// 处理续杯按钮点击
function handleRefill() {
  // 生成随机邮箱（现在返回Promise）
  generateRandomEmail().then(randomEmail => {
    console.log('Augment续杯: 生成随机邮箱', randomEmail);

    // 填入邮箱输入框
    const emailInput = document.querySelector('input[name="username"]');
    if (emailInput) {
      emailInput.value = randomEmail;
      // 触发input事件，确保表单验证能够识别值的变化
      const inputEvent = new Event('input', { bubbles: true });
      emailInput.dispatchEvent(inputEvent);

      // 自动点击原始按钮，延迟1秒以确保表单验证有足够时间处理
      setTimeout(() => {
        const originalButton = document.querySelector('button[name="action"][value="default"]');
        if (originalButton) {
          originalButton.click();
          console.log('Augment续杯: 自动点击继续按钮');
        }
      }, 1000);
    }
  }).catch(error => {
    console.error('Augment续杯: 生成邮箱时出错', error);
  });
}

// 生成随机邮箱
function generateRandomEmail() {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  const charactersLength = characters.length;

  // 从存储中获取邮箱后缀和随机字符串位数，如果没有则使用默认值
  return new Promise((resolve, reject) => {
    chrome.storage.sync.get(['emailDomain', 'randomLength'], function(data) {
      // 检查是否设置了邮箱后缀
      if (!data.emailDomain) {
        // 如果没有设置邮箱后缀，则提示用户
        alert('请先在扩展设置中设置邮箱后缀！');
        reject(new Error('未设置邮箱后缀'));
        return;
      }

      const domain = data.emailDomain;
      // 使用设置的位数，默认为12位
      const length = data.randomLength ? parseInt(data.randomLength) : 12;

      // 生成随机字符串
      for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
      }

      const email = result + '@' + domain;

      // 保存生成的邮箱地址，供验证码获取使用
      chrome.storage.local.set({ currentEmail: email });

      resolve(email);
    });
  });
}

// 添加验证码处理器
function addVerificationHandler() {
  console.log('Augment续杯: 开始设置验证码自动填入');

  // 添加标记，防止重复添加
  const marker = document.createElement('div');
  marker.className = 'verification-handler-added';
  marker.style.display = 'none';
  document.body.appendChild(marker);

  // 等待验证码输入框出现
  const checkVerificationInput = setInterval(() => {
    const verificationInput = document.querySelector('input[name="passcode"]') ||
                             document.querySelector('input[type="text"][placeholder*="code"]') ||
                             document.querySelector('input[type="text"][placeholder*="验证码"]');

    if (verificationInput) {
      clearInterval(checkVerificationInput);
      console.log('Augment续杯: 找到验证码输入框，开始获取验证码');

      // 显示状态提示
      showVerificationStatus('正在获取验证码...');

      // 开始获取验证码
      startVerificationCodeRetrieval(verificationInput);
    }
  }, 1000);

  // 30秒后停止检查
  setTimeout(() => {
    clearInterval(checkVerificationInput);
  }, 30000);
}

// 显示验证码获取状态
function showVerificationStatus(message) {
  // 移除之前的状态提示
  const existingStatus = document.querySelector('.verification-status');
  if (existingStatus) {
    existingStatus.remove();
  }

  // 创建状态提示元素
  const statusDiv = document.createElement('div');
  statusDiv.className = 'verification-status';
  statusDiv.textContent = message;
  statusDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #4285f4;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    z-index: 10000;
    font-size: 14px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  `;

  document.body.appendChild(statusDiv);

  // 5秒后自动移除
  setTimeout(() => {
    if (statusDiv.parentNode) {
      statusDiv.remove();
    }
  }, 5000);
}

// 开始验证码获取流程
async function startVerificationCodeRetrieval(inputElement) {
  try {
    // 获取配置信息
    const config = await new Promise((resolve) => {
      chrome.storage.sync.get(['emailService', 'apiKey', 'pinCode'], resolve);
    });

    // 获取当前邮箱地址
    const emailData = await new Promise((resolve) => {
      chrome.storage.local.get(['currentEmail'], resolve);
    });

    if (!emailData.currentEmail) {
      throw new Error('未找到当前邮箱地址');
    }

    if (!config.emailService) {
      throw new Error('未配置邮箱服务');
    }

    console.log('Augment续杯: 使用邮箱服务:', config.emailService);
    console.log('Augment续杯: 监控邮箱:', emailData.currentEmail);

    // 创建邮箱API实例
    const emailAPI = EmailAPI.create(config);
    emailAPI.setEmailAddress(emailData.currentEmail);

    // 开始轮询获取验证码
    let attempts = 0;
    const maxAttempts = 30; // 最多尝试30次，每次间隔10秒，总共5分钟

    const pollForCode = async () => {
      attempts++;

      try {
        showVerificationStatus(`正在获取验证码... (${attempts}/${maxAttempts})`);

        const emails = await emailAPI.getEmails();

        if (emails && emails.length > 0) {
          // 找到包含验证码的邮件
          const emailWithCode = emails.find(email => email.verificationCode);

          if (emailWithCode) {
            console.log('Augment续杯: 找到验证码:', emailWithCode.verificationCode);

            // 填入验证码
            inputElement.value = emailWithCode.verificationCode;

            // 触发输入事件
            const inputEvent = new Event('input', { bubbles: true });
            inputElement.dispatchEvent(inputEvent);

            // 触发change事件
            const changeEvent = new Event('change', { bubbles: true });
            inputElement.dispatchEvent(changeEvent);

            showVerificationStatus('验证码已自动填入！');

            // 尝试自动提交
            setTimeout(() => {
              const submitButton = document.querySelector('button[type="submit"]') ||
                                 document.querySelector('button[name="action"]') ||
                                 document.querySelector('input[type="submit"]');

              if (submitButton) {
                console.log('Augment续杯: 自动提交验证码');
                submitButton.click();
              }
            }, 1000);

            return;
          }
        }

        // 如果还没有收到验证码，继续尝试
        if (attempts < maxAttempts) {
          setTimeout(pollForCode, 10000); // 10秒后再次尝试
        } else {
          showVerificationStatus('获取验证码超时，请手动输入');
        }

      } catch (error) {
        console.error('Augment续杯: 获取验证码时出错:', error);

        if (attempts < maxAttempts) {
          setTimeout(pollForCode, 10000); // 出错后也继续尝试
        } else {
          showVerificationStatus('获取验证码失败，请手动输入');
        }
      }
    };

    // 开始第一次尝试
    pollForCode();

  } catch (error) {
    console.error('Augment续杯: 验证码获取流程出错:', error);
    showVerificationStatus('验证码获取失败: ' + error.message);
  }
}

// 创建一个标志，用于跟踪按钮是否已添加
let buttonAdded = false;

// 使用防抖函数来限制checkUrl的调用频率
function debounce(func, wait) {
  let timeout;
  return function() {
    clearTimeout(timeout);
    timeout = setTimeout(func, wait);
  };
}

// 防抖处理的checkUrl函数
const debouncedCheckUrl = debounce(checkUrl, 300);

// 在页面变化时检查URL，但使用更精确的选择器和配置
const observer = new MutationObserver((mutations) => {
  // 只有当按钮尚未添加时才继续检查
  if (!buttonAdded) {
    debouncedCheckUrl();
  }
});

// 使用更精确的配置来观察DOM变化
observer.observe(document.body, {
  childList: true,
  subtree: true,
  attributes: false,
  characterData: false
});

// 初始检查
setTimeout(checkUrl, 500);
