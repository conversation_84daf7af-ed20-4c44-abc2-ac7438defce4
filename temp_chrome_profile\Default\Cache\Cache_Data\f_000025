{"version": 3, "file": "reddit-plugins/dc99c5c6506b994b53b9.js", "mappings": "uBAAIA,EACAC,E,KCAAC,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaE,QAGrB,IAAIC,EAASN,EAAyBE,GAAY,CAGjDG,QAAS,IAOV,OAHAE,EAAoBL,GAAUI,EAAQA,EAAOD,QAASJ,GAG/CK,EAAOD,QAIfJ,EAAoBO,EAAID,ECxBxBN,EAAoBQ,EAAKH,IACxB,IAAII,EAASJ,GAAUA,EAAOK,WAC7B,IAAOL,EAAiB,QACxB,IAAM,EAEP,OADAL,EAAoBW,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GCLRT,EAAoBW,EAAI,CAACP,EAASS,KACjC,IAAI,IAAIC,KAAOD,EACXb,EAAoBe,EAAEF,EAAYC,KAASd,EAAoBe,EAAEX,EAASU,IAC5EE,OAAOC,eAAeb,EAASU,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,MCJ3Ed,EAAoBoB,EAAI,GAGxBpB,EAAoBqB,EAAKC,GACjBC,QAAQC,IAAIR,OAAOS,KAAKzB,EAAoBoB,GAAGM,QAAO,CAACC,EAAUb,KACvEd,EAAoBoB,EAAEN,GAAKQ,EAASK,GAC7BA,IACL,KCNJ3B,EAAoB4B,EAAKN,GAEZA,EAAL,2BCHRtB,EAAoB6B,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,GACd,MAAOX,GACR,GAAsB,iBAAXY,OAAqB,OAAOA,QALjB,GCAxBjC,EAAoBe,EAAI,CAACmB,EAAKC,IAAUnB,OAAOoB,UAAUC,eAAeC,KAAKJ,EAAKC,GPA9EtC,EAAa,GACbC,EAAoB,eAExBE,EAAoBuC,EAAI,CAACC,EAAKC,EAAM3B,EAAKQ,KACxC,GAAGzB,EAAW2C,GAAQ3C,EAAW2C,GAAKE,KAAKD,OAA3C,CACA,IAAIE,EAAQC,EACZ,QAAWzC,IAARW,EAEF,IADA,IAAI+B,EAAUC,SAASC,qBAAqB,UACpCC,EAAI,EAAGA,EAAIH,EAAQI,OAAQD,IAAK,CACvC,IAAIE,EAAIL,EAAQG,GAChB,GAAGE,EAAEC,aAAa,QAAUX,GAAOU,EAAEC,aAAa,iBAAmBrD,EAAoBgB,EAAK,CAAE6B,EAASO,EAAG,OAG1GP,IACHC,GAAa,GACbD,EAASG,SAASM,cAAc,WAEzBC,QAAU,QACjBV,EAAOW,QAAU,IACbtD,EAAoBuD,IACvBZ,EAAOa,aAAa,QAASxD,EAAoBuD,IAElDZ,EAAOa,aAAa,eAAgB1D,EAAoBgB,GACxD6B,EAAOc,IAAMjB,GAEd3C,EAAW2C,GAAO,CAACC,GACnB,IAAIiB,EAAmB,CAACC,EAAMC,KAE7BjB,EAAOkB,QAAUlB,EAAOmB,OAAS,KACjCC,aAAaT,GACb,IAAIU,EAAUnE,EAAW2C,GAIzB,UAHO3C,EAAW2C,GAClBG,EAAOsB,YAActB,EAAOsB,WAAWC,YAAYvB,GACnDqB,GAAWA,EAAQG,SAASC,GAAQA,EAAGR,KACpCD,EAAM,OAAOA,EAAKC,IAElBN,EAAUe,WAAWX,EAAiBY,KAAK,UAAMnE,EAAW,CAAEoE,KAAM,UAAWC,OAAQ7B,IAAW,MACtGA,EAAOkB,QAAUH,EAAiBY,KAAK,KAAM3B,EAAOkB,SACpDlB,EAAOmB,OAASJ,EAAiBY,KAAK,KAAM3B,EAAOmB,QACnDlB,GAAcE,SAAS2B,KAAKC,YAAY/B,KQtCzC3C,EAAoB2E,EAAKvE,IACH,oBAAXwE,QAA0BA,OAAOC,aAC1C7D,OAAOC,eAAeb,EAASwE,OAAOC,YAAa,CAAEC,MAAO,WAE7D9D,OAAOC,eAAeb,EAAS,aAAc,CAAE0E,OAAO,K,MCLvD,IAAIC,EACA/E,EAAoB6B,EAAEmD,gBAAeD,EAAY/E,EAAoB6B,EAAEoD,SAAW,IACtF,IAAInC,EAAW9C,EAAoB6B,EAAEiB,SACrC,IAAKiC,GAAajC,IACbA,EAASoC,gBACZH,EAAYjC,EAASoC,cAAczB,MAC/BsB,GAAW,CACf,IAAIlC,EAAUC,EAASC,qBAAqB,UAC5C,GAAGF,EAAQI,OAEV,IADA,IAAID,EAAIH,EAAQI,OAAS,EAClBD,GAAK,IAAM+B,GAAWA,EAAYlC,EAAQG,KAAKS,IAMzD,IAAKsB,EAAW,MAAM,IAAII,MAAM,yDAChCJ,EAAYA,EAAUK,QAAQ,OAAQ,IAAIA,QAAQ,QAAS,IAAIA,QAAQ,YAAa,KACpFpF,EAAoBqF,EAAIN,EAAY,O,SCbpC,IAAIO,EAAkB,CACrB,IAAK,GAGNtF,EAAoBoB,EAAEmE,EAAI,CAACjE,EAASK,KAElC,IAAI6D,EAAqBxF,EAAoBe,EAAEuE,EAAiBhE,GAAWgE,EAAgBhE,QAAWnB,EACtG,GAA0B,IAAvBqF,EAGF,GAAGA,EACF7D,EAASe,KAAK8C,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIlE,SAAQ,CAACmE,EAASC,IAAYH,EAAqBF,EAAgBhE,GAAW,CAACoE,EAASC,KAC1GhE,EAASe,KAAK8C,EAAmB,GAAKC,GAGtC,IAAIjD,EAAMxC,EAAoBqF,EAAIrF,EAAoB4B,EAAEN,GAEpDsE,EAAQ,IAAIT,MAgBhBnF,EAAoBuC,EAAEC,GAfFoB,IACnB,GAAG5D,EAAoBe,EAAEuE,EAAiBhE,KAEf,KAD1BkE,EAAqBF,EAAgBhE,MACRgE,EAAgBhE,QAAWnB,GACrDqF,GAAoB,CACtB,IAAIK,EAAYjC,IAAyB,SAAfA,EAAMW,KAAkB,UAAYX,EAAMW,MAChEuB,EAAUlC,GAASA,EAAMY,QAAUZ,EAAMY,OAAOf,IACpDmC,EAAMG,QAAU,iBAAmBzE,EAAU,cAAgBuE,EAAY,KAAOC,EAAU,IAC1FF,EAAMI,KAAO,iBACbJ,EAAMrB,KAAOsB,EACbD,EAAMK,QAAUH,EAChBN,EAAmB,GAAGI,MAIgB,SAAWtE,EAASA,KAiBlE,IAAI4E,EAAuB,CAACC,EAA4BC,KACvD,IAGInG,EAAUqB,GAHT+E,EAAUC,EAAaC,GAAWH,EAGhBpD,EAAI,EAC3B,GAAGqD,EAASG,MAAMC,GAAgC,IAAxBnB,EAAgBmB,KAAa,CACtD,IAAIxG,KAAYqG,EACZtG,EAAoBe,EAAEuF,EAAarG,KACrCD,EAAoBO,EAAEN,GAAYqG,EAAYrG,IAGhD,GAAGsG,EAAsBA,EAAQvG,GAGlC,IADGmG,GAA4BA,EAA2BC,GACrDpD,EAAIqD,EAASpD,OAAQD,IACzB1B,EAAU+E,EAASrD,GAChBhD,EAAoBe,EAAEuE,EAAiBhE,IAAYgE,EAAgBhE,IACrEgE,EAAgBhE,GAAS,KAE1BgE,EAAgBhE,GAAW,GAKzBoF,EAAqBC,KAA8B,wBAAIA,KAA8B,yBAAK,GAC9FD,EAAmBvC,QAAQ+B,EAAqB5B,KAAK,KAAM,IAC3DoC,EAAmBhE,KAAOwD,EAAqB5B,KAAK,KAAMoC,EAAmBhE,KAAK4B,KAAKoC,K,mCCpFhF,MAMME,EAAoB,cAsBpBC,EAAkB,CAC7B1F,IAAML,GACSmB,OAAO6E,aAAaC,QAAQjG,GAG3CkG,IAAK,CAAClG,EAAagE,IACV7C,OAAO6E,aAAaG,QAAQnG,EAAKgE,ICT5C,EC1BO,SAA4BjE,GAC/B,MAAMqG,EAAU,MAAQC,UACC,+BACPC,gBAAgBvG,EAAYsG,EAAUA,EAASE,eAAiB,IAGlF,OADAH,EAAQI,WAAazG,EAAWmF,KACzBkB,EDoBX,CAlB4E,CAC1ElB,KAAM,iBACNuB,KAAM,SACNJ,SAAU,GACVK,QAAS,CAAEC,aEAuD,CAClEC,MAAO,wBACPC,YAAa,4DACbC,SAAU,MACVC,QAAQ,EACRC,oBAAqB,2FACrBC,OAAQ,GACRC,cAAe,aACfC,QAAS,CAACC,GAAKC,QAAAA,EAASC,UAAAA,MACtB,MAAMC,EAAWD,EAAUC,SAAwDxB,EAC7EyB,EAA2BD,EAAQlH,IHThB,aGUnBoH,EAAyBF,EAAQlH,IAAIyF,GAE3C,GAAI0B,GAAaC,EAAS,CACxB,MAAMC,EAA2C,GAC7CD,IACFC,EAA4C,SAAID,GAE9CD,IACFE,EAA4C,KAAIF,KAEV,IAApCH,EAAQvE,MAAM6E,cAAcC,KAAiBP,EAAQvE,MAAM6E,aAAa,4BAC1EN,EAAQQ,YAAY,sCAAuCH,OFrBjEI,WAAYC,OAAST,UAAAA,MACnB,MAAMC,EAAWD,EAAUC,SAAwDxB,EAE7E0B,EADY,IAAIO,gBAAgB7G,OAAOgD,SAAS8D,QACb5H,IDZP,YCYsC,KAMxE,OAJIoH,GACFF,EAAQrB,IAAIJ,EAAmB2B,GAG1B,M", "sources": ["webpack://Destination/webpack/runtime/load script", "webpack://Destination/webpack/bootstrap", "webpack://Destination/webpack/runtime/compat get default export", "webpack://Destination/webpack/runtime/define property getters", "webpack://Destination/webpack/runtime/ensure chunk", "webpack://Destination/webpack/runtime/get javascript chunk filename", "webpack://Destination/webpack/runtime/global", "webpack://Destination/webpack/runtime/hasOwnProperty shorthand", "webpack://Destination/webpack/runtime/make namespace object", "webpack://Destination/webpack/runtime/publicPath", "webpack://Destination/webpack/runtime/jsonp chunk loading", "webpack://Destination/./destinations/reddit-plugins/src/utils.ts", "webpack://Destination/./destinations/reddit-plugins/src/index.ts", "webpack://Destination/../browser-destination-runtime/dist/esm/shim.js", "webpack://Destination/./destinations/reddit-plugins/src/redditPlugin/index.ts"], "sourcesContent": ["var inProgress = {};\nvar dataWebpackPrefix = \"Destination:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \"/\" + \"431110629a9fe8297174\" + \".js\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "var scriptUrl;\nif (__webpack_require__.g.importScripts) scriptUrl = __webpack_require__.g.location + \"\";\nvar document = __webpack_require__.g.document;\nif (!scriptUrl && document) {\n\tif (document.currentScript)\n\t\tscriptUrl = document.currentScript.src;\n\tif (!scriptUrl) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tif(scripts.length) {\n\t\t\tvar i = scripts.length - 1;\n\t\t\twhile (i > -1 && !scriptUrl) scriptUrl = scripts[i--].src;\n\t\t}\n\t}\n}\n// When supporting browsers where an automatic publicPath is not supported you must specify an output.publicPath manually via configuration\n// or pass an empty string (\"\") and set the __webpack_public_path__ variable from your code to use your own logic.\nif (!scriptUrl) throw new Error(\"Automatic publicPath is not supported in this browser\");\nscriptUrl = scriptUrl.replace(/#.*$/, \"\").replace(/\\?.*$/, \"\").replace(/\\/[^\\/]+$/, \"/\");\n__webpack_require__.p = scriptUrl + \"../\";", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t209: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t} else installedChunks[chunkId] = 0;\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkDestination\"] = self[\"webpackChunkDestination\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// The field name to include for the Reddit click_id Querystring in the context.integrations.Reddit Conversions Api.click_id\nexport const clickIdIntegrationFieldName = 'click_id'\n\n// The name of the Reddit click_id querystring to retrieve when the page loads\nexport const clickIdQuerystringName = 'rdt_cid'\n\n// The name of the key Segment will use when storing the rdt_cid locally in the browser\nexport const clickIdCookieName = 'rdt_cid_seg'\n\n// The field name to include for the Reddit rdt_uuid in the context.integrations.Reddit Conversions Api.rdt_uuid\nexport const rdtUUIDIntegrationFieldName = 'uuid'\n\n// The name of the Reddit rdt_uuid cookie\nexport const rdtCookieName = '_rdt_uuid'\n\nexport const getCookieValue = (cookieName: string): string | null => {\n  const name = cookieName + '='\n  const decodedCookie = decodeURIComponent(document.cookie)\n  const cookieArray = decodedCookie.split('; ')\n\n  for (const cookie of cookieArray) {\n    if (cookie.startsWith(name)) {\n      return cookie.substring(name.length)\n    }\n  }\n\n  return null\n}\n\nexport const storageFallback = {\n  get: (key: string) => {\n    const data = window.localStorage.getItem(key)\n    return data\n  },\n  set: (key: string, value: string) => {\n    return window.localStorage.setItem(key, value)\n  }\n}\n", "import type { Settings } from './generated-types'\nimport type { BrowserDestinationDefinition } from '@segment/browser-destination-runtime/types'\nimport { browserDestination } from '@segment/browser-destination-runtime/shim'\nimport { UniversalStorage } from '@segment/analytics-next'\nimport redditPlugin from './redditPlugin'\nimport { storageFallback, clickIdCookieName, clickIdQuerystringName } from './utils'\n\n// Switch from unknown to the partner SDK client types\nexport const destination: BrowserDestinationDefinition<Settings, unknown> = {\n  name: 'Reddit Plugins',\n  mode: 'device',\n  settings: {},\n  actions: { redditPlugin },\n  initialize: async ({ analytics }) => {\n    const storage = (analytics.storage as UniversalStorage<Record<string, string>>) ?? storageFallback\n    const urlParams = new URLSearchParams(window.location.search)\n    const clickId: string | null = urlParams.get(clickIdQuerystringName) || null\n\n    if (clickId) {\n      storage.set(clickIdCookieName, clickId)\n    }\n\n    return {}\n  }\n}\n\nexport default browserDestination(destination)\n", "export function browserDestination(definition) {\n    const factory = (async (settings) => {\n        const plugin = await import('./plugin');\n        return plugin.generatePlugins(definition, settings, settings.subscriptions || []);\n    });\n    factory.pluginName = definition.name;\n    return factory;\n}\n//# sourceMappingURL=shim.js.map", "import type { BrowserActionDefinition } from '@segment/browser-destination-runtime/types'\nimport type { Settings } from '../generated-types'\nimport type { Payload } from './generated-types'\nimport { UniversalStorage } from '@segment/analytics-next'\nimport {\n  storageFallback,\n  clickIdCookieName,\n  clickIdIntegrationFieldName,\n  rdtCookieName,\n  rdtUUIDIntegrationFieldName\n} from '../utils'\n\nconst action: BrowserActionDefinition<Settings, unknown, Payload> = {\n  title: 'Reddit Browser Plugin',\n  description: 'Enriches Segment payloads with data from the Reddit Pixel',\n  platform: 'web',\n  hidden: false,\n  defaultSubscription: 'type = \"track\" or type = \"identify\" or type = \"page\" or type = \"group\" or type = \"alias\"',\n  fields: {},\n  lifecycleHook: 'enrichment',\n  perform: (_, { context, analytics }) => {\n    const storage = (analytics.storage as UniversalStorage<Record<string, string>>) ?? storageFallback\n    const rdtCookie: string | null = storage.get(rdtCookieName)\n    const clickId: string | null = storage.get(clickIdCookieName)\n\n    if (rdtCookie || clickId) {\n      const integrationsData: Record<string, string> = {}\n      if (clickId) {\n        integrationsData[clickIdIntegrationFieldName] = clickId\n      }\n      if (rdtCookie) {\n        integrationsData[rdtUUIDIntegrationFieldName] = rdtCookie\n      }\n      if (context.event.integrations?.All !== false || context.event.integrations['Reddit Conversions Api']) {\n        context.updateEvent(`integrations.Reddit Conversions Api`, integrationsData)\n      }\n    }\n    return\n  }\n}\n\nexport default action\n"], "names": ["inProgress", "dataWebpackPrefix", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "exports", "module", "__webpack_modules__", "m", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "f", "e", "chunkId", "Promise", "all", "keys", "reduce", "promises", "u", "g", "globalThis", "this", "Function", "window", "obj", "prop", "prototype", "hasOwnProperty", "call", "l", "url", "done", "push", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "i", "length", "s", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "onScriptComplete", "prev", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "fn", "setTimeout", "bind", "type", "target", "head", "append<PERSON><PERSON><PERSON>", "r", "Symbol", "toStringTag", "value", "scriptUrl", "importScripts", "location", "currentScript", "Error", "replace", "p", "installedChunks", "j", "installedChunkData", "promise", "resolve", "reject", "error", "errorType", "realSrc", "message", "name", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "data", "chunkIds", "moreModules", "runtime", "some", "id", "chunkLoadingGlobal", "self", "clickIdCookieName", "storageFallback", "localStorage", "getItem", "set", "setItem", "factory", "settings", "generatePlugins", "subscriptions", "pluginName", "mode", "actions", "redditPlugin", "title", "description", "platform", "hidden", "defaultSubscription", "fields", "lifecycleHook", "perform", "_", "context", "analytics", "storage", "rdtCookie", "clickId", "integrationsData", "integrations", "All", "updateEvent", "initialize", "async", "URLSearchParams", "search"], "sourceRoot": ""}