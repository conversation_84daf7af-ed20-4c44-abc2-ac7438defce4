#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 桌面版
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import os
import re
import random
import string
import webbrowser
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.action_chains import Action<PERSON>hains
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

class EmailAPI:
    def __init__(self, config):
        self.config = config

    def extract_verification_code(self, content):
        patterns = [
            r'验证码[：:\s]*([A-Z0-9]{4,8})',
            r'verification code[：:\s]*([A-Z0-9]{4,8})',
            r'code[：:\s]*([A-Z0-9]{4,8})',
            r'([A-Z0-9]{6})',
            r'([0-9]{4,8})'
        ]

        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)
        return None

    def get_emails(self):
        if not REQUESTS_AVAILABLE:
            raise Exception('需要安装requests库: pip install requests')

        if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
            raise Exception('需要TempMail.Plus邮箱地址和PIN码')

        temp_mail_address = self.config['temp_mail_address']
        username, domain = temp_mail_address.split('@')

        endpoints = [
            f'https://tempmail.plus/api/v1/inbox/{username}',
            f'https://tempmail.plus/api/inbox/{username}',
            f'https://tempmail.plus/inbox/{username}',
        ]

        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, headers={
                    'Content-Type': 'application/json',
                    'X-Pin-Code': self.config['pin_code'],
                    'Authorization': f'PIN {self.config["pin_code"]}',
                }, timeout=10)

                if response.ok:
                    data = response.json()
                    emails = data.get('emails', data.get('messages', data.get('inbox', [])))

                    if isinstance(emails, list):
                        results = []
                        for email in emails[:5]:
                            content = email.get('body', email.get('content', email.get('text', '')))
                            code = self.extract_verification_code(content)
                            if code:
                                results.append({
                                    'verification_code': code,
                                    'subject': email.get('subject', ''),
                                    'content': content
                                })
                        return results
            except Exception as e:
                continue

        raise Exception('无法获取邮件')

class AugmentTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment续杯工具")
        self.root.geometry("900x600")
        self.root.configure(bg='#f0f0f0')

        self.config_file = "config.json"
        self.config = self.load_config()

        # 当前选中的页面
        self.current_page = None

        # 浏览器驱动
        self.driver = None

        self.create_widgets()
        self.load_saved_config()
        self.show_page("home")  # 默认显示主页
        
    def create_widgets(self):
        # 创建主容器
        main_container = tk.Frame(self.root, bg='#f0f0f0')
        main_container.pack(fill="both", expand=True)

        # 左侧导航栏
        self.create_sidebar(main_container)

        # 右侧内容区域
        self.create_content_area(main_container)

    def create_sidebar(self, parent):
        # 左侧导航栏
        sidebar = tk.Frame(parent, bg='#2c3e50', width=200)
        sidebar.pack(side="left", fill="y")
        sidebar.pack_propagate(False)

        # 标题
        title = tk.Label(sidebar, text="🚀 Augment续杯",
                        font=("Arial", 14, "bold"),
                        bg='#2c3e50', fg='white')
        title.pack(pady=20)

        # 导航按钮
        nav_buttons = [
            ("🏠 主页", "home"),
            ("⚙️ 配置设置", "config"),
            ("📧 邮箱管理", "email"),
            ("📋 日志查看", "log")
        ]

        self.nav_buttons = {}
        for text, page_id in nav_buttons:
            btn = tk.Button(sidebar, text=text,
                           command=lambda p=page_id: self.show_page(p),
                           bg='#34495e', fg='white',
                           font=("Arial", 10),
                           relief='flat', bd=0,
                           width=20, height=2)
            btn.pack(pady=2, padx=10, fill="x")
            self.nav_buttons[page_id] = btn

    def create_content_area(self, parent):
        # 右侧内容区域
        self.content_area = tk.Frame(parent, bg='white')
        self.content_area.pack(side="right", fill="both", expand=True)

        # 创建各个页面
        self.create_home_page()
        self.create_config_page()
        self.create_email_page()
        self.create_log_page()

    def create_home_page(self):
        self.home_page = tk.Frame(self.content_area, bg='white')

        # 欢迎标题
        welcome_frame = tk.Frame(self.home_page, bg='white')
        welcome_frame.pack(pady=30)

        title = tk.Label(welcome_frame, text="🚀 Augment续杯工具",
                        font=("Arial", 24, "bold"), bg='white', fg='#2c3e50')
        title.pack()

        subtitle = tk.Label(welcome_frame, text="自动化Augment账号注册和登录助手",
                           font=("Arial", 12), bg='white', fg='#7f8c8d')
        subtitle.pack(pady=5)

        # 功能卡片区域
        cards_frame = tk.Frame(self.home_page, bg='white')
        cards_frame.pack(pady=20, padx=40, fill="both", expand=True)

        # 第一行卡片
        row1_frame = tk.Frame(cards_frame, bg='white')
        row1_frame.pack(fill="x", pady=10)

        # 自动注册卡片
        register_card = tk.Frame(row1_frame, bg='#e8f5e8', relief='raised', bd=2)
        register_card.pack(side="left", fill="both", expand=True, padx=10)

        tk.Label(register_card, text="🎯 自动注册", font=("Arial", 16, "bold"),
                bg='#e8f5e8', fg='#27ae60').pack(pady=15)
        tk.Label(register_card, text="一键自动注册Augment账号\n生成邮箱→注册→验证",
                font=("Arial", 10), bg='#e8f5e8', fg='#2c3e50').pack(pady=5)

        self.auto_register_btn = tk.Button(register_card, text="🚀 开始自动注册",
                                          command=self.auto_register,
                                          bg="#27ae60", fg="white",
                                          font=("Arial", 12, "bold"),
                                          width=15, height=2)
        self.auto_register_btn.pack(pady=15)

        # 手动登录卡片
        login_card = tk.Frame(row1_frame, bg='#e8f4fd', relief='raised', bd=2)
        login_card.pack(side="left", fill="both", expand=True, padx=10)

        tk.Label(login_card, text="🔑 手动登录", font=("Arial", 16, "bold"),
                bg='#e8f4fd', fg='#3498db').pack(pady=15)
        tk.Label(login_card, text="使用现有账号登录\n生成邮箱→手动登录→获取验证码",
                font=("Arial", 10), bg='#e8f4fd', fg='#2c3e50').pack(pady=5)

        manual_login_btn = tk.Button(login_card, text="🌐 打开登录页",
                                    command=self.open_login_page,
                                    bg="#3498db", fg="white",
                                    font=("Arial", 12, "bold"),
                                    width=15, height=2)
        manual_login_btn.pack(pady=15)

        # 第二行卡片
        row2_frame = tk.Frame(cards_frame, bg='white')
        row2_frame.pack(fill="x", pady=10)

        # 邮箱管理卡片
        email_card = tk.Frame(row2_frame, bg='#fdf2e8', relief='raised', bd=2)
        email_card.pack(side="left", fill="both", expand=True, padx=5)

        tk.Label(email_card, text="📧 邮箱管理", font=("Arial", 16, "bold"),
                bg='#fdf2e8', fg='#f39c12').pack(pady=15)
        tk.Label(email_card, text="生成随机邮箱\n获取验证码",
                font=("Arial", 10), bg='#fdf2e8', fg='#2c3e50').pack(pady=5)

        email_manage_btn = tk.Button(email_card, text="📧 邮箱管理",
                                    command=lambda: self.show_page("email"),
                                    bg="#f39c12", fg="white",
                                    font=("Arial", 12, "bold"),
                                    width=15, height=2)
        email_manage_btn.pack(pady=15)

        # 配置设置卡片
        config_card = tk.Frame(row2_frame, bg='#f8e8f8', relief='raised', bd=2)
        config_card.pack(side="left", fill="both", expand=True, padx=5)

        tk.Label(config_card, text="⚙️ 配置设置", font=("Arial", 16, "bold"),
                bg='#f8e8f8', fg='#9b59b6').pack(pady=15)
        tk.Label(config_card, text="设置域名和邮箱服务\n配置TempMail.Plus",
                font=("Arial", 10), bg='#f8e8f8', fg='#2c3e50').pack(pady=5)

        config_btn = tk.Button(config_card, text="⚙️ 配置设置",
                              command=lambda: self.show_page("config"),
                              bg="#9b59b6", fg="white",
                              font=("Arial", 12, "bold"),
                              width=15, height=2)
        config_btn.pack(pady=15)

        # 浏览器控制卡片
        browser_card = tk.Frame(row2_frame, bg='#f0f0f0', relief='raised', bd=2)
        browser_card.pack(side="left", fill="both", expand=True, padx=5)

        tk.Label(browser_card, text="🌐 浏览器控制", font=("Arial", 16, "bold"),
                bg='#f0f0f0', fg='#95a5a6').pack(pady=15)
        tk.Label(browser_card, text="关闭自动化浏览器\n清理临时数据",
                font=("Arial", 10), bg='#f0f0f0', fg='#2c3e50').pack(pady=5)

        browser_btn = tk.Button(browser_card, text="🔒 关闭浏览器",
                               command=self.close_browser,
                               bg="#95a5a6", fg="white",
                               font=("Arial", 12, "bold"),
                               width=15, height=2)
        browser_btn.pack(pady=15)

        # 状态显示区域
        status_frame = tk.Frame(self.home_page, bg='white')
        status_frame.pack(fill="x", pady=20, padx=40)

        tk.Label(status_frame, text="📊 系统状态", font=("Arial", 14, "bold"),
                bg='white', fg='#2c3e50').pack(anchor="w")

        self.status_text = tk.Text(status_frame, height=4, font=("Arial", 10),
                                  state='disabled', bg='#f8f9fa')
        self.status_text.pack(fill="x", pady=10)

        # 更新状态显示
        self.update_status_display()

    def create_config_page(self):
        self.config_page = tk.Frame(self.content_area, bg='white')

        # 页面标题
        title = tk.Label(self.config_page, text="⚙️ 配置设置",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 配置表单
        form_frame = tk.Frame(self.config_page, bg='white')
        form_frame.pack(pady=20, padx=40, fill="x")

        # 域名配置
        domain_frame = tk.Frame(form_frame, bg='white')
        domain_frame.pack(fill="x", pady=10)
        tk.Label(domain_frame, text="邮箱域名:", font=("Arial", 12), bg='white').pack(anchor="w")
        self.domain_entry = tk.Entry(domain_frame, font=("Arial", 11), width=40)
        self.domain_entry.pack(fill="x", pady=5)
        tk.Label(domain_frame, text="例如: example.com", fg="gray", bg='white').pack(anchor="w")

        # TempMail.Plus配置
        tempmail_frame = tk.Frame(form_frame, bg='white')
        tempmail_frame.pack(fill="x", pady=10)
        tk.Label(tempmail_frame, text="TempMail.Plus邮箱:", font=("Arial", 12), bg='white').pack(anchor="w")
        self.tempmail_entry = tk.Entry(tempmail_frame, font=("Arial", 11), width=40)
        self.tempmail_entry.pack(fill="x", pady=5)
        tk.Label(tempmail_frame, text="例如: <EMAIL>", fg="gray", bg='white').pack(anchor="w")

        # PIN码配置
        pin_frame = tk.Frame(form_frame, bg='white')
        pin_frame.pack(fill="x", pady=10)
        tk.Label(pin_frame, text="PIN码:", font=("Arial", 12), bg='white').pack(anchor="w")
        self.pin_entry = tk.Entry(pin_frame, show="*", font=("Arial", 11), width=20)
        self.pin_entry.pack(anchor="w", pady=5)

        # 保存按钮
        save_btn = tk.Button(form_frame, text="💾 保存配置", command=self.save_config,
                            bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                            width=15, height=2)
        save_btn.pack(pady=20)

    def create_email_page(self):
        self.email_page = tk.Frame(self.content_area, bg='white')

        title = tk.Label(self.email_page, text="📧 邮箱管理",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 邮箱生成区域
        gen_frame = tk.Frame(self.email_page, bg='white')
        gen_frame.pack(pady=20, padx=40, fill="x")

        tk.Label(gen_frame, text="随机邮箱生成", font=("Arial", 14, "bold"), bg='white').pack(anchor="w", pady=10)

        gen_btn = tk.Button(gen_frame, text="📧 生成随机邮箱", command=self.generate_email,
                           bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                           width=20, height=2)
        gen_btn.pack(pady=10)

        # 显示生成的邮箱
        self.email_display = tk.Text(gen_frame, height=3, font=("Arial", 11), state='disabled')
        self.email_display.pack(fill="x", pady=10)

        # 验证码获取区域
        code_frame = tk.Frame(self.email_page, bg='white')
        code_frame.pack(pady=20, padx=40, fill="x")

        tk.Label(code_frame, text="验证码获取", font=("Arial", 14, "bold"), bg='white').pack(anchor="w", pady=10)

        code_btn = tk.Button(code_frame, text="🔍 获取验证码", command=self.get_code,
                            bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                            width=20, height=2)
        code_btn.pack(pady=10)

        # 显示验证码
        self.code_display = tk.Text(code_frame, height=3, font=("Arial", 11), state='disabled')
        self.code_display.pack(fill="x", pady=10)



    def create_log_page(self):
        self.log_page = tk.Frame(self.content_area, bg='white')

        title = tk.Label(self.log_page, text="📋 日志查看",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 日志区域
        log_frame = tk.Frame(self.log_page, bg='white')
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)

        self.log_text = scrolledtext.ScrolledText(log_frame, font=("Consolas", 10))
        self.log_text.pack(fill="both", expand=True)

        # 清除按钮
        clear_btn = tk.Button(log_frame, text="🗑️ 清除日志", command=self.clear_log,
                             bg="#95a5a6", fg="white", font=("Arial", 10))
        clear_btn.pack(pady=10)

    def show_page(self, page_id):
        # 隐藏所有页面
        for page in [self.home_page, self.config_page, self.email_page, self.log_page]:
            page.pack_forget()

        # 重置所有按钮颜色
        for btn in self.nav_buttons.values():
            btn.configure(bg='#34495e')

        # 显示选中的页面并高亮按钮
        if page_id == "home":
            self.home_page.pack(fill="both", expand=True)
            self.nav_buttons["home"].configure(bg='#3498db')
        elif page_id == "config":
            self.config_page.pack(fill="both", expand=True)
            self.nav_buttons["config"].configure(bg='#3498db')
        elif page_id == "email":
            self.email_page.pack(fill="both", expand=True)
            self.nav_buttons["email"].configure(bg='#3498db')
        elif page_id == "log":
            self.log_page.pack(fill="both", expand=True)
            self.nav_buttons["log"].configure(bg='#3498db')

        self.current_page = page_id

    def update_status_display(self):
        """更新状态显示"""
        status_info = []

        # 检查配置状态
        if self.config.get('email_domain'):
            status_info.append(f"✅ 邮箱域名: {self.config['email_domain']}")
        else:
            status_info.append("❌ 邮箱域名: 未配置")

        if self.config.get('temp_mail_address'):
            status_info.append(f"✅ TempMail.Plus: {self.config['temp_mail_address']}")
        else:
            status_info.append("❌ TempMail.Plus: 未配置")

        if self.config.get('pin_code'):
            status_info.append("✅ PIN码: 已设置")
        else:
            status_info.append("❌ PIN码: 未设置")

        # 检查requests库
        if REQUESTS_AVAILABLE:
            status_info.append("✅ 网络功能: 可用")
        else:
            status_info.append("❌ 网络功能: 需要安装requests库")

        # 检查selenium库
        if SELENIUM_AVAILABLE:
            status_info.append("✅ 自动化功能: 可用")
        else:
            status_info.append("❌ 自动化功能: 需要安装selenium和webdriver-manager库")

        # 检查浏览器状态
        if self.driver:
            status_info.append("🌐 自动化浏览器: 运行中")
        else:
            status_info.append("🔒 自动化浏览器: 未启动")

        # 更新显示
        self.status_text.config(state='normal')
        self.status_text.delete(1.0, 'end')
        self.status_text.insert(1.0, '\n'.join(status_info))
        self.status_text.config(state='disabled')

    def auto_register(self):
        """自动注册功能"""
        def register_thread():
            try:
                self.log("🚀 开始自动注册流程...")

                # 检查配置
                if not self.config.get('email_domain'):
                    messagebox.showerror("错误", "请先在配置页面设置邮箱域名")
                    return

                # 生成随机邮箱
                self.log("📧 正在生成随机邮箱...")
                chars = string.ascii_letters + string.digits
                random_str = ''.join(random.choice(chars) for _ in range(12))
                email = f"{random_str}@{self.config['email_domain']}"

                self.root.clipboard_clear()
                self.root.clipboard_append(email)

                self.log(f"✅ 生成邮箱: {email}")
                self.log("📋 邮箱已复制到剪贴板")

                # 使用Selenium自动化浏览器
                if SELENIUM_AVAILABLE:
                    self.log("🤖 启动自动化浏览器...")
                    try:
                        # 设置Chrome选项
                        chrome_options = Options()
                        chrome_options.add_argument("--start-maximized")
                        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
                        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                        chrome_options.add_experimental_option('useAutomationExtension', False)

                        # 清除所有浏览器数据（相当于全新安装）
                        temp_profile = os.path.join(os.getcwd(), "temp_chrome_profile")
                        if os.path.exists(temp_profile):
                            import shutil
                            shutil.rmtree(temp_profile, ignore_errors=True)
                        chrome_options.add_argument(f"--user-data-dir={temp_profile}")
                        chrome_options.add_argument("--no-first-run")
                        chrome_options.add_argument("--disable-default-apps")

                        # 创建WebDriver
                        service = Service(ChromeDriverManager().install())
                        self.driver = webdriver.Chrome(service=service, options=chrome_options)

                        # 隐藏自动化标识
                        self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                        self.log("🌐 正在打开Augment Code网站...")
                        self.driver.get("https://www.augmentcode.com/")

                        # 等待页面加载
                        wait = WebDriverWait(self.driver, 15)

                        # 等待页面完全加载
                        self.log("⏳ 等待页面完全加载...")
                        try:
                            # 等待页面标题加载
                            wait.until(lambda driver: driver.title != "")
                            # 等待页面body元素加载
                            wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                            # 等待Sign In按钮出现（更智能的等待）
                            self.log("⏳ 等待Sign In按钮加载...")
                            import time
                            time.sleep(1)  # 减少基础等待时间
                            self.log("✅ 页面加载完成")
                        except Exception as e:
                            self.log(f"⚠️ 页面加载检测失败，继续尝试: {e}")

                        self.log("🔍 正在查找Sign In按钮...")
                        # 查找并点击Sign In按钮 - 基于实际HTML结构优化
                        sign_in_selectors = [
                            # 基于实际HTML结构的精确选择器
                            "//a[@href='https://app.augmentcode.com' and contains(text(), 'Sign in')]",
                            "//a[@data-slot='button' and contains(text(), 'Sign in')]",
                            "//a[contains(@class, 'cursor-pointer') and contains(text(), 'Sign in')]",
                            "//a[contains(@href, 'app.augmentcode.com')]",
                            # 备用选择器
                            "//a[contains(text(), 'Sign in')]",
                            "//a[contains(text(), 'Sign In')]",
                            "//button[contains(text(), 'Sign in')]",
                            "//button[contains(text(), 'Sign In')]",
                            "//a[@href='/signin']",
                            "//a[@href='/login']",
                            # CSS选择器转换为XPath
                            "//a[contains(@class, 'inline-flex') and contains(@class, 'items-center') and contains(text(), 'Sign')]"
                        ]

                        sign_in_button = None

                        # 首先尝试XPath选择器 - 使用更短的等待时间，多次尝试
                        for attempt in range(3):  # 最多尝试3次
                            for selector in sign_in_selectors:
                                try:
                                    # 使用较短的等待时间
                                    short_wait = WebDriverWait(self.driver, 3)
                                    sign_in_button = short_wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                                    self.log(f"✅ 找到Sign In按钮 (XPath): {selector}")
                                    break
                                except:
                                    continue
                            if sign_in_button:
                                break
                            if attempt < 2:  # 不是最后一次尝试
                                self.log(f"⏳ 第{attempt + 1}次尝试未找到，等待1秒后重试...")
                                time.sleep(1)

                        # 如果XPath失败，尝试CSS选择器
                        if not sign_in_button:
                            css_selectors = [
                                "a[href='https://app.augmentcode.com']",
                                "a[data-slot='button']",
                                "a.cursor-pointer:contains('Sign in')",
                                "a[href*='app.augmentcode.com']",
                                "a:contains('Sign in')",
                                "a:contains('Sign In')",
                                "button:contains('Sign in')",
                                "button:contains('Sign In')"
                            ]

                            for css_selector in css_selectors:
                                try:
                                    # 对于包含:contains的选择器，使用JavaScript查找
                                    if ':contains(' in css_selector:
                                        text_to_find = css_selector.split(':contains(')[1].rstrip(')')
                                        text_to_find = text_to_find.strip("'\"")
                                        elements = self.driver.find_elements(By.TAG_NAME, "a") + self.driver.find_elements(By.TAG_NAME, "button")
                                        for element in elements:
                                            if text_to_find.lower() in element.text.lower():
                                                sign_in_button = element
                                                self.log(f"✅ 找到Sign In按钮 (文本匹配): {element.text}")
                                                break
                                        if sign_in_button:
                                            break
                                    else:
                                        sign_in_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, css_selector)))
                                        self.log(f"✅ 找到Sign In按钮 (CSS): {css_selector}")
                                        break
                                except:
                                    continue

                        if sign_in_button:
                            # 滚动到按钮位置确保可见
                            self.driver.execute_script("arguments[0].scrollIntoView(true);", sign_in_button)
                            time.sleep(0.5)  # 减少滚动等待时间

                            # 点击按钮
                            sign_in_button.click()
                            self.log("🎯 已点击Sign In按钮！")

                            # 智能等待登录界面加载 - 等待URL变化或邮箱输入框出现
                            self.log("⏳ 等待登录界面加载...")
                            login_loaded = False
                            for i in range(10):  # 最多等待10秒
                                try:
                                    # 检查是否有邮箱输入框出现
                                    email_input = self.driver.find_element(By.XPATH, "//input[@name='username']")
                                    if email_input.is_displayed():
                                        self.log("✅ 登录界面已加载")
                                        login_loaded = True
                                        break
                                except:
                                    pass
                                time.sleep(1)

                            if not login_loaded:
                                self.log("⚠️ 登录界面加载超时，继续尝试...")
                                time.sleep(1)  # 额外等待1秒

                            # 步骤2: 查找并填入邮箱输入框
                            self.log("📧 正在查找邮箱输入框...")
                            email_input = None

                            email_selectors = [
                                "//input[@name='username' and @type='text']",
                                "//input[@id='username']",
                                "//input[@inputmode='email']",
                                "//input[contains(@class, 'input') and @type='text']",
                                "//input[@autocomplete='email']",
                                "//input[@type='text' and @required]",
                                "//input[@type='email']",
                                "input[name='username']",
                                "input[id='username']",
                                "input[inputmode='email']",
                                "input[type='email']"
                            ]

                            # 使用更短的等待时间，多次尝试
                            for attempt in range(5):  # 最多尝试5次
                                for selector in email_selectors:
                                    try:
                                        short_wait = WebDriverWait(self.driver, 2)  # 2秒等待
                                        if selector.startswith("//"):
                                            email_input = short_wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                                        else:
                                            email_input = short_wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                                        self.log(f"✅ 找到邮箱输入框: {selector}")
                                        break
                                    except:
                                        continue
                                if email_input:
                                    break
                                if attempt < 4:  # 不是最后一次尝试
                                    self.log(f"⏳ 第{attempt + 1}次尝试未找到邮箱输入框，等待1秒后重试...")
                                    time.sleep(1)

                            if email_input:
                                # 清空输入框并输入邮箱
                                email_input.clear()
                                email_input.send_keys(email)
                                self.log(f"📧 已输入邮箱: {email}")
                                time.sleep(0.5)  # 减少输入后等待时间

                                # 步骤3: 等待人机验证复选框加载，然后点击
                                self.log("🤖 等待人机验证复选框加载...")
                                self.log("⏳ 等待8秒确保复选框完全加载...")
                                time.sleep(8)  # 增加8秒延时等待复选框加载

                                self.log("🔍 正在查找人机验证复选框...")
                                checkbox = None

                                # 多次尝试查找复选框，因为可能需要时间加载
                                for attempt in range(3):
                                    try:
                                        # 直接查找所有复选框
                                        checkboxes = self.driver.find_elements(By.CSS_SELECTOR, "input[type='checkbox']")
                                        self.log(f"🔍 找到 {len(checkboxes)} 个复选框")

                                        # 找到可见且可点击的复选框
                                        for i, cb in enumerate(checkboxes):
                                            try:
                                                if cb.is_displayed() and cb.is_enabled():
                                                    # 检查复选框是否在视窗内
                                                    location = cb.location
                                                    size = cb.size
                                                    if location['y'] > 0 and size['width'] > 0 and size['height'] > 0:
                                                        checkbox = cb
                                                        self.log(f"✅ 找到可用的人机验证复选框 (第{i+1}个)")
                                                        self.log(f"   位置: x={location['x']}, y={location['y']}")
                                                        self.log(f"   大小: width={size['width']}, height={size['height']}")
                                                        break
                                            except Exception as e:
                                                self.log(f"⚠️ 检查复选框 {i+1} 时出错: {e}")
                                                continue

                                        if checkbox:
                                            break

                                    except Exception as e:
                                        self.log(f"⚠️ 查找复选框时出错: {e}")

                                    if attempt < 2:
                                        self.log(f"⏳ 第{attempt + 1}次尝试未找到复选框，等待2秒后重试...")
                                        time.sleep(2)

                                if checkbox:
                                    # 使用多种方法尝试点击复选框
                                    self.log("🎯 正在点击人机验证复选框...")

                                    # 滚动到复选框位置确保可见
                                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", checkbox)
                                    time.sleep(1)

                                    click_success = False

                                    # 方法1: 偏移点击（推荐）
                                    try:
                                        self.log("🎯 尝试方法1: 偏移点击...")
                                        size = checkbox.size
                                        location = checkbox.location

                                        # 计算偏移位置（偏离中心30%）
                                        offset_x = size['width'] * 0.3
                                        offset_y = size['height'] * 0.3

                                        actions = ActionChains(self.driver)
                                        actions.move_to_element_with_offset(checkbox, offset_x, offset_y).click().perform()

                                        self.log("✅ 偏移点击成功")
                                        click_success = True

                                    except Exception as e:
                                        self.log(f"⚠️ 偏移点击失败: {e}")

                                    # 方法2: JavaScript点击
                                    if not click_success:
                                        try:
                                            self.log("🎯 尝试方法2: JavaScript点击...")
                                            self.driver.execute_script("arguments[0].click();", checkbox)
                                            self.log("✅ JavaScript点击成功")
                                            click_success = True
                                        except Exception as e:
                                            self.log(f"⚠️ JavaScript点击失败: {e}")

                                    # 方法3: 直接点击
                                    if not click_success:
                                        try:
                                            self.log("🎯 尝试方法3: 直接点击...")
                                            checkbox.click()
                                            self.log("✅ 直接点击成功")
                                            click_success = True
                                        except Exception as e:
                                            self.log(f"⚠️ 直接点击失败: {e}")

                                    # 方法4: 点击父元素（有时复选框被包装在label中）
                                    if not click_success:
                                        try:
                                            self.log("🎯 尝试方法4: 点击父元素...")
                                            parent = checkbox.find_element(By.XPATH, "./..")
                                            parent.click()
                                            self.log("✅ 父元素点击成功")
                                            click_success = True
                                        except Exception as e:
                                            self.log(f"⚠️ 父元素点击失败: {e}")

                                    if click_success:
                                        self.log("✅ 人机验证复选框点击成功")
                                        time.sleep(2)  # 等待验证处理
                                    else:
                                        self.log("❌ 所有点击方法都失败了")

                                    # 完整的成功消息
                                    success_msg = f"""🎉 全自动化成功！

✅ 已自动打开Augment网站
✅ 已自动点击Sign In按钮
✅ 已自动输入邮箱: {email}
✅ 已自动完成人机验证（偏移点击）

接下来请手动完成:
1. 等待人机验证通过
2. 点击继续/登录按钮
3. 完成注册流程
4. 回到本工具获取验证码

浏览器将保持打开状态供您操作。"""

                                    messagebox.showinfo("全自动化成功", success_msg)
                                else:
                                    self.log("⚠️ 未找到人机验证复选框")

                                    # 保存调试信息
                                    try:
                                        self.log("📸 保存调试信息...")
                                        # 保存页面截图
                                        self.driver.save_screenshot("checkbox_debug_screenshot.png")

                                        # 保存页面源码
                                        with open("checkbox_debug_source.html", "w", encoding="utf-8") as f:
                                            f.write(self.driver.page_source)

                                        # 查找所有input元素
                                        all_inputs = self.driver.find_elements(By.TAG_NAME, "input")
                                        self.log(f"🔍 页面上共有 {len(all_inputs)} 个input元素")

                                        for i, inp in enumerate(all_inputs):
                                            try:
                                                inp_type = inp.get_attribute("type")
                                                inp_class = inp.get_attribute("class")
                                                inp_id = inp.get_attribute("id")
                                                inp_name = inp.get_attribute("name")
                                                is_displayed = inp.is_displayed()
                                                self.log(f"   Input {i+1}: type='{inp_type}', class='{inp_class}', id='{inp_id}', name='{inp_name}', visible={is_displayed}")
                                            except:
                                                pass

                                        self.log("📄 调试信息已保存到 checkbox_debug_screenshot.png 和 checkbox_debug_source.html")

                                    except Exception as debug_e:
                                        self.log(f"❌ 保存调试信息失败: {debug_e}")

                                    partial_success_msg = f"""⚠️ 部分自动化成功

✅ 已自动打开Augment网站
✅ 已自动点击Sign In按钮
✅ 已自动输入邮箱: {email}
⚠️ 未找到人机验证复选框

📸 调试信息已保存，请查看:
- checkbox_debug_screenshot.png
- checkbox_debug_source.html

请手动完成:
1. 点击人机验证复选框
2. 点击继续/登录按钮
3. 完成注册流程
4. 回到本工具获取验证码"""

                                    messagebox.showinfo("部分自动化成功", partial_success_msg)
                            else:
                                self.log("⚠️ 未找到邮箱输入框")
                                basic_success_msg = f"""⚠️ 基础自动化成功

✅ 已自动打开Augment网站
✅ 已自动点击Sign In按钮
⚠️ 未找到邮箱输入框

生成的邮箱: {email} (已复制到剪贴板)

请手动完成:
1. 在邮箱输入框中粘贴: {email}
2. 点击人机验证复选框
3. 点击继续/登录按钮
4. 完成注册流程
5. 回到本工具获取验证码"""

                                messagebox.showinfo("基础自动化成功", basic_success_msg)

                            # 显示成功消息
                            success_msg = f"""🎉 自动化成功！

✅ 已自动打开Augment网站
✅ 已自动点击Sign In按钮
✅ 生成邮箱: {email} (已复制到剪贴板)

接下来请手动完成:
1. 选择注册新账号
2. 粘贴邮箱地址: {email}
3. 设置密码并完成注册
4. 回到本工具获取验证码

浏览器将保持打开状态供您操作。"""

                            messagebox.showinfo("自动化成功", success_msg)
                        else:
                            self.log("⚠️ 未找到Sign In按钮，正在保存调试信息...")

                            # 保存调试信息
                            try:
                                # 保存页面截图
                                screenshot_path = "debug_screenshot.png"
                                self.driver.save_screenshot(screenshot_path)
                                self.log(f"📸 页面截图已保存: {screenshot_path}")

                                # 保存页面源码
                                html_path = "debug_page_source.html"
                                with open(html_path, 'w', encoding='utf-8') as f:
                                    f.write(self.driver.page_source)
                                self.log(f"📄 页面源码已保存: {html_path}")

                                # 查找所有可能的链接和按钮
                                all_links = self.driver.find_elements(By.TAG_NAME, "a")
                                all_buttons = self.driver.find_elements(By.TAG_NAME, "button")

                                self.log(f"🔍 页面上找到 {len(all_links)} 个链接和 {len(all_buttons)} 个按钮")

                                # 记录包含"sign"文字的元素
                                sign_elements = []
                                for element in all_links + all_buttons:
                                    try:
                                        if 'sign' in element.text.lower():
                                            sign_elements.append(f"{element.tag_name}: '{element.text}' - href: {element.get_attribute('href')}")
                                    except:
                                        pass

                                if sign_elements:
                                    self.log("🔍 找到包含'sign'的元素:")
                                    for elem in sign_elements[:5]:  # 只显示前5个
                                        self.log(f"   {elem}")

                            except Exception as debug_e:
                                self.log(f"❌ 保存调试信息失败: {debug_e}")

                            fallback_msg = f"""⚠️ 自动化部分成功

✅ 已打开Augment网站
⚠️ 未能自动找到Sign In按钮
✅ 生成邮箱: {email} (已复制到剪贴板)
📸 调试信息已保存到程序目录

请手动完成以下步骤:
1. 点击网站右上角的 'Sign In' 按钮
2. 选择注册新账号
3. 粘贴邮箱地址: {email}
4. 设置密码并完成注册
5. 回到本工具获取验证码

如果问题持续，请查看debug_screenshot.png和debug_page_source.html文件"""

                            messagebox.showinfo("需要手动操作", fallback_msg)

                    except Exception as e:
                        self.log(f"❌ 浏览器自动化失败: {e}")
                        # 如果自动化失败，回退到手动模式
                        self.log("🔄 回退到手动模式...")
                        webbrowser.open("https://www.augmentcode.com/")

                        guide_msg = f"""⚠️ 自动化失败，已切换到手动模式

生成的邮箱: {email}
(已复制到剪贴板)

请手动完成以下步骤:
1. 在打开的网页中点击右上角 'Sign In'
2. 选择注册新账号
3. 粘贴邮箱地址: {email}
4. 设置密码并完成注册
5. 回到本工具获取验证码

错误信息: {str(e)}"""

                        messagebox.showinfo("手动操作指南", guide_msg)
                else:
                    # 如果没有安装selenium，使用传统方式
                    self.log("⚠️ 未安装Selenium，使用手动模式...")
                    webbrowser.open("https://www.augmentcode.com/")

                    guide_msg = f"""📋 手动注册指南

生成的邮箱: {email}
(已复制到剪贴板)

请按以下步骤操作:
1. 在打开的网页中点击右上角 'Sign In'
2. 选择注册新账号
3. 粘贴邮箱地址: {email}
4. 设置密码并完成注册
5. 回到本工具获取验证码

💡 提示: 安装selenium和webdriver-manager可启用自动化功能"""

                    messagebox.showinfo("注册指南", guide_msg)

            except Exception as e:
                self.log(f"❌ 自动注册失败: {e}")
                messagebox.showerror("错误", f"自动注册失败: {e}")

        threading.Thread(target=register_thread, daemon=True).start()

    def open_login_page(self):
        """打开登录页面"""
        try:
            self.log("🌐 正在打开Augment登录页面...")
            url = "https://www.augmentcode.com/"
            webbrowser.open(url)
            self.log("✅ 已在默认浏览器中打开Augment官网")
            self.log("💡 请点击网站右上角的 'Sign In' 按钮登录")

            messagebox.showinfo("提示", "已打开Augment官网\n\n请点击右上角的 'Sign In' 按钮进行登录")

        except Exception as e:
            self.log(f"❌ 打开登录页面失败: {e}")
            messagebox.showerror("错误", f"打开登录页面失败: {e}")
        
    def log(self, message):
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert("end", f"[{timestamp}] {message}\n")
        self.log_text.see("end")
        self.root.update()
        
    def clear_log(self):
        self.log_text.delete("1.0", "end")
        
    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
        
    def save_config_to_file(self):
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
            
    def load_saved_config(self):
        if self.config.get('email_domain'):
            self.domain_entry.insert(0, self.config['email_domain'])
        if self.config.get('temp_mail_address'):
            self.tempmail_entry.insert(0, self.config['temp_mail_address'])
        if self.config.get('pin_code'):
            self.pin_entry.insert(0, self.config['pin_code'])
            
    def save_config(self):
        domain = self.domain_entry.get().strip()
        tempmail = self.tempmail_entry.get().strip()
        pin = self.pin_entry.get().strip()
        
        if not domain:
            messagebox.showerror("错误", "请输入邮箱域名")
            return
        if not tempmail:
            messagebox.showerror("错误", "请输入TempMail.Plus邮箱")
            return
        if not pin:
            messagebox.showerror("错误", "请输入PIN码")
            return
            
        self.config['email_domain'] = domain
        self.config['temp_mail_address'] = tempmail
        self.config['pin_code'] = pin
        
        if self.save_config_to_file():
            messagebox.showinfo("成功", "配置已保存")
            self.log("✅ 配置保存成功")
            # 更新主页状态显示
            self.update_status_display()
        else:
            messagebox.showerror("错误", "配置保存失败")
            
    def generate_email(self):
        if not self.config.get('email_domain'):
            messagebox.showerror("错误", "请先在配置页面保存配置")
            return

        chars = string.ascii_letters + string.digits
        random_str = ''.join(random.choice(chars) for _ in range(12))
        email = f"{random_str}@{self.config['email_domain']}"

        self.root.clipboard_clear()
        self.root.clipboard_append(email)

        # 在邮箱显示区域显示生成的邮箱
        self.email_display.config(state='normal')
        self.email_display.delete(1.0, 'end')
        self.email_display.insert(1.0, f"生成的邮箱: {email}\n已复制到剪贴板")
        self.email_display.config(state='disabled')

        self.log(f"📧 生成邮箱: {email}")
        self.log("📋 邮箱已复制到剪贴板")
        messagebox.showinfo("成功", f"邮箱已生成并复制:\n{email}")

        
    def get_code(self):
        def get_code_thread():
            try:
                if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
                    messagebox.showerror("错误", "请先在配置页面保存配置")
                    return

                self.log("🔍 开始获取验证码...")

                email_api = EmailAPI(self.config)
                emails = email_api.get_emails()

                if emails:
                    code = emails[0]['verification_code']
                    self.root.clipboard_clear()
                    self.root.clipboard_append(code)

                    # 在验证码显示区域显示验证码
                    self.code_display.config(state='normal')
                    self.code_display.delete(1.0, 'end')
                    self.code_display.insert(1.0, f"验证码: {code}\n已复制到剪贴板")
                    self.code_display.config(state='disabled')

                    self.log(f"✅ 找到验证码: {code}")
                    self.log("📋 验证码已复制到剪贴板")
                    messagebox.showinfo("成功", f"验证码: {code}\n已复制到剪贴板")
                else:
                    self.log("❌ 未找到验证码")
                    self.code_display.config(state='normal')
                    self.code_display.delete(1.0, 'end')
                    self.code_display.insert(1.0, "未找到验证码，请稍后重试")
                    self.code_display.config(state='disabled')
                    messagebox.showinfo("提示", "未找到验证码")

            except Exception as e:
                self.log(f"❌ 获取验证码失败: {e}")
                self.code_display.config(state='normal')
                self.code_display.delete(1.0, 'end')
                self.code_display.insert(1.0, f"获取失败: {e}")
                self.code_display.config(state='disabled')
                messagebox.showerror("错误", str(e))

        threading.Thread(target=get_code_thread, daemon=True).start()

    def close_browser(self):
        """关闭自动化浏览器"""
        if self.driver:
            try:
                self.driver.quit()
                self.driver = None
                self.log("🔒 已关闭自动化浏览器")
                messagebox.showinfo("成功", "已关闭自动化浏览器")
            except Exception as e:
                self.log(f"❌ 关闭浏览器失败: {e}")
                messagebox.showerror("错误", f"关闭浏览器失败: {e}")
        else:
            messagebox.showinfo("提示", "没有运行中的自动化浏览器")

    def on_closing(self):
        """程序关闭时的清理工作"""
        if self.driver:
            try:
                self.driver.quit()
            except:
                pass
        self.root.destroy()

    def run(self):
        self.log("🎉 Augment续杯工具启动成功")
        self.log("💡 功能说明:")
        self.log("   🎯 自动注册: 一键生成邮箱并打开注册页面")
        self.log("   🔑 手动登录: 打开登录页面进行手动登录")
        self.log("   📧 邮箱管理: 生成邮箱和获取验证码")
        self.log("   ⚙️ 配置设置: 设置域名和TempMail.Plus信息")
        self.log("")
        self.log("📋 使用步骤:")
        self.log("   1. 先在'配置设置'页面配置您的信息")
        self.log("   2. 在主页点击'自动注册'或'手动登录'")
        self.log("   3. 在'邮箱管理'页面获取验证码")

        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.root.mainloop()

if __name__ == "__main__":
    app = AugmentTool()
    app.run()
