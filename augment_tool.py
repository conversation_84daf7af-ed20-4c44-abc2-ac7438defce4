#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 桌面版
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import os
import requests
import re
import random
import string
import webbrowser
import tempfile
import shutil
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

class EmailAPI:
    def __init__(self, config):
        self.config = config
    
    def extract_verification_code(self, content):
        patterns = [
            r'验证码[：:\s]*([A-Z0-9]{4,8})',
            r'verification code[：:\s]*([A-Z0-9]{4,8})',
            r'code[：:\s]*([A-Z0-9]{4,8})',
            r'([A-Z0-9]{6})',
            r'([0-9]{4,8})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)
        return None

    def get_emails(self):
        if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
            raise Exception('需要TempMail.Plus邮箱地址和PIN码')
        
        temp_mail_address = self.config['temp_mail_address']
        username, domain = temp_mail_address.split('@')
        
        endpoints = [
            f'https://tempmail.plus/api/v1/inbox/{username}',
            f'https://tempmail.plus/api/inbox/{username}',
            f'https://tempmail.plus/inbox/{username}',
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, headers={
                    'Content-Type': 'application/json',
                    'X-Pin-Code': self.config['pin_code'],
                    'Authorization': f'PIN {self.config["pin_code"]}',
                }, timeout=10)
                
                if response.ok:
                    data = response.json()
                    emails = data.get('emails', data.get('messages', data.get('inbox', [])))
                    
                    if isinstance(emails, list):
                        results = []
                        for email in emails[:5]:
                            content = email.get('body', email.get('content', email.get('text', '')))
                            code = self.extract_verification_code(content)
                            if code:
                                results.append({
                                    'verification_code': code,
                                    'subject': email.get('subject', ''),
                                    'content': content
                                })
                        return results
            except Exception as e:
                continue
        
        raise Exception('无法获取邮件')

class AugmentTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment续杯工具")
        self.root.geometry("900x600")
        self.root.configure(bg='#f0f0f0')

        self.config_file = "config.json"
        self.config = self.load_config()

        # 当前选中的页面
        self.current_page = None

        # 浏览器相关
        self.driver = None
        self.temp_profile_dir = None

        self.create_widgets()
        self.load_saved_config()
        self.show_page("config")  # 默认显示配置页面
        
    def create_widgets(self):
        # 创建主容器
        main_container = tk.Frame(self.root, bg='#f0f0f0')
        main_container.pack(fill="both", expand=True)

        # 左侧导航栏
        self.create_sidebar(main_container)

        # 右侧内容区域
        self.create_content_area(main_container)

    def create_sidebar(self, parent):
        # 左侧导航栏
        sidebar = tk.Frame(parent, bg='#2c3e50', width=200)
        sidebar.pack(side="left", fill="y")
        sidebar.pack_propagate(False)

        # 标题
        title = tk.Label(sidebar, text="🚀 Augment续杯",
                        font=("Arial", 14, "bold"),
                        bg='#2c3e50', fg='white')
        title.pack(pady=20)

        # 导航按钮
        nav_buttons = [
            ("⚙️ 配置设置", "config"),
            ("📧 邮箱管理", "email"),
            ("🌐 浏览器助手", "browser"),
            ("📋 日志查看", "log")
        ]

        self.nav_buttons = {}
        for text, page_id in nav_buttons:
            btn = tk.Button(sidebar, text=text,
                           command=lambda p=page_id: self.show_page(p),
                           bg='#34495e', fg='white',
                           font=("Arial", 10),
                           relief='flat', bd=0,
                           width=20, height=2)
            btn.pack(pady=2, padx=10, fill="x")
            self.nav_buttons[page_id] = btn

    def create_content_area(self, parent):
        # 右侧内容区域
        self.content_area = tk.Frame(parent, bg='white')
        self.content_area.pack(side="right", fill="both", expand=True)

        # 创建各个页面
        self.create_config_page()
        self.create_email_page()
        self.create_browser_page()
        self.create_log_page()

    def create_config_page(self):
        self.config_page = tk.Frame(self.content_area, bg='white')

        # 页面标题
        title = tk.Label(self.config_page, text="⚙️ 配置设置",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 配置表单
        form_frame = tk.Frame(self.config_page, bg='white')
        form_frame.pack(pady=20, padx=40, fill="x")

        # 域名配置
        domain_frame = tk.Frame(form_frame, bg='white')
        domain_frame.pack(fill="x", pady=10)
        tk.Label(domain_frame, text="邮箱域名:", font=("Arial", 12), bg='white').pack(anchor="w")
        self.domain_entry = tk.Entry(domain_frame, font=("Arial", 11), width=40)
        self.domain_entry.pack(fill="x", pady=5)
        tk.Label(domain_frame, text="例如: example.com", fg="gray", bg='white').pack(anchor="w")

        # TempMail.Plus配置
        tempmail_frame = tk.Frame(form_frame, bg='white')
        tempmail_frame.pack(fill="x", pady=10)
        tk.Label(tempmail_frame, text="TempMail.Plus邮箱:", font=("Arial", 12), bg='white').pack(anchor="w")
        self.tempmail_entry = tk.Entry(tempmail_frame, font=("Arial", 11), width=40)
        self.tempmail_entry.pack(fill="x", pady=5)
        tk.Label(tempmail_frame, text="例如: <EMAIL>", fg="gray", bg='white').pack(anchor="w")

        # PIN码配置
        pin_frame = tk.Frame(form_frame, bg='white')
        pin_frame.pack(fill="x", pady=10)
        tk.Label(pin_frame, text="PIN码:", font=("Arial", 12), bg='white').pack(anchor="w")
        self.pin_entry = tk.Entry(pin_frame, show="*", font=("Arial", 11), width=20)
        self.pin_entry.pack(anchor="w", pady=5)

        # 保存按钮
        save_btn = tk.Button(form_frame, text="💾 保存配置", command=self.save_config,
                            bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                            width=15, height=2)
        save_btn.pack(pady=20)

    def create_email_page(self):
        self.email_page = tk.Frame(self.content_area, bg='white')

        title = tk.Label(self.email_page, text="📧 邮箱管理",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 邮箱生成区域
        gen_frame = tk.Frame(self.email_page, bg='white')
        gen_frame.pack(pady=20, padx=40, fill="x")

        tk.Label(gen_frame, text="随机邮箱生成", font=("Arial", 14, "bold"), bg='white').pack(anchor="w", pady=10)

        gen_btn = tk.Button(gen_frame, text="📧 生成随机邮箱", command=self.generate_email,
                           bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                           width=20, height=2)
        gen_btn.pack(pady=10)

        # 显示生成的邮箱
        self.email_display = tk.Text(gen_frame, height=3, font=("Arial", 11), state='disabled')
        self.email_display.pack(fill="x", pady=10)

        # 验证码获取区域
        code_frame = tk.Frame(self.email_page, bg='white')
        code_frame.pack(pady=20, padx=40, fill="x")

        tk.Label(code_frame, text="验证码获取", font=("Arial", 14, "bold"), bg='white').pack(anchor="w", pady=10)

        code_btn = tk.Button(code_frame, text="🔍 获取验证码", command=self.get_code,
                            bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                            width=20, height=2)
        code_btn.pack(pady=10)

        # 显示验证码
        self.code_display = tk.Text(code_frame, height=3, font=("Arial", 11), state='disabled')
        self.code_display.pack(fill="x", pady=10)

    def create_browser_page(self):
        self.browser_page = tk.Frame(self.content_area, bg='white')

        title = tk.Label(self.browser_page, text="🌐 浏览器助手",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 浏览器控制区域
        browser_frame = tk.Frame(self.browser_page, bg='white')
        browser_frame.pack(pady=20, padx=40, fill="x")

        # 说明文字
        info_text = """
浏览器功能：

• 启动全新Chrome浏览器（无历史记录、无缓存）
• 自动打开Augment官网
• 自动点击"Sign In"按钮
• 支持手动操作和自动化操作
        """

        tk.Label(browser_frame, text=info_text, font=("Arial", 11),
                bg='white', justify='left').pack(anchor="w", pady=10)

        # 按钮区域
        btn_frame = tk.Frame(browser_frame, bg='white')
        btn_frame.pack(pady=20)

        # 启动浏览器按钮
        self.start_browser_btn = tk.Button(btn_frame, text="🚀 启动全新Chrome",
                                          command=self.start_fresh_browser,
                                          bg="#27ae60", fg="white",
                                          font=("Arial", 12, "bold"),
                                          width=18, height=2)
        self.start_browser_btn.pack(side="left", padx=10)

        # 自动登录按钮
        self.auto_login_btn = tk.Button(btn_frame, text="🤖 自动打开登录页",
                                       command=self.auto_open_login,
                                       bg="#3498db", fg="white",
                                       font=("Arial", 12, "bold"),
                                       width=18, height=2,
                                       state='disabled')
        self.auto_login_btn.pack(side="left", padx=10)

        # 关闭浏览器按钮
        self.close_browser_btn = tk.Button(btn_frame, text="❌ 关闭浏览器",
                                          command=self.close_browser,
                                          bg="#e74c3c", fg="white",
                                          font=("Arial", 12, "bold"),
                                          width=18, height=2,
                                          state='disabled')
        self.close_browser_btn.pack(side="left", padx=10)

        # 状态显示
        self.browser_status = tk.Label(browser_frame, text="浏览器状态: 未启动",
                                      font=("Arial", 11), bg='white', fg='gray')
        self.browser_status.pack(pady=20)

    def create_log_page(self):
        self.log_page = tk.Frame(self.content_area, bg='white')

        title = tk.Label(self.log_page, text="📋 日志查看",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 日志区域
        log_frame = tk.Frame(self.log_page, bg='white')
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)

        self.log_text = scrolledtext.ScrolledText(log_frame, font=("Consolas", 10))
        self.log_text.pack(fill="both", expand=True)

        # 清除按钮
        clear_btn = tk.Button(log_frame, text="🗑️ 清除日志", command=self.clear_log,
                             bg="#95a5a6", fg="white", font=("Arial", 10))
        clear_btn.pack(pady=10)

    def show_page(self, page_id):
        # 隐藏所有页面
        for page in [self.config_page, self.email_page, self.browser_page, self.log_page]:
            page.pack_forget()

        # 重置所有按钮颜色
        for btn in self.nav_buttons.values():
            btn.configure(bg='#34495e')

        # 显示选中的页面并高亮按钮
        if page_id == "config":
            self.config_page.pack(fill="both", expand=True)
            self.nav_buttons["config"].configure(bg='#3498db')
        elif page_id == "email":
            self.email_page.pack(fill="both", expand=True)
            self.nav_buttons["email"].configure(bg='#3498db')
        elif page_id == "browser":
            self.browser_page.pack(fill="both", expand=True)
            self.nav_buttons["browser"].configure(bg='#3498db')
        elif page_id == "log":
            self.log_page.pack(fill="both", expand=True)
            self.nav_buttons["log"].configure(bg='#3498db')

        self.current_page = page_id
        
    def log(self, message):
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert("end", f"[{timestamp}] {message}\n")
        self.log_text.see("end")
        self.root.update()
        
    def clear_log(self):
        self.log_text.delete("1.0", "end")
        
    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
        
    def save_config_to_file(self):
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
            
    def load_saved_config(self):
        if self.config.get('email_domain'):
            self.domain_entry.insert(0, self.config['email_domain'])
        if self.config.get('temp_mail_address'):
            self.tempmail_entry.insert(0, self.config['temp_mail_address'])
        if self.config.get('pin_code'):
            self.pin_entry.insert(0, self.config['pin_code'])
            
    def save_config(self):
        domain = self.domain_entry.get().strip()
        tempmail = self.tempmail_entry.get().strip()
        pin = self.pin_entry.get().strip()
        
        if not domain:
            messagebox.showerror("错误", "请输入邮箱域名")
            return
        if not tempmail:
            messagebox.showerror("错误", "请输入TempMail.Plus邮箱")
            return
        if not pin:
            messagebox.showerror("错误", "请输入PIN码")
            return
            
        self.config['email_domain'] = domain
        self.config['temp_mail_address'] = tempmail
        self.config['pin_code'] = pin
        
        if self.save_config_to_file():
            messagebox.showinfo("成功", "配置已保存")
            self.log("✅ 配置保存成功")
        else:
            messagebox.showerror("错误", "配置保存失败")
            
    def generate_email(self):
        if not self.config.get('email_domain'):
            messagebox.showerror("错误", "请先在配置页面保存配置")
            return

        chars = string.ascii_letters + string.digits
        random_str = ''.join(random.choice(chars) for _ in range(12))
        email = f"{random_str}@{self.config['email_domain']}"

        self.root.clipboard_clear()
        self.root.clipboard_append(email)

        # 在邮箱显示区域显示生成的邮箱
        self.email_display.config(state='normal')
        self.email_display.delete(1.0, 'end')
        self.email_display.insert(1.0, f"生成的邮箱: {email}\n已复制到剪贴板")
        self.email_display.config(state='disabled')

        self.log(f"📧 生成邮箱: {email}")
        self.log("📋 邮箱已复制到剪贴板")
        messagebox.showinfo("成功", f"邮箱已生成并复制:\n{email}")

        
    def get_code(self):
        def get_code_thread():
            try:
                if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
                    messagebox.showerror("错误", "请先在配置页面保存配置")
                    return

                self.log("🔍 开始获取验证码...")

                email_api = EmailAPI(self.config)
                emails = email_api.get_emails()

                if emails:
                    code = emails[0]['verification_code']
                    self.root.clipboard_clear()
                    self.root.clipboard_append(code)

                    # 在验证码显示区域显示验证码
                    self.code_display.config(state='normal')
                    self.code_display.delete(1.0, 'end')
                    self.code_display.insert(1.0, f"验证码: {code}\n已复制到剪贴板")
                    self.code_display.config(state='disabled')

                    self.log(f"✅ 找到验证码: {code}")
                    self.log("📋 验证码已复制到剪贴板")
                    messagebox.showinfo("成功", f"验证码: {code}\n已复制到剪贴板")
                else:
                    self.log("❌ 未找到验证码")
                    self.code_display.config(state='normal')
                    self.code_display.delete(1.0, 'end')
                    self.code_display.insert(1.0, "未找到验证码，请稍后重试")
                    self.code_display.config(state='disabled')
                    messagebox.showinfo("提示", "未找到验证码")

            except Exception as e:
                self.log(f"❌ 获取验证码失败: {e}")
                self.code_display.config(state='normal')
                self.code_display.delete(1.0, 'end')
                self.code_display.insert(1.0, f"获取失败: {e}")
                self.code_display.config(state='disabled')
                messagebox.showerror("错误", str(e))

        threading.Thread(target=get_code_thread, daemon=True).start()

    def start_fresh_browser(self):
        """启动全新的Chrome浏览器"""
        def start_browser_thread():
            try:
                self.log("🚀 正在启动全新Chrome浏览器...")
                self.browser_status.config(text="浏览器状态: 启动中...", fg='orange')

                # 创建临时用户配置文件目录
                self.temp_profile_dir = tempfile.mkdtemp(prefix="chrome_temp_")
                self.log(f"📁 创建临时配置文件: {self.temp_profile_dir}")

                # 配置Chrome选项
                chrome_options = Options()
                chrome_options.add_argument(f"--user-data-dir={self.temp_profile_dir}")
                chrome_options.add_argument("--no-first-run")
                chrome_options.add_argument("--no-default-browser-check")
                chrome_options.add_argument("--disable-default-apps")
                chrome_options.add_argument("--disable-popup-blocking")
                chrome_options.add_argument("--disable-translate")
                chrome_options.add_argument("--disable-background-timer-throttling")
                chrome_options.add_argument("--disable-renderer-backgrounding")
                chrome_options.add_argument("--disable-backgrounding-occluded-windows")
                chrome_options.add_argument("--disable-ipc-flooding-protection")
                chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
                chrome_options.add_experimental_option('useAutomationExtension', False)

                # 启动浏览器
                service = Service(ChromeDriverManager().install())
                self.driver = webdriver.Chrome(service=service, options=chrome_options)

                # 隐藏自动化特征
                self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

                # 最大化窗口
                self.driver.maximize_window()

                self.log("✅ Chrome浏览器启动成功")
                self.browser_status.config(text="浏览器状态: 已启动", fg='green')

                # 更新按钮状态
                self.start_browser_btn.config(state='disabled')
                self.auto_login_btn.config(state='normal')
                self.close_browser_btn.config(state='normal')

            except Exception as e:
                self.log(f"❌ 启动浏览器失败: {e}")
                self.browser_status.config(text="浏览器状态: 启动失败", fg='red')
                messagebox.showerror("错误", f"启动浏览器失败:\n{e}")

        threading.Thread(target=start_browser_thread, daemon=True).start()

    def auto_open_login(self):
        """自动打开登录页面并点击Sign In"""
        def auto_login_thread():
            try:
                if not self.driver:
                    messagebox.showerror("错误", "请先启动浏览器")
                    return

                self.log("🌐 正在打开Augment官网...")

                # 打开Augment官网
                self.driver.get("https://www.augmentcode.com/")

                # 等待页面加载
                wait = WebDriverWait(self.driver, 10)

                # 查找并点击Sign In按钮
                self.log("🔍 查找Sign In按钮...")

                # 尝试多种可能的选择器
                sign_in_selectors = [
                    "//a[contains(text(), 'Sign In')]",
                    "//a[contains(text(), 'sign in')]",
                    "//a[contains(text(), 'Sign in')]",
                    "//a[contains(text(), 'LOGIN')]",
                    "//a[contains(text(), 'Login')]",
                    "//button[contains(text(), 'Sign In')]",
                    "//button[contains(text(), 'sign in')]",
                    "//button[contains(text(), 'Sign in')]",
                    "//a[contains(@class, 'sign')]",
                    "//a[contains(@class, 'login')]"
                ]

                sign_in_button = None
                for selector in sign_in_selectors:
                    try:
                        sign_in_button = wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                        break
                    except:
                        continue

                if sign_in_button:
                    self.log("✅ 找到Sign In按钮，正在点击...")
                    sign_in_button.click()
                    self.log("🎉 已自动点击Sign In按钮")

                    # 等待跳转到登录页面
                    time.sleep(2)
                    current_url = self.driver.current_url
                    self.log(f"📍 当前页面: {current_url}")

                else:
                    self.log("⚠️ 未找到Sign In按钮，请手动点击")
                    messagebox.showwarning("提示", "未找到Sign In按钮，请手动点击网站右上角的登录按钮")

            except Exception as e:
                self.log(f"❌ 自动登录失败: {e}")
                messagebox.showerror("错误", f"自动登录失败:\n{e}")

        threading.Thread(target=auto_login_thread, daemon=True).start()

    def close_browser(self):
        """关闭浏览器并清理临时文件"""
        try:
            if self.driver:
                self.driver.quit()
                self.driver = None
                self.log("🔒 浏览器已关闭")

            # 清理临时配置文件
            if self.temp_profile_dir and os.path.exists(self.temp_profile_dir):
                try:
                    shutil.rmtree(self.temp_profile_dir)
                    self.log("🗑️ 临时配置文件已清理")
                except:
                    self.log("⚠️ 临时配置文件清理失败（可能被占用）")

            self.temp_profile_dir = None

            # 更新界面状态
            self.browser_status.config(text="浏览器状态: 未启动", fg='gray')
            self.start_browser_btn.config(state='normal')
            self.auto_login_btn.config(state='disabled')
            self.close_browser_btn.config(state='disabled')

        except Exception as e:
            self.log(f"❌ 关闭浏览器时出错: {e}")

    def open_login(self):
        """在默认浏览器中打开Augment官网"""
        url = "https://www.augmentcode.com/"
        webbrowser.open(url)
        self.log("🌐 已在默认浏览器中打开Augment官网")
        self.log("💡 请点击网站右上角的 'Sign In' 按钮登录")
        
    def on_closing(self):
        """程序关闭时的清理工作"""
        try:
            self.close_browser()
        except:
            pass
        self.root.destroy()

    def run(self):
        self.log("🎉 Augment续杯工具启动")
        self.log("💡 使用步骤:")
        self.log("   1. 在配置页面设置域名和TempMail.Plus信息")
        self.log("   2. 在邮箱管理页面生成邮箱和获取验证码")
        self.log("   3. 在浏览器助手页面启动全新Chrome浏览器")
        self.log("   4. 使用生成的邮箱登录并获取验证码")

        # 设置关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        self.root.mainloop()

if __name__ == "__main__":
    app = AugmentTool()
    app.run()
