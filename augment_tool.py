#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 桌面版
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import os
import requests
import re
import random
import string
import webbrowser

class EmailAPI:
    def __init__(self, config):
        self.config = config
    
    def extract_verification_code(self, content):
        patterns = [
            r'验证码[：:\s]*([A-Z0-9]{4,8})',
            r'verification code[：:\s]*([A-Z0-9]{4,8})',
            r'code[：:\s]*([A-Z0-9]{4,8})',
            r'([A-Z0-9]{6})',
            r'([0-9]{4,8})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)
        return None

    def get_emails(self):
        if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
            raise Exception('需要TempMail.Plus邮箱地址和PIN码')
        
        temp_mail_address = self.config['temp_mail_address']
        username, domain = temp_mail_address.split('@')
        
        endpoints = [
            f'https://tempmail.plus/api/v1/inbox/{username}',
            f'https://tempmail.plus/api/inbox/{username}',
            f'https://tempmail.plus/inbox/{username}',
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, headers={
                    'Content-Type': 'application/json',
                    'X-Pin-Code': self.config['pin_code'],
                    'Authorization': f'PIN {self.config["pin_code"]}',
                }, timeout=10)
                
                if response.ok:
                    data = response.json()
                    emails = data.get('emails', data.get('messages', data.get('inbox', [])))
                    
                    if isinstance(emails, list):
                        results = []
                        for email in emails[:5]:
                            content = email.get('body', email.get('content', email.get('text', '')))
                            code = self.extract_verification_code(content)
                            if code:
                                results.append({
                                    'verification_code': code,
                                    'subject': email.get('subject', ''),
                                    'content': content
                                })
                        return results
            except Exception as e:
                continue
        
        raise Exception('无法获取邮件')

class AugmentTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment续杯工具")
        self.root.geometry("600x500")
        
        self.config_file = "config.json"
        self.config = self.load_config()
        
        self.create_widgets()
        self.load_saved_config()
        
    def create_widgets(self):
        # 标题
        title = tk.Label(self.root, text="🚀 Augment续杯工具", font=("Arial", 16, "bold"))
        title.pack(pady=10)
        
        # 配置区域
        config_frame = ttk.LabelFrame(self.root, text="配置", padding=10)
        config_frame.pack(fill="x", padx=20, pady=5)
        
        # 域名
        tk.Label(config_frame, text="邮箱域名:").grid(row=0, column=0, sticky="w", pady=2)
        self.domain_entry = tk.Entry(config_frame, width=30)
        self.domain_entry.grid(row=0, column=1, padx=10, pady=2)
        tk.Label(config_frame, text="例如: example.com", fg="gray").grid(row=0, column=2, sticky="w")
        
        # TempMail.Plus邮箱
        tk.Label(config_frame, text="TempMail.Plus邮箱:").grid(row=1, column=0, sticky="w", pady=2)
        self.tempmail_entry = tk.Entry(config_frame, width=30)
        self.tempmail_entry.grid(row=1, column=1, padx=10, pady=2)
        tk.Label(config_frame, text="例如: <EMAIL>", fg="gray").grid(row=1, column=2, sticky="w")
        
        # PIN码
        tk.Label(config_frame, text="PIN码:").grid(row=2, column=0, sticky="w", pady=2)
        self.pin_entry = tk.Entry(config_frame, show="*", width=20)
        self.pin_entry.grid(row=2, column=1, padx=10, pady=2, sticky="w")
        
        # 按钮区域
        btn_frame = tk.Frame(self.root)
        btn_frame.pack(pady=20)
        
        tk.Button(btn_frame, text="💾 保存配置", command=self.save_config, 
                 bg="#4CAF50", fg="white", width=12).pack(side="left", padx=5)
        tk.Button(btn_frame, text="📧 生成邮箱", command=self.generate_email, 
                 bg="#2196F3", fg="white", width=12).pack(side="left", padx=5)
        tk.Button(btn_frame, text="🌐 打开登录页", command=self.open_login, 
                 bg="#FF9800", fg="white", width=12).pack(side="left", padx=5)
        tk.Button(btn_frame, text="🔍 获取验证码", command=self.get_code, 
                 bg="#F44336", fg="white", width=12).pack(side="left", padx=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(self.root, text="日志", padding=10)
        log_frame.pack(fill="both", expand=True, padx=20, pady=5)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15)
        self.log_text.pack(fill="both", expand=True)
        
        tk.Button(log_frame, text="清除", command=self.clear_log).pack(pady=5)
        
    def log(self, message):
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert("end", f"[{timestamp}] {message}\n")
        self.log_text.see("end")
        self.root.update()
        
    def clear_log(self):
        self.log_text.delete("1.0", "end")
        
    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
        
    def save_config_to_file(self):
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
            
    def load_saved_config(self):
        if self.config.get('email_domain'):
            self.domain_entry.insert(0, self.config['email_domain'])
        if self.config.get('temp_mail_address'):
            self.tempmail_entry.insert(0, self.config['temp_mail_address'])
        if self.config.get('pin_code'):
            self.pin_entry.insert(0, self.config['pin_code'])
            
    def save_config(self):
        domain = self.domain_entry.get().strip()
        tempmail = self.tempmail_entry.get().strip()
        pin = self.pin_entry.get().strip()
        
        if not domain:
            messagebox.showerror("错误", "请输入邮箱域名")
            return
        if not tempmail:
            messagebox.showerror("错误", "请输入TempMail.Plus邮箱")
            return
        if not pin:
            messagebox.showerror("错误", "请输入PIN码")
            return
            
        self.config['email_domain'] = domain
        self.config['temp_mail_address'] = tempmail
        self.config['pin_code'] = pin
        
        if self.save_config_to_file():
            messagebox.showinfo("成功", "配置已保存")
            self.log("✅ 配置保存成功")
        else:
            messagebox.showerror("错误", "配置保存失败")
            
    def generate_email(self):
        if not self.config.get('email_domain'):
            messagebox.showerror("错误", "请先保存配置")
            return
            
        chars = string.ascii_letters + string.digits
        random_str = ''.join(random.choice(chars) for _ in range(12))
        email = f"{random_str}@{self.config['email_domain']}"
        
        self.root.clipboard_clear()
        self.root.clipboard_append(email)
        
        self.log(f"📧 生成邮箱: {email}")
        self.log("📋 邮箱已复制到剪贴板")
        messagebox.showinfo("成功", f"邮箱已生成并复制:\n{email}")
        
    def open_login(self):
        url = "https://login.augmentcode.com/u/login/identifier"
        webbrowser.open(url)
        self.log("🌐 已打开Augment登录页面")
        
    def get_code(self):
        def get_code_thread():
            try:
                if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
                    messagebox.showerror("错误", "请先保存配置")
                    return
                    
                self.log("🔍 开始获取验证码...")
                
                email_api = EmailAPI(self.config)
                emails = email_api.get_emails()
                
                if emails:
                    code = emails[0]['verification_code']
                    self.root.clipboard_clear()
                    self.root.clipboard_append(code)
                    
                    self.log(f"✅ 找到验证码: {code}")
                    self.log("📋 验证码已复制到剪贴板")
                    messagebox.showinfo("成功", f"验证码: {code}\n已复制到剪贴板")
                else:
                    self.log("❌ 未找到验证码")
                    messagebox.showinfo("提示", "未找到验证码")
                    
            except Exception as e:
                self.log(f"❌ 获取验证码失败: {e}")
                messagebox.showerror("错误", str(e))
                
        threading.Thread(target=get_code_thread, daemon=True).start()
        
    def run(self):
        self.log("🎉 Augment续杯工具启动")
        self.log("💡 使用步骤:")
        self.log("   1. 配置域名和TempMail.Plus信息")
        self.log("   2. 保存配置")
        self.log("   3. 生成邮箱")
        self.log("   4. 打开登录页面手动登录")
        self.log("   5. 获取验证码")
        self.root.mainloop()

if __name__ == "__main__":
    app = AugmentTool()
    app.run()
