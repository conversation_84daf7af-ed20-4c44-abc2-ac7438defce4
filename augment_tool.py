#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment续杯工具 - 桌面版
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import json
import os
import requests
import re
import random
import string
import webbrowser

class EmailAPI:
    def __init__(self, config):
        self.config = config
    
    def extract_verification_code(self, content):
        patterns = [
            r'验证码[：:\s]*([A-Z0-9]{4,8})',
            r'verification code[：:\s]*([A-Z0-9]{4,8})',
            r'code[：:\s]*([A-Z0-9]{4,8})',
            r'([A-Z0-9]{6})',
            r'([0-9]{4,8})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1)
        return None

    def get_emails(self):
        if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
            raise Exception('需要TempMail.Plus邮箱地址和PIN码')
        
        temp_mail_address = self.config['temp_mail_address']
        username, domain = temp_mail_address.split('@')
        
        endpoints = [
            f'https://tempmail.plus/api/v1/inbox/{username}',
            f'https://tempmail.plus/api/inbox/{username}',
            f'https://tempmail.plus/inbox/{username}',
        ]
        
        for endpoint in endpoints:
            try:
                response = requests.get(endpoint, headers={
                    'Content-Type': 'application/json',
                    'X-Pin-Code': self.config['pin_code'],
                    'Authorization': f'PIN {self.config["pin_code"]}',
                }, timeout=10)
                
                if response.ok:
                    data = response.json()
                    emails = data.get('emails', data.get('messages', data.get('inbox', [])))
                    
                    if isinstance(emails, list):
                        results = []
                        for email in emails[:5]:
                            content = email.get('body', email.get('content', email.get('text', '')))
                            code = self.extract_verification_code(content)
                            if code:
                                results.append({
                                    'verification_code': code,
                                    'subject': email.get('subject', ''),
                                    'content': content
                                })
                        return results
            except Exception as e:
                continue
        
        raise Exception('无法获取邮件')

class AugmentTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Augment续杯工具")
        self.root.geometry("900x600")
        self.root.configure(bg='#f0f0f0')

        self.config_file = "config.json"
        self.config = self.load_config()

        # 当前选中的页面
        self.current_page = None

        self.create_widgets()
        self.load_saved_config()
        self.show_page("config")  # 默认显示配置页面
        
    def create_widgets(self):
        # 创建主容器
        main_container = tk.Frame(self.root, bg='#f0f0f0')
        main_container.pack(fill="both", expand=True)

        # 左侧导航栏
        self.create_sidebar(main_container)

        # 右侧内容区域
        self.create_content_area(main_container)

    def create_sidebar(self, parent):
        # 左侧导航栏
        sidebar = tk.Frame(parent, bg='#2c3e50', width=200)
        sidebar.pack(side="left", fill="y")
        sidebar.pack_propagate(False)

        # 标题
        title = tk.Label(sidebar, text="🚀 Augment续杯",
                        font=("Arial", 14, "bold"),
                        bg='#2c3e50', fg='white')
        title.pack(pady=20)

        # 导航按钮
        nav_buttons = [
            ("⚙️ 配置设置", "config"),
            ("📧 邮箱管理", "email"),
            ("🌐 登录助手", "login"),
            ("📋 日志查看", "log")
        ]

        self.nav_buttons = {}
        for text, page_id in nav_buttons:
            btn = tk.Button(sidebar, text=text,
                           command=lambda p=page_id: self.show_page(p),
                           bg='#34495e', fg='white',
                           font=("Arial", 10),
                           relief='flat', bd=0,
                           width=20, height=2)
            btn.pack(pady=2, padx=10, fill="x")
            self.nav_buttons[page_id] = btn

    def create_content_area(self, parent):
        # 右侧内容区域
        self.content_area = tk.Frame(parent, bg='white')
        self.content_area.pack(side="right", fill="both", expand=True)

        # 创建各个页面
        self.create_config_page()
        self.create_email_page()
        self.create_login_page()
        self.create_log_page()

    def create_config_page(self):
        self.config_page = tk.Frame(self.content_area, bg='white')

        # 页面标题
        title = tk.Label(self.config_page, text="⚙️ 配置设置",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 配置表单
        form_frame = tk.Frame(self.config_page, bg='white')
        form_frame.pack(pady=20, padx=40, fill="x")

        # 域名配置
        domain_frame = tk.Frame(form_frame, bg='white')
        domain_frame.pack(fill="x", pady=10)
        tk.Label(domain_frame, text="邮箱域名:", font=("Arial", 12), bg='white').pack(anchor="w")
        self.domain_entry = tk.Entry(domain_frame, font=("Arial", 11), width=40)
        self.domain_entry.pack(fill="x", pady=5)
        tk.Label(domain_frame, text="例如: example.com", fg="gray", bg='white').pack(anchor="w")

        # TempMail.Plus配置
        tempmail_frame = tk.Frame(form_frame, bg='white')
        tempmail_frame.pack(fill="x", pady=10)
        tk.Label(tempmail_frame, text="TempMail.Plus邮箱:", font=("Arial", 12), bg='white').pack(anchor="w")
        self.tempmail_entry = tk.Entry(tempmail_frame, font=("Arial", 11), width=40)
        self.tempmail_entry.pack(fill="x", pady=5)
        tk.Label(tempmail_frame, text="例如: <EMAIL>", fg="gray", bg='white').pack(anchor="w")

        # PIN码配置
        pin_frame = tk.Frame(form_frame, bg='white')
        pin_frame.pack(fill="x", pady=10)
        tk.Label(pin_frame, text="PIN码:", font=("Arial", 12), bg='white').pack(anchor="w")
        self.pin_entry = tk.Entry(pin_frame, show="*", font=("Arial", 11), width=20)
        self.pin_entry.pack(anchor="w", pady=5)

        # 保存按钮
        save_btn = tk.Button(form_frame, text="💾 保存配置", command=self.save_config,
                            bg="#27ae60", fg="white", font=("Arial", 12, "bold"),
                            width=15, height=2)
        save_btn.pack(pady=20)

    def create_email_page(self):
        self.email_page = tk.Frame(self.content_area, bg='white')

        title = tk.Label(self.email_page, text="📧 邮箱管理",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 邮箱生成区域
        gen_frame = tk.Frame(self.email_page, bg='white')
        gen_frame.pack(pady=20, padx=40, fill="x")

        tk.Label(gen_frame, text="随机邮箱生成", font=("Arial", 14, "bold"), bg='white').pack(anchor="w", pady=10)

        gen_btn = tk.Button(gen_frame, text="📧 生成随机邮箱", command=self.generate_email,
                           bg="#3498db", fg="white", font=("Arial", 12, "bold"),
                           width=20, height=2)
        gen_btn.pack(pady=10)

        # 显示生成的邮箱
        self.email_display = tk.Text(gen_frame, height=3, font=("Arial", 11), state='disabled')
        self.email_display.pack(fill="x", pady=10)

        # 验证码获取区域
        code_frame = tk.Frame(self.email_page, bg='white')
        code_frame.pack(pady=20, padx=40, fill="x")

        tk.Label(code_frame, text="验证码获取", font=("Arial", 14, "bold"), bg='white').pack(anchor="w", pady=10)

        code_btn = tk.Button(code_frame, text="🔍 获取验证码", command=self.get_code,
                            bg="#e74c3c", fg="white", font=("Arial", 12, "bold"),
                            width=20, height=2)
        code_btn.pack(pady=10)

        # 显示验证码
        self.code_display = tk.Text(code_frame, height=3, font=("Arial", 11), state='disabled')
        self.code_display.pack(fill="x", pady=10)

    def create_login_page(self):
        self.login_page = tk.Frame(self.content_area, bg='white')

        title = tk.Label(self.login_page, text="🌐 登录助手",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 登录步骤说明
        steps_frame = tk.Frame(self.login_page, bg='white')
        steps_frame.pack(pady=20, padx=40, fill="x")

        steps_text = """
登录步骤：

1. 点击下方按钮打开Augment官网
2. 点击网站右上角的 "Sign In" 按钮
3. 使用生成的邮箱地址登录
4. 在验证码页面获取验证码
5. 输入验证码完成登录
        """

        tk.Label(steps_frame, text=steps_text, font=("Arial", 11),
                bg='white', justify='left').pack(anchor="w")

        # 打开登录页按钮
        login_btn = tk.Button(steps_frame, text="🌐 打开Augment官网", command=self.open_login,
                             bg="#f39c12", fg="white", font=("Arial", 12, "bold"),
                             width=20, height=2)
        login_btn.pack(pady=20)

    def create_log_page(self):
        self.log_page = tk.Frame(self.content_area, bg='white')

        title = tk.Label(self.log_page, text="📋 日志查看",
                        font=("Arial", 18, "bold"), bg='white')
        title.pack(pady=20)

        # 日志区域
        log_frame = tk.Frame(self.log_page, bg='white')
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)

        self.log_text = scrolledtext.ScrolledText(log_frame, font=("Consolas", 10))
        self.log_text.pack(fill="both", expand=True)

        # 清除按钮
        clear_btn = tk.Button(log_frame, text="🗑️ 清除日志", command=self.clear_log,
                             bg="#95a5a6", fg="white", font=("Arial", 10))
        clear_btn.pack(pady=10)

    def show_page(self, page_id):
        # 隐藏所有页面
        for page in [self.config_page, self.email_page, self.login_page, self.log_page]:
            page.pack_forget()

        # 重置所有按钮颜色
        for btn in self.nav_buttons.values():
            btn.configure(bg='#34495e')

        # 显示选中的页面并高亮按钮
        if page_id == "config":
            self.config_page.pack(fill="both", expand=True)
            self.nav_buttons["config"].configure(bg='#3498db')
        elif page_id == "email":
            self.email_page.pack(fill="both", expand=True)
            self.nav_buttons["email"].configure(bg='#3498db')
        elif page_id == "login":
            self.login_page.pack(fill="both", expand=True)
            self.nav_buttons["login"].configure(bg='#3498db')
        elif page_id == "log":
            self.log_page.pack(fill="both", expand=True)
            self.nav_buttons["log"].configure(bg='#3498db')

        self.current_page = page_id
        
    def log(self, message):
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.insert("end", f"[{timestamp}] {message}\n")
        self.log_text.see("end")
        self.root.update()
        
    def clear_log(self):
        self.log_text.delete("1.0", "end")
        
    def load_config(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
        
    def save_config_to_file(self):
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
            
    def load_saved_config(self):
        if self.config.get('email_domain'):
            self.domain_entry.insert(0, self.config['email_domain'])
        if self.config.get('temp_mail_address'):
            self.tempmail_entry.insert(0, self.config['temp_mail_address'])
        if self.config.get('pin_code'):
            self.pin_entry.insert(0, self.config['pin_code'])
            
    def save_config(self):
        domain = self.domain_entry.get().strip()
        tempmail = self.tempmail_entry.get().strip()
        pin = self.pin_entry.get().strip()
        
        if not domain:
            messagebox.showerror("错误", "请输入邮箱域名")
            return
        if not tempmail:
            messagebox.showerror("错误", "请输入TempMail.Plus邮箱")
            return
        if not pin:
            messagebox.showerror("错误", "请输入PIN码")
            return
            
        self.config['email_domain'] = domain
        self.config['temp_mail_address'] = tempmail
        self.config['pin_code'] = pin
        
        if self.save_config_to_file():
            messagebox.showinfo("成功", "配置已保存")
            self.log("✅ 配置保存成功")
        else:
            messagebox.showerror("错误", "配置保存失败")
            
    def generate_email(self):
        if not self.config.get('email_domain'):
            messagebox.showerror("错误", "请先在配置页面保存配置")
            return

        chars = string.ascii_letters + string.digits
        random_str = ''.join(random.choice(chars) for _ in range(12))
        email = f"{random_str}@{self.config['email_domain']}"

        self.root.clipboard_clear()
        self.root.clipboard_append(email)

        # 在邮箱显示区域显示生成的邮箱
        self.email_display.config(state='normal')
        self.email_display.delete(1.0, 'end')
        self.email_display.insert(1.0, f"生成的邮箱: {email}\n已复制到剪贴板")
        self.email_display.config(state='disabled')

        self.log(f"📧 生成邮箱: {email}")
        self.log("📋 邮箱已复制到剪贴板")
        messagebox.showinfo("成功", f"邮箱已生成并复制:\n{email}")

    def open_login(self):
        url = "https://www.augmentcode.com/"
        webbrowser.open(url)
        self.log("🌐 已打开Augment官网")
        self.log("💡 请点击网站右上角的 'Sign In' 按钮登录")
        
    def get_code(self):
        def get_code_thread():
            try:
                if not self.config.get('temp_mail_address') or not self.config.get('pin_code'):
                    messagebox.showerror("错误", "请先在配置页面保存配置")
                    return

                self.log("🔍 开始获取验证码...")

                email_api = EmailAPI(self.config)
                emails = email_api.get_emails()

                if emails:
                    code = emails[0]['verification_code']
                    self.root.clipboard_clear()
                    self.root.clipboard_append(code)

                    # 在验证码显示区域显示验证码
                    self.code_display.config(state='normal')
                    self.code_display.delete(1.0, 'end')
                    self.code_display.insert(1.0, f"验证码: {code}\n已复制到剪贴板")
                    self.code_display.config(state='disabled')

                    self.log(f"✅ 找到验证码: {code}")
                    self.log("📋 验证码已复制到剪贴板")
                    messagebox.showinfo("成功", f"验证码: {code}\n已复制到剪贴板")
                else:
                    self.log("❌ 未找到验证码")
                    self.code_display.config(state='normal')
                    self.code_display.delete(1.0, 'end')
                    self.code_display.insert(1.0, "未找到验证码，请稍后重试")
                    self.code_display.config(state='disabled')
                    messagebox.showinfo("提示", "未找到验证码")

            except Exception as e:
                self.log(f"❌ 获取验证码失败: {e}")
                self.code_display.config(state='normal')
                self.code_display.delete(1.0, 'end')
                self.code_display.insert(1.0, f"获取失败: {e}")
                self.code_display.config(state='disabled')
                messagebox.showerror("错误", str(e))

        threading.Thread(target=get_code_thread, daemon=True).start()
        
    def run(self):
        self.log("🎉 Augment续杯工具启动")
        self.log("💡 使用步骤:")
        self.log("   1. 配置域名和TempMail.Plus信息")
        self.log("   2. 保存配置")
        self.log("   3. 生成邮箱")
        self.log("   4. 打开登录页面手动登录")
        self.log("   5. 获取验证码")
        self.root.mainloop()

if __name__ == "__main__":
    app = AugmentTool()
    app.run()
